﻿using Bigpara.Service.Hangfire.Infrastructure.HttpClients;

namespace Bigpara.Service.Hangfire.Extensions;

public static class HttpClientExtensions
{
    public static IServiceCollection AddBigparaHttpClients(this IServiceCollection services,IConfiguration configuration)
    {
        services.AddScoped<ForeksHttpClient>();

        services.AddScoped<ForeksRealTimeHttpClient>();

        services.AddScoped<BigparaHttpClient>();

        services.AddHttpClient();

        services.AddHttpClient("BigparaSignalrService", c =>
        {
            c.BaseAddress = new Uri(configuration["Bigpara:Api"]);
        });

        services.AddHttpClient("ForeksFeedService", c =>
        {
            c.BaseAddress = new Uri("https://feed-definition.foreks.com");
        });

        services.AddHttpClient("ForeksRealTimeService", c =>
        {
            c.BaseAddress = new Uri("https://snapshot.foreks.com");
        });

        services.AddHttpClient("ForeksDelayedService", c =>
        {
            c.BaseAddress = new Uri("https://snapshot.foreks.com");
        });

        services.AddHttpClient("ForeksCloudService", c =>
        {
            c.BaseAddress = new Uri("https://cloud.foreks.com");
        });

        return services;
    }
}
