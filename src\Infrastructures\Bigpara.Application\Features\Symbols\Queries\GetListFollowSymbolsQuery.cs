﻿using Bigpara.Application.Features.Symbols.Queries.ViewModels;
using Bigpara.Cache.Interfaces;
using Bigpara.Application.Common;
using Bigpara.Application.Dtos.Matriks;
using MediatR;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Bigpara.Application.Contracts.Repositories.BigparaDB.UserSymbols;
using Bigpara.Application.Contracts.Repositories.Matriks.Sembol;
using Bigpara.Application.Contracts.Repositories.BigparaDB.Users;
using Bigpara.Application.Features.Symbols.Queries.Models;

namespace Bigpara.Application.Features.Symbols.Queries
{
  

    public class GetListFollowSymbolsQuery:IRequest<GetListFollowSymbolsQueryResponse>{
    }

    public class GetListFollowSymbolsQueryHandler : IRequestHandler<GetListFollowSymbolsQuery, GetListFollowSymbolsQueryResponse>
    {
        private readonly IRedisCacheService _redisCacheService;
        private readonly IUserSymbolRepository _userSymbolRepository;
        private readonly IUserRepository _userRepository;
        private readonly ISembolRepository _sembolRepository;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public GetListFollowSymbolsQueryHandler(ISembolRepository sembolRepository, IUserSymbolRepository userSymbolRepository, IHttpContextAccessor httpContextAccessor, IRedisCacheService redisCacheService, IUserRepository userRepository)
        {
            _sembolRepository = sembolRepository;
            _userSymbolRepository = userSymbolRepository;
            _httpContextAccessor = httpContextAccessor;
            _redisCacheService = redisCacheService;
            _userRepository = userRepository;
        }
        public async Task<GetListFollowSymbolsQueryResponse> Handle(GetListFollowSymbolsQuery request, CancellationToken cancellationToken)
        {
            var response = new GetListFollowSymbolsQueryResponse();
            
            var currentUser = _httpContextAccessor.HttpContext.User;
            if (currentUser == null || !currentUser.Identity.IsAuthenticated)
            {
                response.Errors.Add("Kullanıcı oturumu bulunamadı.");
                return response;
            }

            var email = currentUser.Claims.FirstOrDefault(c => c.Type == "email")?.Value;
            if (string.IsNullOrEmpty(email))
            {
                response.Errors.Add("Kullanıcı oturumu bulunamadı.");
                return response;
            }
            var user = await _userRepository.GetUserByEmail(email);

            if (user == null)
            {
                response.Errors.Add("Kullanıcı oturumu bulunamadı.");
                return response;
            }
            var data= await _userSymbolRepository.ListAsync(user.Id,0, int.MaxValue);
            foreach (var item in data)
            {
                var symbol = await _sembolRepository.GetSembollerBySembolId(item.SembolId);

                if(symbol != null)
                {
                    response.Data.Add(
                        new UserSembolItem
                        {
                            Id = item.Id,
                            Code = symbol.Sembol
                        }
                    );

                }

            }
            return response;
        }
    }
}
