﻿using Bigpara.Cache.Interfaces;

namespace Bigpara.Cache;

public static class CacheExtensions
{
    private static readonly object _lockObj = new();

    public static T Get<T>(this ICacheService cacheService, string key, Func<T> acquire) where T : class
    {
        return cacheService.Get(key, CacheKeys.CACHE_DATA_DURATION, acquire);
    }

    public static T Get<T>(this ICacheService cacheService, string key, int cacheTime, Func<T> acquire) where T : class
    {
        if (cacheTime < 0)
            return acquire();

        lock (_lockObj)
        {
            if (cacheService.IsSet(key))
                return cacheService.Get<T>(key);

            var result = acquire();
            if (result is not null)
                cacheService.Set(key, result, cacheTime);

            return result;
        }
    }


    public static Task<T> GetAsync<T>(this ICacheService cacheService, string key, Func<Task<T>> acquire) where T : class
    {
        return cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, acquire);
    }

    public static async Task<T> GetAsync<T>(this ICacheService cacheService, string key, int cacheTime, Func<Task<T>> acquire) where T : class
    {
        if (cacheTime < 0)
            return await acquire();

        if (cacheService.IsSet(key))
            return cacheService.Get<T>(key);

        var result = await acquire();

        if (result is not null)
            cacheService.Set(key, result, cacheTime);

        return result;
    }

    public static IList<T> GetRedisList<T>(this IRedisCacheService redisCacheService, string key, Func<IEnumerable<T>> acquire, short page, int pageSize, out int totalItemCount)
    {
        return redisCacheService.GetRedisList(key, acquire, CacheKeys.CACHE_DATA_DURATION, page, pageSize, out totalItemCount);
    }

    public static IList<T> GetRedisList<T>(this IRedisCacheService redisCacheService, string key, Func<IEnumerable<T>> acquire, int cacheTime, short page, int pageSize, out int totalItemCount)
    {
        lock (_lockObj)
        {
            IList<T> pagedData = new List<T>();
            totalItemCount = 0;

            if (redisCacheService.IsSet(key))
            {
                pagedData = redisCacheService.GetTypedList<T>(key, page, pageSize, out totalItemCount);

                if (!pagedData.Any())
                {
                    var acquiredData = acquire();
                    if (acquiredData != null && acquiredData.Any())
                    {
                        redisCacheService.AddTypedList(key, acquiredData, cacheTime);
                        pagedData = acquiredData.Skip((page - 1) * pageSize).Take(pageSize).ToList();
                    }
                }
            }
            else
            {
                var acquiredData = acquire();

                if (acquiredData != null && acquiredData.Any())
            {
                    totalItemCount = acquiredData.Count();
                    redisCacheService.AddTypedList(key, acquiredData, cacheTime);
                    pagedData = acquiredData.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            }

            }

            return pagedData;
        }
    }

    public static IList<T> GetRedisList<T>(this IRedisCacheService redisCacheService, string key, Func<IEnumerable<T>> acquire) where T : class
    {
        return redisCacheService.GetRedisList(key, acquire, CacheKeys.CACHE_DATA_DURATION);
    }

    public static IList<T> GetRedisList<T>(this IRedisCacheService redisCacheService, string key, Func<IEnumerable<T>> acquire, int cacheTime) where T : class
    {
        lock (_lockObj)
        {
            if (redisCacheService.IsSet(key))
            {
                var data = redisCacheService.GetTypedList<T>(key).ToList();
                if (!data.Any())
                {
                    var result = acquire();

                    var typedList = result as IList<T> ?? new List<T>();
                    if (result != null && result.Any())
                    {
                        redisCacheService.AddTypedList(key, typedList, cacheTime);
            }

                    return typedList;
                }

                return data;
            }
            else
            {
                var result = acquire();

                var typedList = result as IList<T> ?? result.ToList();
                if (result != null && result.Any())
                {
                    redisCacheService.AddTypedList(key, typedList, cacheTime);
        }

                return typedList;
            }
    }
    }

    public static IList<T> GetRedisList<T>(this IRedisCacheService redisCacheService, string key, Func<IEnumerable<T>> acquire, int cacheTime, int topN) where T : class
    {
        lock (_lockObj)
        {
            if (redisCacheService.IsSet(key))
            {
                int total;
                var data = redisCacheService.GetTypedList<T>(key, 1, topN, out total).ToList();
                if (!data.Any() || topN > data.Count)
                {
                    var result = acquire();

                    var typedList = result as IList<T> ?? result.ToList();
                    if (result != null && result.Any())
                    {
                        redisCacheService.AddTypedList(key, typedList, cacheTime);
                    }

                    return typedList;
                }

                    return data;
            }
            else
            {
                var result = acquire();

                var typedList = result as IList<T> ?? result.ToList();

                if (result != null && result.Any())
                {
                    redisCacheService.AddTypedList(key, typedList, cacheTime);
                }

                return typedList;
            }
        }
    }
}
