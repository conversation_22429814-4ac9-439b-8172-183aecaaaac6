﻿using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Matriks;
using Bigpara.Service.Hangfire.Services.Interfaces;
using Bigpara.Application.Contracts.Repositories.Matriks.Yuzeysels;
using Bigpara.Application.Contracts.Repositories.Matriks.Borsa;

namespace Bigpara.Service.Hangfire.Services;

public class YuzeyselService : IYuzeyselService
{
    #region Constants Yuzeysel
    private const string YUZEYSEL_HEPSI_B_KEY = "bigpara.yuzeysel.hepsi";

    private const string YUZEYSEL_DETAY_BY_SEMBOL = "bigpara.yuzeysel.by.sembol.{0}";
    private const string YUZEYSEL_DETAY_BY_SEMBOLS = "bigpara.yuzeysel.by.sembols.{0}";
    private const string YUZEYSEL_DETAY_BY_SEMBOLID = "bigpara.yuzeysel.by.sembolid.{0}";
    private const string YUZEYSEL_DETAY_BY_SEMBOL_TARIH = "bigpara.yuzeysel.by.sembol.tarih.{0}-{1}";
    #endregion
    private readonly IYuzeyselRepository _yuzeyselRepository;
    private readonly IHisseRepository _hisseRepository;
    private readonly IRedisCacheService _redisCacheService;

    public YuzeyselService
        (
            IYuzeyselRepository yuzeyselRepository,
            IHisseRepository hisseRepository,
            IRedisCacheService redisCacheService
        )
        {
            _yuzeyselRepository = yuzeyselRepository;
            _hisseRepository = hisseRepository;
            _redisCacheService = redisCacheService;
        }

    public async Task<Yuzeysel> GetYuzeyselBySembolId(int sembolId)
    {
        string key = string.Format(YUZEYSEL_DETAY_BY_SEMBOLID, sembolId);
        return _redisCacheService.Get(key, () => _yuzeyselRepository.GetById(sembolId).GetAwaiter().GetResult());
    }

    public async  Task<Yuzeysel> GetYuzeyselBySembolTarih(string sembol, DateTime tarih)
    {

        string key = string.Format(YUZEYSEL_DETAY_BY_SEMBOL_TARIH, sembol,tarih.ToString("yyyyMM.dd"));
        return _redisCacheService.Get(key, () => _yuzeyselRepository.GetYuzeyselBySembolTarih(sembol, tarih).GetAwaiter().GetResult());
    }

    public async Task<List<YuzeyselOpeation>> GetYuzeysellerList()
    {
        string key = string.Format(YUZEYSEL_HEPSI_B_KEY);
        return
            _redisCacheService.GetRedisList(key, () => _hisseRepository.GetYuzeysellerList().GetAwaiter().GetResult(),
                CacheKeys.CACHE_DATA_DURATION).ToList();
    }


    public async Task<Yuzeysel> GetBySembol(string sembol)
    {

        string key = string.Format(YUZEYSEL_DETAY_BY_SEMBOL, sembol);
        return _redisCacheService.Get(key, () => _yuzeyselRepository.GetBySembol(sembol).GetAwaiter().GetResult());
    }

    public async Task<List<Yuzeysel>> GetBySembols(string sembols)
    {

        string key = string.Format(YUZEYSEL_DETAY_BY_SEMBOLS, sembols);
        return _redisCacheService.GetRedisList(key, () => _yuzeyselRepository.GetBySembols(sembols).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_15SEC_DURATION).ToList();
    }
}
