﻿using Microsoft.AspNetCore.SignalR.Protocol;
using StackExchange.Redis;
using System.Text.Json;
using System.Text;
using System.Buffers;

namespace Bigpara.Service.Hangfire.Infrastructure.Publishers
{

    public class SignalRPublisher : IPublisher
    {
        private readonly IConnectionMultiplexer _redis;
        private readonly ILogger<SignalRPublisher> _logger;
        private readonly ISubscriber _subscriber;
        private readonly MessagePackHubProtocol _hubProtocol;

        public SignalRPublisher(
            IConnectionMultiplexer redis,
            ILogger<SignalRPublisher> logger)
        {
            _redis = redis;
            _logger = logger;
            _subscriber = _redis.GetSubscriber();
            _hubProtocol = new MessagePackHubProtocol();
        }

        public async Task PublishSymbolDataAsync(string symbol, Dictionary<string, string> data)
        {
           
            var channel = $"Bigpara.Service.Realtime.Hubs.TradeHub:group:symbol_chart_{symbol}";

            // SignalR Protocol Message Format
            var signalRMessage = new
            {
                type = 1, // InvocationMessage
                target = "ReceiveChart",
                arguments = new object[] { symbol, data }
            };

            var message = JsonSerializer.Serialize(signalRMessage);
            await _subscriber.PublishAsync(channel, message);
         }
        public async Task PublishSymbolDetailAsync(string symbol, Dictionary<string, string> detail)
        {
            try
            {
                var channel = $"signalr_backplaneBigpara.Service.Realtime.Hubs.TradeHub:group:symbol_detail_{symbol}";
                var signalRMessage = new
                {
                    type = 1,
                    target = "ReceiveDetail",
                    arguments = new object[] { symbol, detail }
                };

                var message = System.Text.Json.JsonSerializer.Serialize(signalRMessage);
                await _subscriber.PublishAsync(channel, message);

                _logger.LogDebug($"Published symbol detail for {symbol}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error publishing symbol detail for {symbol}");
            }
        }

        public async Task PublishAllSymbolsAsync(List<Dictionary<string, string>> symbols)
        {
            try
            {
                var channel = "signalr_backplaneBigpara.Service.Realtime.Hubs.TradeHub:group:symbol_table_all";
                var signalRMessage = new
                {
                    type = 1,
                    target = "ReceiveTable",
                    arguments = new object[] { symbols }
                };

                var message = System.Text.Json.JsonSerializer.Serialize(signalRMessage);
                await _subscriber.PublishAsync(channel, message);

                _logger.LogDebug($"Published all symbols data ({symbols.Count} symbols)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing all symbols data");
            }
        }

        public async Task PublishSymbolsByTypeAsync(string type, List<Dictionary<string, string>> symbols)
        {
            try
            {
                var channel = $"signalr_backplaneBigpara.Service.Realtime.Hubs.TradeHub:group:symbol_table_{type}_all";
                var signalRMessage = new
                {
                    type = 1,
                    target = "ReceiveTypeTable",
                    arguments = new object[] { type, symbols }
                };

                var message = System.Text.Json.JsonSerializer.Serialize(signalRMessage);
                await _subscriber.PublishAsync(channel, message);

                _logger.LogDebug($"Published {type} symbols data ({symbols.Count} symbols)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error publishing {type} symbols data");
            }
        }
    }
}
