﻿using Bigpara.Domain.Matriks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Report;

public class GetSeansRaporu10DkEnCokDegerKazananRecurringJob : IRecurringJob
{
    private readonly ISeansRaporlariService _seansRaporlariService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<GetSeansRaporu10DkEnCokDegerKazananRecurringJob> _logger;

    public GetSeansRaporu10DkEnCokDegerKazananRecurringJob
    (
        ISeansRaporlariService seansRaporlariService,
        INotificationService notificationService,
        ILogger<GetSeansRaporu10DkEnCokDegerKazananRecurringJob> logger
    )
    {
        _seansRaporlariService = seansRaporlariService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "GetSeansRaporu10DkEnCokDegerKazananRecurringJob";
     public IEnumerable<string> Crons => ["*/10 9-17 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("GetSeansRaporu10DkEnCokDegerKazananRecurringJob - Başladı ");
            await ProcessGetSeansRaporu10DkEnCokDegerKazanan();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    /// <summary>
    /// 10 dk gun ici top 3 hisse aliniyor
    /// </summary>
    public async Task ProcessGetSeansRaporu10DkEnCokDegerKazanan()
    {
        if (!_seansRaporlariService.IsActive())
            return;

        try
        {
            const string pattern = "En çok değer kazanan hisse senetleri {0}.";
            var data = await _seansRaporlariService.GetSeansRaporu10DkEnCokDegerKazanan();
            var semboller = string.Join(",", data.Select(r => r.ACIKLAMA).ToList());

            var result = string.Format(pattern, semboller);

            var seansRaporuYuzeysel = data.FirstOrDefault();
            if (seansRaporuYuzeysel == null) return;
            var seansRaporu = new SeansRaporu
            {
                HisseCumle = result,
                Yon = 1,
                HisseAdi = string.Empty,
                EklenmeTarihi = DateTime.Now
            };

            await _seansRaporlariService.CreateAndCacheSeansRaporu(seansRaporu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            //EmailHelper.SendEmail("GetSeansRaporu10DkEnCokDegerKazanan", ex.Message);
            await _notificationService.NotifyErrorAsync($"GetSeansRaporu10DkEnCokDegerKazanan", ex.Message);
        }
    }
}
