﻿using Bigpara.Domain.Enums;
using Bigpara.Domain;
using Bigpara.Domain.Bigpara;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.Definitions;
using Bigpara.Domain.Matriks.StoredProcedureResults;
using Bigpara.Domain.SummaryTypes;

namespace Bigpara.Application.Contracts.Repositories.Matriks.Borsa;

public interface IHisseRepository
{
    Task<List<Hisse>> GetTumSemboller();

    Task<Yuzeysel> GetYuzeyselBySembolId(int sembolId);

    Task<List<YuzeyselOpeation>> GetYuzeysellerList();

    Task<List<SembolOzet>> GetHisseDurumByType(ShareProcessStatusType type);

    Task<List<SembolOzet>> GetHisseDurumByType(ShareProcessStatusType type, SeansTypes seansTypes);

    Task<List<SembolOzet>> GetAgirlikliOrtalamaDurumV2(string orderByString);

    Task<List<SembolOzet>> GetAgirlikliOrtalamaDurum(string orderByString, int pageNo, byte pageSize);

    Task<PagingResult<SembolOzet>> GetAgirlikliOrtalamaDurumPaging(string orderByString, int pageNo, byte pageSize);

    Task<HisseYuzeysel> GetHisseYuzeyselDegerleriBySembolId(int sembolId);

    Task<List<Hisse>> GetHisseList();

    Task<List<HisseYuzeysel>> GetHisseYuzeysel();

    Task<List<HisseYuzeyselOnline>> GetHisseYuzeyselOnline();

    Task<List<HisseYuzeyselIndikator>> GetHisseYuzeyselTarihsiz(string sembol);

    Task<List<HisseYuzeysel>> GetHisseYuzeyselTarihsizBySembolId(int sembol);

    Task<List<DunyaBorsalari>> GetDunyaBorsalariData();

    Task<List<BilancoDonem>> GetBilancoDonems(string sembol, string donem, int cins);

    Task<List<BilancoDonem>> GetBilancoDonemByYil(string sembol, string donem, int cins, int yil);

    Task<List<BilancoDonemSembol>> GetBilancoDonemsBySembol(string sembol);

    Task<List<HisseYuzeysel>> GetEndeksHisseYuzeysels(string endeks);

    Task<PagingResult<HisseYuzeysel>> GetEndeksHisseYuzeysels(string endeks, string hisse, int pageNo, byte pageSize, char? letter);

    Task<List<KisaTanimSemboller>> GetDefaultPiyasaBandi();

    Task<List<LiveStockHisse>> GetYuzeselOnlineBySembol(DateTime? tarih);

    Task<PagingResult<HisseYuzeysel>> GetHisseFiyatlariByEndeksVeHarf(string endeks, int pageNo, byte pageSize, char? letter);

    Task<List<HisseYuzeysel>> GetHisselerIslemHacmiArtanAzalan(OrderByDirection orderByDirection);
    Task<PagingResult<HisseYuzeysel>> GetHisselerIslemHacmiFiyatArtanAzalan(TradingVolumeFilterType tradingVolumeFilterType, int pageNo, byte pageSize);

    Task<List<HisseYuzeysel>> GetHisselerIslemHacmiFiyatArtanAzalan(TradingVolumeFilterType tradingVolumeFilterType);

    Task<List<SembolOzet>> GetAgirlikliOrtalama(string orderBy, int pageNumber, int pageSize);

    Task<PagingResult<PerformansAnaliz>> GetPerformansAnaliz(string endeks, int sembolId, DateTime? startDateTime,
        DateTime? endDateTime, string currency, int orderBy, int pageNo, byte pageSize);

    Task<List<PerformansAnaliz>> GetPerformansAnaliz(string endeks, int sembolId, DateTime? startDateTime,
        DateTime? endDateTime, string currency, int orderBy);

    Task<List<KisaTanimSemboller>> GetPiyasaBandiHisseListe();

    #region User
    Task<List<HisseYuzeysel>> GetUserHisseYuzeysel(int userId);

    Task<List<KisaTanimSemboller>> GetUserHisseSembolList(int userId, int pageType);

    Task<List<Haberler>> GetUserHisseHaberleri(int userId);

    Task<List<UserSembol>> GetUserHisseList(int userId);

    Task<int> SaveUserHisse(int userId, int sembolId, int pageType, int opId);

    Task<int> SaveUserAlert(int userId, string sembol, double price, string alertField);

    Task<int> DeleteUserAlert(int userId, int sembolId);

    Task<List<UserAlert>> GetUserAlerts(int userId);

    #endregion

    Task<int> SaveUserHisse(int userId, int sembolId, int pageType, int opId, int orderId);

    Task<List<UserAlert>> GetUserAlertResult(int userId);

    Task<int> UserAlertDeactivate(int userId, int id);

    Task<int> UserAlertInstanceAlert(int userId);

    Task<PagingResult<HisseYuzeysel>> GetHisseFiyatlariByEndeksVeHarfOnline(string endeks, int pageNo, byte pageSize, char? letter);


    Task<List<HisseYuzeysel>> GetUserHisseYuzeyselOnline(int userId);

    Task<List<UserHisseHaber>> GetUserSembolHaberleri(int userId);
}
