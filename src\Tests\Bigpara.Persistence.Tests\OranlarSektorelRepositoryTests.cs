using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Bigpara.Domain.Matriks;
using Moq;
using Xunit;
using Microsoft.Data.SqlClient;
using Bigpara.Persistence.Matriks.Borsa;

namespace Bigpara.Persistence.Tests.Matriks.Borsa
{
    public class OranlarSektorelRepositoryTests
    {
        private readonly Mock<IMatriksDbContext> _mockDbContext;
        private readonly OranlarSektorelRepository _repository;

        public OranlarSektorelRepositoryTests()
        {
            _mockDbContext = new Mock<IMatriksDbContext>();
            _repository = new OranlarSektorelRepository(_mockDbContext.Object);
        }

        [Fact]
        public async Task GetOranlarSektorels_ReturnsList()
        {
            // Arrange
            var sembol = "ABC";
            var expected = new List<OranlarSektorel> { new OranlarSektorel { Id = 1 } };
            _mockDbContext.Setup(x => x.ExecuteStoredProcedureAsync<OranlarSektorel>(
                "bp.pGetHisseMaliOranlar", It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetOranlarSektorels(sembol);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetSektorelMaliOranlar_ReturnsList()
        {
            // Arrange
            var expected = new List<OranlarSektorel> { new OranlarSektorel { Id = 2 } };
            _mockDbContext.Setup(x => x.ExecuteStoredProcedureAsync<OranlarSektorel>(
                "bp.pGetMaliOranlar"))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetSektorelMaliOranlar();

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task InsertOranSektorel_ReturnsAffectedRows()
        {
            // Arrange
            var oran = new OranlarSektorel { Id = 3, Sektor_ID = 1, Senet = "DEF", Donem = "2023/12", UpdatedDateTime = DateTime.Now };
            _mockDbContext.Setup(x => x.ExecuteNonQueryAsync(
                "bp.pInsertOranlarSektorel", It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.InsertOranSektorel(oran);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task DeleteOranSektorel_ReturnsAffectedRows()
        {
            // Arrange
            _mockDbContext.Setup(x => x.ExecuteNonQueryAsync("bp.pDeleteOranlarSektorel"))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.DeleteOranSektorel();

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task GetOranlarSektorelId_ReturnsList()
        {
            // Arrange
            int id = 5;
            var expected = new List<OranlarSektorel> { new OranlarSektorel { Id = id } };
            _mockDbContext.Setup(x => x.ExecuteStoredProcedureAsync<OranlarSektorel>(
                "bp.pGetHisseMaliOranlarById", It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetOranlarSektorelId(id);

            // Assert
            Assert.Equal(expected, result);
        }
    }
}
