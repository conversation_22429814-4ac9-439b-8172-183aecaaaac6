﻿using Bigpara.Domain.Matriks;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Services.Interfaces;
using System.Text;

namespace Bigpara.Service.Hangfire.Jobs;

public class OtomatikTeknikYorumRecurringJob : IRecurringJob
{
    int _seansOty = 1;
    const int MomRefAltDeger = 100;
    const int CciRefAltDeger = -100;
    const int CciRefUstDeger = 100;
    const int RsiRefAltDeger = 30;
    const int RsiRefUstDeger = 70;
    const int FStochRefAltDeger = 20;
    const int FStochRefUstDeger = 80;

    private readonly IIndikatorlerService _indikatorlerService;
    private readonly IYuzeyselService _yuzeyselService;
    private readonly IOtomatikTeknikYorumlarService _otomatikTeknikYorumlarService;
    private readonly IGrafikService _grafikService;
    private readonly ILogger<OtomatikTeknikYorumRecurringJob> _logger;

    public OtomatikTeknikYorumRecurringJob
    (
        IIndikatorlerService indikatorlerService,
        IYuzeyselService yuzeyselService,
        IOtomatikTeknikYorumlarService otomatikTeknikYorumlarService,
        IGrafikService grafikService,
        ILogger<OtomatikTeknikYorumRecurringJob> logger
    )
    {
        _indikatorlerService = indikatorlerService;
        _yuzeyselService = yuzeyselService;
        _otomatikTeknikYorumlarService = otomatikTeknikYorumlarService;
        _grafikService = grafikService;
        _logger = logger;
    }

    public string Name => "OtomatikTeknikYorumRecurringJob";
    public IEnumerable<string> Crons => ["15 10 * * 1-5", "45 15 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("OtomatikTeknikYorumRecurringJob - Başladı ");
            await ProcessOtomatikTeknikYorum();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.StackTrace);
        }
    }

    public async Task ProcessOtomatikTeknikYorum()
    {
        try
        {
            DateTime todayDate = DateTime.Today;
            var indikatorlerRows = await _indikatorlerService.GetIndikatorlers(todayDate);
            var yuzeysel = await _yuzeyselService.GetYuzeysellerList();
            if (indikatorlerRows.Any())
            {
                foreach (var itemIndikator in indikatorlerRows)
                {
                    if (itemIndikator.SEMBOL == "HURGZ")
                    {
                        _logger.LogError($"Seans : {_seansOty}");
                        _logger.LogError($"itemIndikator.UpdatedDateTime.Value.Hour : {itemIndikator.UpdatedDateTime.Value.Hour}");
                        _logger.LogError($"Seans Kontrol : {itemIndikator.UpdatedDateTime != null && itemIndikator.UpdatedDateTime.Value.Hour > 17}");
                    }
                    if (itemIndikator.UpdatedDateTime != null && itemIndikator.UpdatedDateTime.Value.Hour > 17)
                    {
                        _seansOty = 2;
                    }
                    //Console.WriteLine(itemIndikator.SEMBOL);

                    YuzeyselOpeation rowYuzeysel = yuzeysel.FirstOrDefault(r => r.SEMBOL == itemIndikator.SEMBOL);

                    if (rowYuzeysel != null)
                    {
                        string sYorum1 = HisseYorum1(itemIndikator, rowYuzeysel);
                        string sYorum2 = await HisseYorum2(itemIndikator, rowYuzeysel);
                        string sTablo1 = HareketliOrtalamalarTablo(itemIndikator, rowYuzeysel.KAPANIS);
                        string sTablo2 = IndikatorDegerleriTablo(itemIndikator);
                        string sTablo3 = PivotDegerleriTablo(rowYuzeysel);

                        try
                        {
                            var rowToUpdate = (await _otomatikTeknikYorumlarService.GetOtomatikTeknikYorumlars(itemIndikator.SEMBOL, DateTime.Today)).FirstOrDefault();

                            if (rowToUpdate == null)
                            {
                                var otomatikTeknikYorumlar = new OtomatikTeknikYorumlar
                                {
                                    SEMBOL = itemIndikator.SEMBOL,
                                    TARIH = DateTime.Today,
                                    UpdatedDateTime = DateTime.Now,
                                    Yorum1 = sYorum1,
                                    Yorum2 = sYorum2,
                                    Tablo1 = sTablo1,
                                    Tablo2 = sTablo2,
                                    Tablo3 = sTablo3
                                };
                                await _otomatikTeknikYorumlarService.CreateOrUpdateOtomatikTeknikYorumlar(otomatikTeknikYorumlar);
                            }
                            else
                            {
                                rowToUpdate.UpdatedDateTime = DateTime.Now;
                                rowToUpdate.Yorum1 = sYorum1;
                                rowToUpdate.TARIH = DateTime.Today;
                                rowToUpdate.Yorum2 = sYorum2;
                                rowToUpdate.Tablo1 = sTablo1;
                                rowToUpdate.Tablo2 = sTablo2;
                                rowToUpdate.Tablo3 = sTablo3;
                                _otomatikTeknikYorumlarService.CreateOrUpdateOtomatikTeknikYorumlar(rowToUpdate);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex.StackTrace);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.StackTrace);
        }
    }

    private string HisseYorum1(Indikatorler itemIndikator1, YuzeyselOpeation rowYuzeysel1)
    {
        var tmpStr = new StringBuilder();
        if (itemIndikator1.MOV_5 != null && itemIndikator1.MOV_100 != null && itemIndikator1.MOV_250 != null)
        {
            tmpStr.AppendLine("<div class=\"otyTable1 mBot20\">");
            tmpStr.AppendLine(" <div class=\"otyHead\">");
            tmpStr.AppendFormat("  <span class=\"date\">Yorum tarihi: <b>{0}</b></span>", itemIndikator1.TARIH.ToShortDateString());
            tmpStr.AppendFormat("  <span class=\"seans\">{0}</span>", _seansOty == 1 ? "1. Seans" : "2. Seans");
            tmpStr.AppendLine(" </div>");
            tmpStr.AppendLine(" <div class=\"otyBody\">");
            tmpStr.AppendLine("  <div class=\"col1\">");
            tmpStr.AppendFormat("   <span class=\"text\">{0}</span>", itemIndikator1.SEMBOL);
            tmpStr.AppendFormat("   <span class=\"text\">{0}</span>", TextHelper.FormatNumber(rowYuzeysel1.KAPANIS, 2));
            tmpStr.AppendFormat("   <span class=\"text\">%{0}</span>", TextHelper.FormatNumber(rowYuzeysel1.YUZDEDEGISIM, 2));
            tmpStr.AppendLine("  </div>");
            tmpStr.AppendLine("  <div class=\"col2\">");
            tmpStr.AppendLine("   <span class=\"text\">Önceki Kapanış</span>");
            tmpStr.AppendLine("   <span class=\"text\">Değişim</span>");
            tmpStr.AppendLine("   <span class=\"text\">Kısa Vadeli Trend</span>");
            tmpStr.AppendLine("  </div>");
            tmpStr.AppendLine("  <div class=\"col3\">");
            tmpStr.AppendFormat("   <span class=\"text\">{0}</span>", TextHelper.FormatNumber(rowYuzeysel1.DUNKUKAPANIS, 2));
            tmpStr.AppendFormat("   <span class=\"text\">{0}</span>", TextHelper.FormatNumber(rowYuzeysel1.YUZDEDEGISIM, 2));
            tmpStr.AppendLine((rowYuzeysel1.KAPANIS > itemIndikator1.MOV_5) ? "<span class=\"text\"><small class=\"s up\">&nbsp;</small></span>" : "<span class=\"text\"><small class=\"s dw\">&nbsp;</small></span>");
            tmpStr.AppendLine("  </div>");
            tmpStr.AppendLine("  <div class=\"col2\">");
            tmpStr.AppendLine("   <span class=\"text\">Düşük</span>");
            tmpStr.AppendLine("   <span class=\"text\">Yüksek</span>");
            tmpStr.AppendLine("   <span class=\"text\">Orta Vadeli Trend</span>");
            tmpStr.AppendLine("  </div>");
            tmpStr.AppendLine("  <div class=\"col3\">");
            tmpStr.AppendFormat("   <span class=\"text\">{0}</span>", TextHelper.FormatNumber(rowYuzeysel1.DUSUK, 2));
            tmpStr.AppendFormat("   <span class=\"text\">{0}</span>", TextHelper.FormatNumber(rowYuzeysel1.YUKSEK, 2));
            tmpStr.AppendLine((rowYuzeysel1.KAPANIS > itemIndikator1.MOV_100) ? "<span class=\"text\"><small class=\"s up\">&nbsp;</small></span>" : "<span class=\"text\"><small class=\"s dw\">&nbsp;</small></span>");
            tmpStr.AppendLine("  </div>");
            tmpStr.AppendLine("  <div class=\"col2\">");
            tmpStr.AppendLine("   <span class=\"text\">İşlem Adedi</span>");
            tmpStr.AppendLine("   <span class=\"text\">İşlem Hacmi (TL)</span>");
            tmpStr.AppendLine("   <span class=\"text\">Uzun Vadeli Trend</span>");
            tmpStr.AppendLine("  </div>");
            tmpStr.AppendLine("  <div class=\"col3\">");
            tmpStr.AppendFormat("   <span class=\"text\">{0}</span>", TextHelper.FormatNumber(rowYuzeysel1.HACIMLOT, 0));
            tmpStr.AppendFormat("   <span class=\"text\">{0}</span>", TextHelper.FormatNumber(rowYuzeysel1.HACIMTL, 0));
            tmpStr.AppendLine((rowYuzeysel1.KAPANIS > itemIndikator1.MOV_250) ? "<span class=\"text\"><small class=\"s up\">&nbsp;</small></span>" : "<span class=\"text\"><small class=\"s dw\">&nbsp;</small></span>");
            tmpStr.AppendLine("  </div>");
            tmpStr.AppendLine(" </div>");
            tmpStr.AppendLine("</div>");
        }

        return tmpStr.ToString();
    }
    private async Task<string> HisseYorum2(Indikatorler itemIndikator1, YuzeyselOpeation rowYuzeysel1)
    {
        var tmpStr = new StringBuilder();
        tmpStr.AppendLine("<div class=\"Bot20\">");
        if (itemIndikator1.MOV_10 != null && itemIndikator1.MOV_20 != null && itemIndikator1.MOV_50 != null && itemIndikator1.MOV_250 != null)
        {
            tmpStr.AppendLine("<p>");
            tmpStr.AppendFormat("<b>{0}</b> hissesi ", rowYuzeysel1.SEMBOL);
            tmpStr.AppendFormat("{0} tarihinde {1} ", itemIndikator1.TARIH.ToShortDateString(), _seansOty == 1 ? "1.seansı" : "günü");
            tmpStr.AppendFormat("{0} TL'den {1}tamamladı. ", TextHelper.FormatNumber(rowYuzeysel1.KAPANIS.GetValueOrDefault(0), 2), (!IsApproximatelyEqual(rowYuzeysel1.KAPANISFARK, 0.0)) ? string.Format("ve %{0} deger {1} ile ", TextHelper.FormatNumber(rowYuzeysel1.YUZDEDEGISIM, 2), (rowYuzeysel1.KAPANISFARK < 0 ? " kaybı" : " kazancı")) : "");
            tmpStr.AppendFormat("Hisse senedi {0} TL'den yaptığı kapanış ile 10 günlük hareketli ortalamaların {1}. ", rowYuzeysel1.KAPANIS, (itemIndikator1.MOV_10 > rowYuzeysel1.KAPANIS ? " altında seyrediyor" : " üzerinde bulunuyor"));
            tmpStr.AppendLine("Hisse senedinin kısa vadeli yönü için 10 ve 21 günlük hareketli ortalamalarının üzerinde kalıp kalmadığı izlenmelidir.");
            tmpStr.AppendFormat("Şayet hisse senedi 50 günlük hareketli ortalaması olan {0} TL'nin altına inerse orta vadeli trend yön değiştirerek sat sinyali verecektir. ", itemIndikator1.MOV_50);
            if (rowYuzeysel1.KAPANIS > itemIndikator1.MOV_250)
            {
                tmpStr.AppendFormat("Benzer şekilde, şu an {0} TL fiyattan işlem gören hisse senedi 250 günlük ortalaması olan {1} TL'nin altına inerse, ", rowYuzeysel1.KAPANIS, itemIndikator1.MOV_250);
                tmpStr.AppendLine("bu durum uzun vadeli yükseliş trendinin kırıldığı anlamına gelecektir. ");
                tmpStr.AppendLine("Bu nedenle, hisse senedinde kısa vade için 10 ve 20 günlük ortalamaları ");
                tmpStr.AppendFormat("olan {0} TL ve {1} TL'nin üzerinde kalıp kalamadığı takip edilmedilir.", itemIndikator1.MOV_10, itemIndikator1.MOV_20);
            }

            tmpStr.AppendLine("</p>");
        }

        //Eğer hisse için 3 günlük kayıt yok ise burayı çıktıya eklemiyoruz.
        string ucGunlukYukselis = await UcGunlukYukselisString(rowYuzeysel1.SEMBOL, rowYuzeysel1.SEMBOLID);
        if (ucGunlukYukselis != "")
        {
            tmpStr.AppendLine("<p>");
            tmpStr.AppendLine(ucGunlukYukselis);
            tmpStr.AppendLine("</p>");
        }

        //Momentum indikatörü
        if (itemIndikator1.Momentum_12 != null)
        {
            tmpStr.AppendLine("<p>");
            tmpStr.AppendFormat("{0} seviyesinde bulunan Momentum indikatörü hissenin güç {1} gösteriyor. ", TextHelper.FormatNumber(itemIndikator1.Momentum_12, 2), (itemIndikator1.Momentum_12 < 100 ? "kaybettiğini" : "kazandığını"));
            tmpStr.AppendFormat("Bu konumu ile {0} bölgesinde bulunan hissede {1} seviyesi referans dönüş noktası olarak takip edilmelidir. ", (itemIndikator1.Momentum_12 < 100 ? "satım" : "alım"), MomRefAltDeger);
            tmpStr.AppendFormat("Bu seviyeden dönüş yapılması halinde gösterge {0} sinyal üretmeye başlayacaktır. ", (itemIndikator1.Momentum_12 > 100 ? "negatif" : "pozitif"));
            tmpStr.AppendLine("</p>");
        }

        //var eaDfinansEntities = new dfinansEntities();
        //var rowsIndikatorler = (from o in eaDfinansEntities.Indikatorler 
        //                        where o.TARIH < itemIndikator1.TARIH && o.SEMBOL == itemIndikator1.SEMBOL orderby o.TARIH descending select o);

        var rowsIndikatorler = await _indikatorlerService.GetOtomatikTeknikYorumIndikatorSembolbyTarih(itemIndikator1.SEMBOL, itemIndikator1.TARIH);
        //CCI indikatörü
        if (itemIndikator1.CCI_14 != null)
        {
            tmpStr.AppendLine("<p>");
            if (itemIndikator1.CCI_14 > CciRefUstDeger || itemIndikator1.CCI_14 < CciRefAltDeger)
            {
                tmpStr.AppendFormat("Yatay piyasada daha iyi sonuçlar üreten CCI göstergesi, {0} değeri ile hissenin ", itemIndikator1.CCI_14);
                if (itemIndikator1.CCI_14 < CciRefAltDeger)
                {
                    tmpStr.AppendFormat("aşırı satım bölgesinde olduğunu göstermektedir. Hissenin {0}’ün üstüne çıkıp çıkmadığı gözlenmeli ve çıkması halinde AL sinyali üreteceği göz ardı edilmemelidir.", CciRefAltDeger);
                }
                else if (itemIndikator1.CCI_14 > CciRefUstDeger)
                {
                    tmpStr.AppendFormat("aşırı alım bölgesinde olduğunu göstermektedir. Hissenin {0}’ün altına inip inmediği gözlenmeli ve inilmesi halinde SAT sinyali üreteceği göz ardı edilmemelidir.", CciRefUstDeger);
                }
            }
            else
            {
                foreach (var indikatorcci in rowsIndikatorler)
                {
                    if (indikatorcci.CCI_14 < CciRefAltDeger)
                    {
                        tmpStr.AppendFormat("CCI indikatörü {0} değeri ile hissenin satım bölgesinde olduğunu göstermektedir. ", TextHelper.FormatNumber(itemIndikator1.CCI_14, 2));
                        tmpStr.AppendFormat("Hisse en son {0} gün önce {1} aşağı keserek aşırı satım bölgesine girdi.", Convert.ToInt32((DateTime.Now - indikatorcci.TARIH).TotalDays - 1), CciRefAltDeger);
                        break;
                    }
                    if (indikatorcci.CCI_14 > CciRefUstDeger)
                    {
                        tmpStr.AppendFormat("CCI indikatörü {0} değeri ile hissenin alım bölgesinde olduğunu göstermektedir. ", TextHelper.FormatNumber(itemIndikator1.CCI_14, 2));
                        tmpStr.AppendFormat("Hisse en son {0} gün önce {1} yukarı keserek aşırı alım bölgesine girdi.", Convert.ToInt32((DateTime.Now - indikatorcci.TARIH).TotalDays - 1), CciRefUstDeger);
                        break;
                    }
                }
            }

            tmpStr.AppendLine("</p>");
        }

        //RSI indikatörü
        if (itemIndikator1.RSI_14 != null)
        {
            tmpStr.AppendLine("<p>");
            if (itemIndikator1.RSI_14 > RsiRefUstDeger || itemIndikator1.RSI_14 < RsiRefAltDeger)
            {
                tmpStr.AppendFormat("RSI indikatörü {0} seviyesinde bulunan hissenin", TextHelper.FormatNumber(itemIndikator1.RSI_14, 2));
                if (itemIndikator1.RSI_14 > RsiRefUstDeger)
                {
                    tmpStr.AppendFormat("{0}'in üzerinde olması, hissenin aşırı alım bölgesinde olduğunu göstermektedir.", RsiRefUstDeger);
                    tmpStr.AppendFormat("Bu nedenle, {0}'in altına inilip inilmediği gözlenmeli ve inilmesi halinde SAT sinyali üreteceği göz ardı edilmemelidir.", RsiRefUstDeger);
                }
                else if (itemIndikator1.RSI_14 < RsiRefAltDeger)
                {
                    tmpStr.AppendFormat("{0}'in altında olması, hissenin aşırı satım bölgesinde olduğunu göstermektedir.", RsiRefAltDeger);
                    tmpStr.AppendFormat("Bu nedenle, {0}'un üstüne çıkılıp çıkılmadığı gözlenmeli ve çıkılması halinde AL sinyali üreteceği göz ardı edilmemelidir.", RsiRefAltDeger);
                }
            }
            else
            {
                foreach (var indikatorRsi in rowsIndikatorler)
                {
                    if (indikatorRsi.RSI_14 > RsiRefUstDeger)
                    {
                        tmpStr.AppendFormat("RSI indikatörü {0} seviyesinde olan hisse, en son ", TextHelper.FormatNumber(itemIndikator1.RSI_14, 2));
                        tmpStr.AppendFormat("{0} gün önce {1} seviyesini aşağı keserek SAT sinyali üretmeye başladı.", Convert.ToInt32((DateTime.Now - indikatorRsi.TARIH).TotalDays - 1), RsiRefUstDeger);
                        break;
                    }
                    if (indikatorRsi.RSI_14 < RsiRefAltDeger)
                    {
                        tmpStr.AppendFormat("RSI indikatörü {0} seviyesinde olan hisse, en son ", TextHelper.FormatNumber(itemIndikator1.RSI_14, 2));
                        tmpStr.AppendFormat("{0} gün önce {1} seviyesini yukarı keserek AL sinyali üretmeye başladı ve bugünkü {2} değeri ile satım bölgesindeki konumunu korudu. ", Convert.ToInt32((DateTime.Now - indikatorRsi.TARIH).TotalDays - 1), RsiRefUstDeger, TextHelper.FormatNumber(itemIndikator1.RSI_14, 2));
                        break;
                    }
                }
            }

            tmpStr.AppendLine("</p>");
        }

        //MACD indikatörü
        if (itemIndikator1.MACD_26_12 != null && itemIndikator1.MACD_Trigger_9 != null)
        {
            bool macdSatim = false;
            bool macdAlim = false;
            bool firstCase = false;
            if (itemIndikator1.MACD_26_12 < itemIndikator1.MACD_Trigger_9)
            {
                macdSatim = true;
            }
            else if (itemIndikator1.MACD_26_12 >= itemIndikator1.MACD_Trigger_9)
            {
                macdAlim = true;
            }

            foreach (var indikatorlerMacd in rowsIndikatorler)
            {
                if (macdSatim == false && indikatorlerMacd.MACD_26_12 < indikatorlerMacd.MACD_Trigger_9)
                {
                    tmpStr.AppendLine("<p>");
                    tmpStr.AppendFormat("{0} olan MACD eğrisi, {1} olan hareketli ortalamasını aşağı yönde kesti. ", itemIndikator1.MACD_26_12, itemIndikator1.MACD_Trigger_9);
                    tmpStr.AppendLine("MACD’ın hareketli ortalamasının altına inmesi sebebiyle gösterge SAT sinyali üretmeye başladı.");
                    tmpStr.AppendLine("</p>");
                    firstCase = true;
                }
                if (macdAlim == false && indikatorlerMacd.MACD_26_12 >= indikatorlerMacd.MACD_Trigger_9)
                {
                    tmpStr.AppendLine("<p>");
                    tmpStr.AppendFormat("{0} olan MACD eğrisi, {1} olan hareketli ortalamasını yukarı yönde kesti. ", itemIndikator1.MACD_26_12, itemIndikator1.MACD_Trigger_9);
                    tmpStr.AppendLine("MACD’ın hareketli ortalamasının üstüne çıkması sebebiyle gösterge AL sinyali üretmeye başladı.");
                    tmpStr.AppendLine("</p>");
                    firstCase = true;
                }
                if (firstCase)
                    break;
            }
            //Birinci FOR'dan bir sonuç çıkmaz ise
            if (!firstCase)
            {
                foreach (var indikatorlerMacd in rowsIndikatorler)
                {
                    if (macdSatim == false && indikatorlerMacd.MACD_26_12 < indikatorlerMacd.MACD_Trigger_9)
                    {
                        tmpStr.AppendLine("<p>");
                        tmpStr.AppendFormat("MACD indikatörü en son {0} gün önce hareketli ortalamasının üstüne çıkarak AL sinyali üretti.", Convert.ToInt32((DateTime.Now - indikatorlerMacd.TARIH).TotalDays - 1));
                        tmpStr.AppendLine("</p>");
                        break;
                    }
                    if (macdAlim == false && indikatorlerMacd.MACD_26_12 >= indikatorlerMacd.MACD_Trigger_9)
                    {
                        tmpStr.AppendLine("<p>");
                        tmpStr.AppendFormat("MACD indikatörü en son {0} gün önce hareketli ortalamasının altına inerek SAT sinyali üretti.", Convert.ToInt32((DateTime.Now - indikatorlerMacd.TARIH).TotalDays - 1));
                        tmpStr.AppendLine("</p>");
                        break;
                    }
                }
            }
        }

        //Stochastic indikatörü
        if (itemIndikator1.FStoch_K_5 != null)
        {
            tmpStr.AppendLine("<p>");
            if (itemIndikator1.FStoch_K_5 > FStochRefUstDeger || itemIndikator1.FStoch_K_5 < FStochRefAltDeger)
            {
                if (itemIndikator1.RSI_14 > FStochRefUstDeger)
                {
                    tmpStr.AppendFormat("Stochastic Osilatör {0} değeri ile aşırı alım bölgesinde bulunuyor. ", TextHelper.FormatNumber(itemIndikator1.FStoch_K_5, 2));
                    tmpStr.AppendFormat("Bu nedenle {0}’in altına inip inilmediği gözlenmeli ve inilmesi halinde SAT sinyali üreteceği göz ardı edilmemelidir.", FStochRefUstDeger);
                }
                else if (itemIndikator1.RSI_14 < FStochRefAltDeger)
                {
                    tmpStr.AppendFormat("Stochastic Osilatör {0} değeri ile aşırı satım bölgesinde bulunuyor. ", TextHelper.FormatNumber(itemIndikator1.FStoch_K_5, 2));
                    tmpStr.AppendFormat("Bu nedenle {0}’un üstüne çıkılıp çıkılmadığı gözlenmeli ve çıkması halinde AL sinyali üreteceği göz ardı edilmemelidir.", FStochRefAltDeger);
                }
            }
            else
            {
                foreach (var indikatorlerFStoch in rowsIndikatorler)
                {
                    if (indikatorlerFStoch.FStoch_K_5 > FStochRefAltDeger)
                    {
                        tmpStr.AppendLine("Stochastic Osilatör en son ");
                        tmpStr.AppendFormat("{0} gün önce {1} seviyesini yukarı yönde keserek AL sinyali üretmeye başladı. ", Convert.ToInt32((DateTime.Now - indikatorlerFStoch.TARIH).TotalDays - 1), FStochRefAltDeger);
                        break;
                    }
                    if (indikatorlerFStoch.FStoch_K_5 < FStochRefUstDeger)
                    {
                        tmpStr.AppendLine("Stochastic Osilatör en son ");
                        tmpStr.AppendFormat("{0} gün önce {1} seviyesini aşağı yönde keserek SAT sinyali üretmeye başladı. ", Convert.ToInt32((DateTime.Now - indikatorlerFStoch.TARIH).TotalDays - 1), FStochRefUstDeger);
                        break;
                    }
                }
            }

            tmpStr.AppendLine("</p>");
        }

        //Bollinger indikatörü
        if (itemIndikator1.BBW_20_2_Middle != null)
        {
            if (rowYuzeysel1.KAPANIS > itemIndikator1.BBW_20_2_Up)
            {
                tmpStr.AppendLine("<p>");
                tmpStr.AppendLine("Hissenin kapanış fiyatı, ");
                tmpStr.AppendLine("bollinger üst kanalını geçerek yükselişin devam ettiğini gösterse de, bu durum hissenin aşırı alım bölgesinde yer aldığı şeklinde görülmeli ve dikkat edilmelidir.");
                tmpStr.AppendLine("</p>");
            }
            else if (rowYuzeysel1.KAPANIS < itemIndikator1.BBW_20_2_Down)
            {
                tmpStr.AppendLine("<p>");
                tmpStr.AppendLine("Hissenin kapanış fiyatı, ");
                tmpStr.AppendLine("bollinger alt bandını aşağı kırarak zayıflamanın sürdüğünü gösteriyor. Yukarı yönlü dönüşler için hisse fiyatının tekrar kanalın içine girdiği görülmelidir.");
                tmpStr.AppendLine("</p>");
            }
        }

        tmpStr.AppendLine("</div>");
        return tmpStr.ToString();
    }
    private string HareketliOrtalamalarTablo(Indikatorler itemIndikator1, double? kapanisFiyat)
    {
        var tmpStr = new StringBuilder();
        tmpStr.AppendLine("<div class=\"tableCnt\">");
        tmpStr.AppendLine("<div class=\"table wide\" style=\"margin-left: 0px;\">");
        tmpStr.AppendLine("<div class=\"tableBox\">");
        tmpStr.AppendLine(" <div class=\"tHead\">");
        tmpStr.AppendLine(" <ul>");
        tmpStr.AppendLine("         <li class=\"cell029 tal\">Hareketli Ortalamalar</li> ");
        tmpStr.AppendLine("         <li class=\"cell031\">Son Değer</li> ");
        tmpStr.AppendLine(" </ul>");
        tmpStr.AppendLine("     </div> ");
        tmpStr.AppendLine("     <div class=\"tBody\"> ");
        tmpStr.AppendLine("         <ul> ");
        tmpStr.AppendLine("             <li class=\"cell029 tal\"><b>Kapanış Fiyatı</b></li> ");
        tmpStr.AppendFormat("           <li class=\"cell031\"><b>{0}</b></li> ", TextHelper.FormatNumber(kapanisFiyat, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("         <ul class=\"col\"> ");
        tmpStr.AppendLine("             <li class=\"cell029 tal\">Hareketli Ortalamalar (5 günlük - Basit)</li> ");
        tmpStr.AppendFormat("           <li class=\"cell031\"><b>{0}</b></li> ", TextHelper.FormatNumber(itemIndikator1.MOV_5, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("         <ul class=\"col\"> ");
        tmpStr.AppendLine("             <li class=\"cell029 tal\">Hareketli Ortalamalar (10 günlük - Basit)</li> ");
        tmpStr.AppendFormat("           <li class=\"cell031\"><b>{0}</b></li> ", TextHelper.FormatNumber(itemIndikator1.MOV_10, 2));
        tmpStr.AppendLine("         </ul>");
        tmpStr.AppendLine("         <ul class=\"col\"> ");
        tmpStr.AppendLine("             <li class=\"cell029 tal\">Hareketli Ortalamalar (20 günlük - Basit)</li> ");
        tmpStr.AppendFormat("           <li class=\"cell031\"><b>{0}</b></li> ", TextHelper.FormatNumber(itemIndikator1.MOV_20, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("         <ul class=\"col\"> ");
        tmpStr.AppendLine("             <li class=\"cell029 tal\">Hareketli Ortalamalar (50 günlük - Basit)</li> ");
        tmpStr.AppendFormat("           <li class=\"cell031\"><b>{0}</b></li> ", TextHelper.FormatNumber(itemIndikator1.MOV_50, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("         <ul class=\"col\"> ");
        tmpStr.AppendLine("             <li class=\"cell029 tal\">Hareketli Ortalamalar (100 günlük - Basit)</li> ");
        tmpStr.AppendFormat("           <li class=\"cell031\"><b>{0}</b></li> ", TextHelper.FormatNumber(itemIndikator1.MOV_100, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("         <ul class=\"col\"> ");
        tmpStr.AppendLine("             <li class=\"cell029 tal\">Hareketli Ortalamalar (250 günlük - Basit)</li> ");
        tmpStr.AppendFormat("           <li class=\"cell031\"><b>{0}</b></li> ", TextHelper.FormatNumber(itemIndikator1.MOV_250, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("     </div> ");
        tmpStr.AppendLine(" </div> ");
        tmpStr.AppendLine(" </div> ");
        tmpStr.AppendLine(" </div> ");
        return tmpStr.ToString();
    }
    private string IndikatorDegerleriTablo(Indikatorler itemIndikator1)
    {
        var tmpStr = new StringBuilder();
        tmpStr.AppendLine("<div class=\"tableCnt\">");
        tmpStr.AppendLine("<div class=\"table wide\" style=\"margin-left: 0px;\">");
        tmpStr.AppendLine("<div class=\"tableBox\">");
        tmpStr.AppendLine(" <div class=\"tHead\">");
        tmpStr.AppendLine(" <ul>");
        tmpStr.AppendLine("         <li class=\"cell032 tal\">İndikatörler</li> ");
        tmpStr.AppendLine("         <li class=\"cell037\">Referans Üst Değer</li> ");
        tmpStr.AppendLine("         <li class=\"cell037\">Referans Alt Değer</li> ");
        tmpStr.AppendLine("         <li class=\"cell037\">Değer</li> ");
        tmpStr.AppendLine(" </ul>");
        tmpStr.AppendLine("     </div> ");
        tmpStr.AppendLine("     <div class=\"tBody\"> ");
        tmpStr.AppendLine("         <ul> ");
        tmpStr.AppendLine("             <li class=\"cell032 tal\">Momentum (12)</li> ");
        tmpStr.AppendLine("             <li class=\"cell037\">&nbsp;</li> ");
        tmpStr.AppendFormat("             <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(MomRefAltDeger, 2));
        tmpStr.AppendFormat("           <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(itemIndikator1.Momentum_12, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("         <ul> ");
        tmpStr.AppendLine("             <li class=\"cell032 tal\">Stochastic (5,3)</li> ");
        tmpStr.AppendFormat("             <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(FStochRefUstDeger, 2));
        tmpStr.AppendFormat("             <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(FStochRefAltDeger, 2));
        tmpStr.AppendFormat("           <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(itemIndikator1.FStoch_K_5, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("         <ul> ");
        tmpStr.AppendLine("             <li class=\"cell032 tal\">RSI (Relative Strenght Index) (14)</li> ");
        tmpStr.AppendFormat("             <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(RsiRefUstDeger, 2));
        tmpStr.AppendFormat("             <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(RsiRefAltDeger, 2));
        tmpStr.AppendFormat("           <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(itemIndikator1.RSI_14, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("         <ul> ");
        tmpStr.AppendLine("             <li class=\"cell032 tal\">CCI (Commodity Channel Index) (14)</li> ");
        tmpStr.AppendFormat("             <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(CciRefUstDeger, 2));
        tmpStr.AppendFormat("             <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(CciRefAltDeger, 2));
        tmpStr.AppendFormat("           <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(itemIndikator1.CCI_14, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("         <ul> ");
        tmpStr.AppendLine("             <li class=\"cell032 tal\">MACD (26,12)</li> ");
        tmpStr.AppendLine("             <li class=\"cell037\">&nbsp;</li> ");
        tmpStr.AppendLine("             <li class=\"cell037\">&nbsp;</li> ");
        tmpStr.AppendFormat("           <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(itemIndikator1.MACD_26_12, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("         <ul> ");
        tmpStr.AppendLine("             <li class=\"cell032 tal\">Bolinger Band (2,20)</li> ");
        tmpStr.AppendFormat("           <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(itemIndikator1.BBW_20_2_Up, 2));
        tmpStr.AppendFormat("           <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(itemIndikator1.BBW_20_2_Down, 2));
        tmpStr.AppendFormat("           <li class=\"cell037\">{0}</li> ", TextHelper.FormatNumber(itemIndikator1.BBW_20_2_Middle, 2));
        tmpStr.AppendLine("         </ul> ");
        tmpStr.AppendLine("     </div> ");
        tmpStr.AppendLine(" </div> ");
        tmpStr.AppendLine(" </div> ");
        tmpStr.AppendLine(" </div> ");
        return tmpStr.ToString();
    }
    private string PivotDegerleriTablo(YuzeyselOpeation rowYuzeysel1)
    {
        //Klasik
        double? pivotNoktasiKlasik = (rowYuzeysel1.YUKSEK + rowYuzeysel1.KAPANIS + rowYuzeysel1.DUSUK) / 3;
        double? destekSeviyesiKlasikS1 = (2 * pivotNoktasiKlasik) - rowYuzeysel1.YUKSEK;
        double? direncSeviyesiKlasikR1 = (2 * pivotNoktasiKlasik) - rowYuzeysel1.DUSUK;
        double? destekSeviyesiKlasikS2 = pivotNoktasiKlasik - (direncSeviyesiKlasikR1 - destekSeviyesiKlasikS1);
        double? destekSeviyesiKlasikS3 = rowYuzeysel1.DUSUK - 2 * (rowYuzeysel1.YUKSEK - pivotNoktasiKlasik);
        double? direncSeviyesiKlasikR2 = pivotNoktasiKlasik + direncSeviyesiKlasikR1 - destekSeviyesiKlasikS1;
        double? direncSeviyesiKlasikR3 = rowYuzeysel1.YUKSEK + 2 * (pivotNoktasiKlasik - rowYuzeysel1.DUSUK);

        //Fibonacci
        double? pivotNoktasiFibonacci = (rowYuzeysel1.YUKSEK + rowYuzeysel1.KAPANIS + rowYuzeysel1.DUSUK) / 3;
        double? direncSeviyesiFibonacciR1 = pivotNoktasiFibonacci + 0.382 * (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK);
        double? direncSeviyesiFibonacciR2 = pivotNoktasiFibonacci + 0.618 * (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK);
        double? direncSeviyesiFibonacciR3 = pivotNoktasiFibonacci + 1.000 * (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK);
        double? destekSeviyesiFibonacciS1 = pivotNoktasiFibonacci - 0.382 * (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK);
        double? destekSeviyesiFibonacciS2 = pivotNoktasiFibonacci - 0.618 * (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK);
        double? destekSeviyesiFibonacciS3 = pivotNoktasiFibonacci - 1.000 * (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK);

        //Camarilla
        double? direncSeviyesiCamarillaR1 = rowYuzeysel1.KAPANIS + (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK) * 1.1 / 12;
        double? direncSeviyesiCamarillaR2 = rowYuzeysel1.KAPANIS + (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK) * 1.1 / 6;
        double? direncSeviyesiCamarillaR3 = rowYuzeysel1.KAPANIS + (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK) * 1.1 / 4;
        double? destekSeviyesiCamarillaS1 = rowYuzeysel1.KAPANIS - (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK) * 1.1 / 12;
        double? destekSeviyesiCamarillaS2 = rowYuzeysel1.KAPANIS - (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK) * 1.1 / 6;
        double? destekSeviyesiCamarillaS3 = rowYuzeysel1.KAPANIS - (rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK) * 1.1 / 4;

        //Woodie 
        double? pivotNoktasiWoodie = (rowYuzeysel1.YUKSEK + 2 * rowYuzeysel1.KAPANIS + rowYuzeysel1.DUSUK) / 4;
        double? direncSeviyesiWoodieR1 = (2 * pivotNoktasiWoodie) - rowYuzeysel1.DUSUK;
        double? direncSeviyesiWoodieR2 = pivotNoktasiWoodie + rowYuzeysel1.YUKSEK - rowYuzeysel1.DUSUK;
        double? destekSeviyesiWoodieS1 = (2 * pivotNoktasiWoodie) - rowYuzeysel1.YUKSEK;
        double? destekSeviyesiWoodieS2 = pivotNoktasiWoodie - rowYuzeysel1.YUKSEK + rowYuzeysel1.DUSUK;

        //Demark
        double? tmpDemark = null;
        if (rowYuzeysel1.KAPANIS < rowYuzeysel1.ACILIS)
        {
            tmpDemark = (rowYuzeysel1.YUKSEK + (rowYuzeysel1.DUSUK * 2) + rowYuzeysel1.KAPANIS);
        }
        if (rowYuzeysel1.KAPANIS > rowYuzeysel1.ACILIS)
        {
            tmpDemark = ((rowYuzeysel1.YUKSEK * 2) + rowYuzeysel1.DUSUK + rowYuzeysel1.KAPANIS);
        }
        if ( IsApproximatelyEqual( rowYuzeysel1.KAPANIS,rowYuzeysel1.ACILIS.GetValueOrDefault(0),0.0))
        {
            tmpDemark = (rowYuzeysel1.YUKSEK + rowYuzeysel1.DUSUK + (rowYuzeysel1.KAPANIS * 2));
        }

        double? pivotNoktasiDemark = tmpDemark / 4;
        double? direncSeviyesiDemarkR1 = tmpDemark / 2 - rowYuzeysel1.DUSUK;
        double? destekSeviyesiDemarkS1 = tmpDemark / 2 - rowYuzeysel1.YUKSEK;

        StringBuilder tmpStr = new StringBuilder();
        tmpStr.AppendLine("<div class=\"tableCnt\">");
        tmpStr.AppendLine("<div class=\"table wide\" style=\"margin-left: 0px;\">");
        tmpStr.AppendLine("<div class=\"tableBox\">");
        tmpStr.AppendLine(" <div class=\"tHead\">");
        tmpStr.AppendLine(" <ul>");
        tmpStr.AppendLine("  <li class=\"cell040 tal\">Destek ve Direnç</li>");
        tmpStr.AppendLine("  <li class=\"cell024\">S3</li>");
        tmpStr.AppendLine("  <li class=\"cell024\">S2</li>");
        tmpStr.AppendLine("  <li class=\"cell024\">S1</li>");
        tmpStr.AppendLine("  <li class=\"cell024\">Pivot</li>");
        tmpStr.AppendLine("  <li class=\"cell024\">R1</li>");
        tmpStr.AppendLine("  <li class=\"cell024\">R2</li>");
        tmpStr.AppendLine("  <li class=\"cell024\">R3</li>");
        tmpStr.AppendLine(" </ul>");
        tmpStr.AppendLine(" </div>");
        tmpStr.AppendLine(" <div class=\"tBody\">");
        tmpStr.AppendLine(" <ul>");
        tmpStr.AppendLine("   <li class=\"cell040 tal\"><b>Klasik</b></li>");
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiKlasikS3, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiKlasikS2, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiKlasikS1, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\"><b>{0}</b></li>", TextHelper.FormatNumber(pivotNoktasiKlasik, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiKlasikR1, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiKlasikR2, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiKlasikR3, 4));
        tmpStr.AppendLine("  </ul>");
        tmpStr.AppendLine("  <ul>");
        tmpStr.AppendLine("   <li class=\"cell040 tal\"><b>Fibonacci</b></li>");
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiFibonacciS3, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiFibonacciS2, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiFibonacciS1, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\"><b>{0}</b></li>", TextHelper.FormatNumber(pivotNoktasiFibonacci, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiFibonacciR1, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiFibonacciR2, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiFibonacciR3, 4));
        tmpStr.AppendLine("  </ul>");
        tmpStr.AppendLine("  <ul>");
        tmpStr.AppendLine("   <li class=\"cell040 tal\"><b>Camarilla</b></li>");
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiCamarillaS3, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiCamarillaS2, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiCamarillaS1, 4));
        tmpStr.AppendLine("   <li class=\"cell024\"><b>-</b></li>");
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiCamarillaR1, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiCamarillaR2, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiCamarillaR3, 4));
        tmpStr.AppendLine("  </ul>");
        tmpStr.AppendLine("  <ul>");
        tmpStr.AppendLine("   <li class=\"cell040 tal\"><b>Woodie</b></li>");
        tmpStr.AppendLine("   <li class=\"cell024\">-</li>");
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiWoodieS2, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiWoodieS1, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\"><b>{0}</b></li>", TextHelper.FormatNumber(pivotNoktasiWoodie, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiWoodieR1, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiWoodieR2, 4));
        tmpStr.AppendLine("   <li class=\"cell024\">-</li>");
        tmpStr.AppendLine("  </ul>");
        tmpStr.AppendLine("  <ul>");
        tmpStr.AppendLine("   <li class=\"cell040 tal\"><b>Demark</b></li>");
        tmpStr.AppendLine("   <li class=\"cell024\">-</li>");
        tmpStr.AppendLine("   <li class=\"cell024\">-</li>");
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(destekSeviyesiDemarkS1, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\"><b>{0}</b></li>", TextHelper.FormatNumber(pivotNoktasiDemark, 4));
        tmpStr.AppendLine();
        tmpStr.AppendFormat("   <li class=\"cell024\">{0}</li>", TextHelper.FormatNumber(direncSeviyesiDemarkR1, 4));
        tmpStr.AppendLine("   <li class=\"cell024\">-</li>");
        tmpStr.AppendLine("   <li class=\"cell024\">-</li>");
        tmpStr.AppendLine("  </ul>");
        tmpStr.AppendLine(" </div>");
        tmpStr.AppendLine("</div>");
        tmpStr.AppendLine("</div>");
        tmpStr.AppendLine("</div>");

        //System.IO.StreamWriter sr = new System.IO.StreamWriter("D:\\OTY.txt",true);
        //sr.Write(tmpStr.ToString());
        //sr.Close();

        return tmpStr.ToString();
    }

    private async Task<string> UcGunlukYukselisString(string sSembol, int sembolId)
    {
        StringBuilder tmpStr1 = new StringBuilder();


        //var grafikRows = (from o in eaMatriksEntities.GRAFIK_GUNLUK
        //                  join s in eaMatriksEntities.SEMBOLLER on o.SEMBOLID equals s.SEMBOLID
        //                  where s.SEMBOL == sSembol
        //                  orderby o.TARIH descending
        //                  select o).Take(3);

        var grafikRows = await _grafikService.GetTopGrafikGunluks(sembolId, 3);
        if (grafikRows.Any())
        {
            //Eğer 3 den az kayıt varsa işlemi yapmayalım
            if (grafikRows.Count() < 3) return "";

            double? grafikFirstRowKapanis = 0;
            double? grafikSecondRowKapanis = 0;
            double? grafikLastRowKapanis = 0;

            decimal? grafikFirstRowHacimLot = 0;
            decimal? grafikSecondRowHacimLot = 0;
            decimal? grafikLastRowHacimLot = 0;

            int i = 0;

            foreach (var items in grafikRows)
            {
                if (i == 0)
                {
                    grafikFirstRowKapanis = items.KAPANIS;
                    grafikFirstRowHacimLot = items.HACIMLOT;
                }
                if (i == 1)
                {
                    grafikSecondRowKapanis = items.KAPANIS;
                    grafikSecondRowHacimLot = items.HACIMLOT;
                }
                if (i == 2)
                {
                    grafikLastRowKapanis = items.KAPANIS;
                    grafikLastRowHacimLot = items.HACIMLOT;
                }

                i++;
            }

            if (grafikFirstRowKapanis > grafikSecondRowKapanis && grafikFirstRowKapanis > grafikLastRowKapanis)
            {
                if (grafikFirstRowHacimLot > grafikSecondRowHacimLot && grafikFirstRowHacimLot > grafikLastRowHacimLot)
                {
                    tmpStr1.AppendLine("Hisse senedi son üç gündür yükselirken, işlem hacminin de artışta olması teknik açıdan yükselişin hacimle desteklendiğini ve kısa vade için al sinyali üretilmeye başlandığını gösteriyor. ");
                }

                if (grafikFirstRowHacimLot < grafikSecondRowHacimLot && grafikFirstRowHacimLot < grafikLastRowHacimLot)
                {
                    tmpStr1.AppendLine("Hisse senedi son üç gündür yükselirken, işlem hacminin benzer şekilde seyretmemesi yükselişin hacimle desteklenmediğini gösteriyor ve teknik açıdan dikkatli olunmasını gerektiriyor. ");
                }
            }

            if (grafikFirstRowKapanis < grafikSecondRowKapanis && grafikFirstRowKapanis < grafikLastRowKapanis)
            {
                if (grafikFirstRowHacimLot > grafikSecondRowHacimLot && grafikFirstRowHacimLot > grafikLastRowHacimLot)
                {
                    tmpStr1.AppendLine("Hisse senedi son üç gündür değer kaybederken hacmin artıyor olması, hissede para çıkışı olduğunu ve teknik açıdan kısa vade için sat sinyalinin üretilmeye başlandığını gösteriyor. ");
                }

                if (grafikFirstRowHacimLot < grafikSecondRowHacimLot && grafikFirstRowHacimLot < grafikLastRowHacimLot)
                {
                    tmpStr1.AppendLine("Hisse senedi son üç gündür değer kaybederken hacmin de düşüyor olması, geri çekilmenin hacimsiz olduğunu gösteriyor ve sat sinyalini henüz teyit etmiyor. ");
                }
            }

            if (grafikFirstRowKapanis == grafikSecondRowKapanis && IsApproximatelyEqual(grafikFirstRowKapanis, grafikLastRowKapanis.GetValueOrDefault(0), 0.0))
            {
                if (grafikFirstRowHacimLot == grafikSecondRowHacimLot && grafikFirstRowHacimLot == grafikLastRowHacimLot)
                {
                    tmpStr1.AppendLine("Son üç gündür hisse senedi yatayda seyrediyor. ");
                }
            }
        }

        return tmpStr1.ToString();
    }

    private bool IsApproximatelyEqual(double? value1, double value2, double tolerance = 1e-10)
    {
        if (!value1.HasValue) return false;
        return Math.Abs(value1.Value - value2) < tolerance;
    }
}
