﻿using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.StoredProcedureResults;
using Bigpara.Domain.Widget;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs;

public class GetDovizRecurringJob : IRecurringJob
{
    private readonly ISeansRaporlariService _seansRaporlariService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<GetDovizRecurringJob> _logger;

    public GetDovizRecurringJob
    (
        ISeansRaporlariService seansRaporlariService,
        INotificationService notificationService,
        ILogger<GetDovizRecurringJob> logger
    )
    {
        _seansRaporlariService = seansRaporlariService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "GetDovizRecurringJob";
    public IEnumerable<string> Crons => ["*/15 9-17 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("GetDovizRecurringJob - Başladı ");
            await ProcessGetDoviz();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    public async Task ProcessGetDoviz()
    {
        if (!_seansRaporlariService.IsActive())
            return;

        try
        {
            var seansRaporuYuzeysel = await _seansRaporlariService.GetSeansRaporu15DkDoviz(WidgetSembol.USDTRY.Sembol);
            await CreateDovizRapor(seansRaporuYuzeysel, "Dolar", "XUSD");

            seansRaporuYuzeysel = await _seansRaporlariService.GetSeansRaporu15DkDoviz(WidgetSembol.EURTRY.Sembol);
            await CreateDovizRapor(seansRaporuYuzeysel, "Euro", "XEUR");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            //EmailHelper.SendEmail("GetDoviz", ex.Message);
            await _notificationService.NotifyErrorAsync($"GetDoviz", ex.Message);
        }
    }

    private async Task CreateDovizRapor(SeansRaporuDoviz seansRaporuYuzeysel, string sembolAciklama, string sembol)
    {
        if (seansRaporuYuzeysel == null) return;
        try
        {
            var seansRaporu = new SeansRaporu
            {
                HisseCumle = string.Format("{0} serbest piyasada {1} liradan (ALIŞ) işlem görüyor.", sembolAciklama, seansRaporuYuzeysel.ALIS),
                Yon = 0,
                HisseAdi = sembol,
                EklenmeTarihi = DateTime.Now
            };

            await _seansRaporlariService.CreateAndCacheSeansRaporu(seansRaporu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            // EmailHelper.SendEmail("GetDoviz", ex.Message);
            await _notificationService.NotifyErrorAsync($"CreateDovizRapor", ex.Message);
        }
    }
}
