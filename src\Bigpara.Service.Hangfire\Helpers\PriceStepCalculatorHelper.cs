﻿using System.Collections.Concurrent;
using System.Globalization;

namespace Bigpara.Service.Hangfire.Helpers;

public static class PriceStepCalculatorHelper
{
    private static readonly ConcurrentDictionary<string, List<(decimal minValue, decimal maxValue, decimal step)>> _cachedSteps
        = new ConcurrentDictionary<string, List<(decimal minValue, decimal maxValue, decimal step)>>();

    public static decimal GetPriceStep(string pttRow, decimal price)
    {
        if (string.IsNullOrEmpty(pttRow))
            return decimal.MinValue;

        var steps = _cachedSteps.GetOrAdd(pttRow, ParsePttRow);

        foreach (var step in steps)
        {
            if (price >= step.minValue && price <= step.maxValue)
            {
                return step.step;
            }
        }

        return steps.Last().step;
    }

    private static List<(decimal minValue, decimal maxValue, decimal step)> ParsePttRow(string pttRow)
    {
        var steps = new List<(decimal minValue, decimal maxValue, decimal step)>();
        var ranges = pttRow.Split(':');

        foreach (var range in ranges)
        {
            var parts = range.Split('|');
            if (parts.Length == 3)
            {
                try
                {
                    steps.Add((
                        decimal.Parse(parts[0], CultureInfo.InvariantCulture),
                        decimal.Parse(parts[1], CultureInfo.InvariantCulture),
                        decimal.Parse(parts[2], CultureInfo.InvariantCulture)
                    ));
                }
                catch (FormatException ex)
                {
                    Console.WriteLine($"Invalid price step format: {range}. Error: {ex.Message}");
                }
            }
        }

        return steps;
    }
}