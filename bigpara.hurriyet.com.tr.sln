﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35818.85
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "bigpara.hurriyet.com.tr.AppHost", "bigpara.hurriyet.com.tr.AppHost\bigpara.hurriyet.com.tr.AppHost.csproj", "{68AFD628-83A5-410C-B41E-2118BC0F4018}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "bigpara.hurriyet.com.tr.ServiceDefaults", "bigpara.hurriyet.com.tr.ServiceDefaults\bigpara.hurriyet.com.tr.ServiceDefaults.csproj", "{44B736DA-5071-B4CB-ADB8-EE54D0052D6F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructures", "Infrastructures", "{48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{34F1E216-297D-4E65-9934-00D878FB2FBD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Persistence.Tests", "src\Tests\Bigpara.Persistence.Tests\Bigpara.Persistence.Tests.csproj", "{E93F8A7B-B03C-3631-3BFB-59457A320E71}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Service.Api.Tests", "src\Tests\Bigpara.Service.Api.Tests\Bigpara.Service.Api.Tests.csproj", "{C0C2B31F-3752-1245-5E4D-DF0633758F65}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Cache.Tests", "src\Tests\Bigpara.Cache.Tests\Bigpara.Cache.Tests.csproj", "{D7B3D083-8F87-38E9-0DC9-A0E1B38DE285}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentations", "Presentations", "{4ADBDFDD-8FC0-4068-B90A-D0BB56A97AB2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Application", "src\Infrastructures\Bigpara.Application\Bigpara.Application.csproj", "{EE550F6F-867D-3E52-1AE0-D84FF68A2729}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Domain", "src\Infrastructures\Bigpara.Domain\Bigpara.Domain.csproj", "{BF3152C8-8C84-D1B0-BBDA-7027D9835F7A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Domain.Services", "src\Infrastructures\Bigpara.Domain.Services\Bigpara.Domain.Services.csproj", "{3B67D8AA-F41D-C8D7-6F8B-857FA4A0C238}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Applicaitons", "Applicaitons", "{29402B7C-1763-41D7-9DE3-30761498AAA0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Service.Api", "src\Presentations\Bigpara.Service.Api\Bigpara.Service.Api.csproj", "{B405C2A5-16E5-B5FA-5AC6-8D0FF9236DB5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Service.Hangfire", "src\Presentations\Bigpara.Service.Hangfire\Bigpara.Service.Hangfire.csproj", "{0A14F6DC-CAFD-F1C1-BC52-F12C310E5C8C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Service.Realtime", "src\Presentations\Bigpara.Service.Realtime\Bigpara.Service.Realtime.csproj", "{B86216D3-9867-12B5-73AF-EB4669AC6ED3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Infrastructure.Cache", "src\Infrastructures\Bigpara.Infrastructure.Cache\Bigpara.Infrastructure.Cache.csproj", "{97153A47-987C-9B30-5E2A-D003E4574D8D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Infrastructure.Cache.Memory", "src\Infrastructures\Bigpara.Infrastructure.Cache.Memory\Bigpara.Infrastructure.Cache.Memory.csproj", "{16D4C6B9-591E-E483-7264-6D8145F8845B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Infrastructure.Cache.Redis", "src\Infrastructures\Bigpara.Infrastructure.Cache.Redis\Bigpara.Infrastructure.Cache.Redis.csproj", "{31D8A869-AB2A-04C1-7F05-BBBB00A5559E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Infrastructure.External.Quark", "src\Infrastructures\Bigpara.Infrastructure.External.Quark\Bigpara.Infrastructure.External.Quark.csproj", "{799150FE-BDF8-2138-D9D6-8031AFA73880}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Infrastructure.Logging", "src\Infrastructures\Bigpara.Infrastructure.Logging\Bigpara.Infrastructure.Logging.csproj", "{A0E496F5-649A-B864-C1D5-71358CCC4113}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Infrastructure.Notifications", "src\Infrastructures\Bigpara.Infrastructure.Notifications\Bigpara.Infrastructure.Notifications.csproj", "{3A5A1BD4-346E-066A-5C77-8D27F872D5A8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Infrastructure.Persistence", "src\Infrastructures\Bigpara.Infrastructure.Persistence\Bigpara.Infrastructure.Persistence.csproj", "{7B0A87E1-4579-1094-372B-137580FCFD96}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Infrastructure.Persistence.MongoDB", "src\Infrastructures\Bigpara.Infrastructure.Persistence.MongoDB\Bigpara.Infrastructure.Persistence.MongoDB.csproj", "{1C2F0977-9F64-18E2-A4FC-8B18FEB1269A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Infrastructure.Persistence.PostgreSql", "src\Infrastructures\Bigpara.Infrastructure.Persistence.PostgreSql\Bigpara.Infrastructure.Persistence.PostgreSql.csproj", "{F79F4D0A-2116-56C2-6031-DEFBD1BCCA2D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Infrastructure.Persistence.SqlServer", "src\Infrastructures\Bigpara.Infrastructure.Persistence.SqlServer\Bigpara.Infrastructure.Persistence.SqlServer.csproj", "{548B83D5-AE75-2F57-6A16-E504BC5ECA68}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bigpara.Application.Contracts", "src\Infrastructures\Bigpara.Application.Contracts\Bigpara.Application.Contracts.csproj", "{175ED3B1-FF0D-4C0E-88D6-54EF35B7AFF2}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{68AFD628-83A5-410C-B41E-2118BC0F4018}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{68AFD628-83A5-410C-B41E-2118BC0F4018}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{68AFD628-83A5-410C-B41E-2118BC0F4018}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{68AFD628-83A5-410C-B41E-2118BC0F4018}.Release|Any CPU.Build.0 = Release|Any CPU
		{44B736DA-5071-B4CB-ADB8-EE54D0052D6F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44B736DA-5071-B4CB-ADB8-EE54D0052D6F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44B736DA-5071-B4CB-ADB8-EE54D0052D6F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44B736DA-5071-B4CB-ADB8-EE54D0052D6F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E93F8A7B-B03C-3631-3BFB-59457A320E71}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E93F8A7B-B03C-3631-3BFB-59457A320E71}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E93F8A7B-B03C-3631-3BFB-59457A320E71}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E93F8A7B-B03C-3631-3BFB-59457A320E71}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0C2B31F-3752-1245-5E4D-DF0633758F65}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0C2B31F-3752-1245-5E4D-DF0633758F65}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0C2B31F-3752-1245-5E4D-DF0633758F65}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0C2B31F-3752-1245-5E4D-DF0633758F65}.Release|Any CPU.Build.0 = Release|Any CPU
		{D7B3D083-8F87-38E9-0DC9-A0E1B38DE285}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D7B3D083-8F87-38E9-0DC9-A0E1B38DE285}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D7B3D083-8F87-38E9-0DC9-A0E1B38DE285}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D7B3D083-8F87-38E9-0DC9-A0E1B38DE285}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE550F6F-867D-3E52-1AE0-D84FF68A2729}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE550F6F-867D-3E52-1AE0-D84FF68A2729}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE550F6F-867D-3E52-1AE0-D84FF68A2729}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE550F6F-867D-3E52-1AE0-D84FF68A2729}.Release|Any CPU.Build.0 = Release|Any CPU
		{BF3152C8-8C84-D1B0-BBDA-7027D9835F7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BF3152C8-8C84-D1B0-BBDA-7027D9835F7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BF3152C8-8C84-D1B0-BBDA-7027D9835F7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BF3152C8-8C84-D1B0-BBDA-7027D9835F7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B67D8AA-F41D-C8D7-6F8B-857FA4A0C238}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B67D8AA-F41D-C8D7-6F8B-857FA4A0C238}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B67D8AA-F41D-C8D7-6F8B-857FA4A0C238}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B67D8AA-F41D-C8D7-6F8B-857FA4A0C238}.Release|Any CPU.Build.0 = Release|Any CPU
		{B405C2A5-16E5-B5FA-5AC6-8D0FF9236DB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B405C2A5-16E5-B5FA-5AC6-8D0FF9236DB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B405C2A5-16E5-B5FA-5AC6-8D0FF9236DB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B405C2A5-16E5-B5FA-5AC6-8D0FF9236DB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{0A14F6DC-CAFD-F1C1-BC52-F12C310E5C8C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0A14F6DC-CAFD-F1C1-BC52-F12C310E5C8C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0A14F6DC-CAFD-F1C1-BC52-F12C310E5C8C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0A14F6DC-CAFD-F1C1-BC52-F12C310E5C8C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B86216D3-9867-12B5-73AF-EB4669AC6ED3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B86216D3-9867-12B5-73AF-EB4669AC6ED3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B86216D3-9867-12B5-73AF-EB4669AC6ED3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B86216D3-9867-12B5-73AF-EB4669AC6ED3}.Release|Any CPU.Build.0 = Release|Any CPU
		{97153A47-987C-9B30-5E2A-D003E4574D8D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{97153A47-987C-9B30-5E2A-D003E4574D8D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{97153A47-987C-9B30-5E2A-D003E4574D8D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{97153A47-987C-9B30-5E2A-D003E4574D8D}.Release|Any CPU.Build.0 = Release|Any CPU
		{16D4C6B9-591E-E483-7264-6D8145F8845B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{16D4C6B9-591E-E483-7264-6D8145F8845B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{16D4C6B9-591E-E483-7264-6D8145F8845B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{16D4C6B9-591E-E483-7264-6D8145F8845B}.Release|Any CPU.Build.0 = Release|Any CPU
		{31D8A869-AB2A-04C1-7F05-BBBB00A5559E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{31D8A869-AB2A-04C1-7F05-BBBB00A5559E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{31D8A869-AB2A-04C1-7F05-BBBB00A5559E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{31D8A869-AB2A-04C1-7F05-BBBB00A5559E}.Release|Any CPU.Build.0 = Release|Any CPU
		{799150FE-BDF8-2138-D9D6-8031AFA73880}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{799150FE-BDF8-2138-D9D6-8031AFA73880}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{799150FE-BDF8-2138-D9D6-8031AFA73880}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{799150FE-BDF8-2138-D9D6-8031AFA73880}.Release|Any CPU.Build.0 = Release|Any CPU
		{A0E496F5-649A-B864-C1D5-71358CCC4113}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A0E496F5-649A-B864-C1D5-71358CCC4113}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A0E496F5-649A-B864-C1D5-71358CCC4113}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A0E496F5-649A-B864-C1D5-71358CCC4113}.Release|Any CPU.Build.0 = Release|Any CPU
		{3A5A1BD4-346E-066A-5C77-8D27F872D5A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3A5A1BD4-346E-066A-5C77-8D27F872D5A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3A5A1BD4-346E-066A-5C77-8D27F872D5A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3A5A1BD4-346E-066A-5C77-8D27F872D5A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{7B0A87E1-4579-1094-372B-137580FCFD96}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B0A87E1-4579-1094-372B-137580FCFD96}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B0A87E1-4579-1094-372B-137580FCFD96}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B0A87E1-4579-1094-372B-137580FCFD96}.Release|Any CPU.Build.0 = Release|Any CPU
		{1C2F0977-9F64-18E2-A4FC-8B18FEB1269A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1C2F0977-9F64-18E2-A4FC-8B18FEB1269A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1C2F0977-9F64-18E2-A4FC-8B18FEB1269A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1C2F0977-9F64-18E2-A4FC-8B18FEB1269A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F79F4D0A-2116-56C2-6031-DEFBD1BCCA2D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F79F4D0A-2116-56C2-6031-DEFBD1BCCA2D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F79F4D0A-2116-56C2-6031-DEFBD1BCCA2D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F79F4D0A-2116-56C2-6031-DEFBD1BCCA2D}.Release|Any CPU.Build.0 = Release|Any CPU
		{548B83D5-AE75-2F57-6A16-E504BC5ECA68}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{548B83D5-AE75-2F57-6A16-E504BC5ECA68}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{548B83D5-AE75-2F57-6A16-E504BC5ECA68}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{548B83D5-AE75-2F57-6A16-E504BC5ECA68}.Release|Any CPU.Build.0 = Release|Any CPU
		{175ED3B1-FF0D-4C0E-88D6-54EF35B7AFF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{175ED3B1-FF0D-4C0E-88D6-54EF35B7AFF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{175ED3B1-FF0D-4C0E-88D6-54EF35B7AFF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{175ED3B1-FF0D-4C0E-88D6-54EF35B7AFF2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{48FAF25F-88F8-4BB9-A8E5-A52F74A2B191} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{34F1E216-297D-4E65-9934-00D878FB2FBD} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{E93F8A7B-B03C-3631-3BFB-59457A320E71} = {34F1E216-297D-4E65-9934-00D878FB2FBD}
		{C0C2B31F-3752-1245-5E4D-DF0633758F65} = {34F1E216-297D-4E65-9934-00D878FB2FBD}
		{D7B3D083-8F87-38E9-0DC9-A0E1B38DE285} = {34F1E216-297D-4E65-9934-00D878FB2FBD}
		{4ADBDFDD-8FC0-4068-B90A-D0BB56A97AB2} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{EE550F6F-867D-3E52-1AE0-D84FF68A2729} = {29402B7C-1763-41D7-9DE3-30761498AAA0}
		{BF3152C8-8C84-D1B0-BBDA-7027D9835F7A} = {29402B7C-1763-41D7-9DE3-30761498AAA0}
		{3B67D8AA-F41D-C8D7-6F8B-857FA4A0C238} = {29402B7C-1763-41D7-9DE3-30761498AAA0}
		{29402B7C-1763-41D7-9DE3-30761498AAA0} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{B405C2A5-16E5-B5FA-5AC6-8D0FF9236DB5} = {4ADBDFDD-8FC0-4068-B90A-D0BB56A97AB2}
		{0A14F6DC-CAFD-F1C1-BC52-F12C310E5C8C} = {4ADBDFDD-8FC0-4068-B90A-D0BB56A97AB2}
		{B86216D3-9867-12B5-73AF-EB4669AC6ED3} = {4ADBDFDD-8FC0-4068-B90A-D0BB56A97AB2}
		{97153A47-987C-9B30-5E2A-D003E4574D8D} = {48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}
		{16D4C6B9-591E-E483-7264-6D8145F8845B} = {48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}
		{31D8A869-AB2A-04C1-7F05-BBBB00A5559E} = {48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}
		{799150FE-BDF8-2138-D9D6-8031AFA73880} = {48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}
		{A0E496F5-649A-B864-C1D5-71358CCC4113} = {48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}
		{3A5A1BD4-346E-066A-5C77-8D27F872D5A8} = {48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}
		{7B0A87E1-4579-1094-372B-137580FCFD96} = {48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}
		{1C2F0977-9F64-18E2-A4FC-8B18FEB1269A} = {48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}
		{F79F4D0A-2116-56C2-6031-DEFBD1BCCA2D} = {48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}
		{548B83D5-AE75-2F57-6A16-E504BC5ECA68} = {48FAF25F-88F8-4BB9-A8E5-A52F74A2B191}
		{175ED3B1-FF0D-4C0E-88D6-54EF35B7AFF2} = {29402B7C-1763-41D7-9DE3-30761498AAA0}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BEBF892D-7E5A-4C4A-947D-E924FF2BF515}
	EndGlobalSection
EndGlobal
