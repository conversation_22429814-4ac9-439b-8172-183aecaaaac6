﻿namespace Bigpara.Domain.Bigpara;

public class UserPayment
{
    public string Id { get; set; }
    public string Subscription_Domain { get; set; }
    public DateTime Start_Date { get; set; }
    public DateTime End_Date { get; set; }
    public string User_Id { get; set; }
    public string Crm_Product_Id { get; set; }
    public string Domain_Product_Id { get; set; }
}

public class UserPaymentRoot
{
    public string status { get; set; }
    public List<object> messages { get; set; }
    public List<UserPayment> data { get; set; }
}
