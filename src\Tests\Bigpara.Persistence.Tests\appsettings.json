{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"Matriks": "Server=***************;Database=DBMATRIKSv2;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;", "Bigpara": "Server=***************;Database=TestBigpara;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;", "Foreks": "Server=***************;Database=TestForeks;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;", "Redis": "***************:6379,password=BIGpara!com!tr!2014!"}}