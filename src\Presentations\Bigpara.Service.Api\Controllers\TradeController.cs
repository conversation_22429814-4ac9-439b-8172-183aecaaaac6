﻿using Bigpara.Application.Features.Grafiks.Queries;
using Bigpara.Application.Features.Yuzeysels.Queries;
using Bigpara.Cache.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Bigpara.Service.Api.Controllers
{
    [Route("api/trade")]
    [ApiController]
    public class TradeController : ControllerBase
    {
        public readonly ILogger<TradeController> _logger;
        private readonly ICacheService _cacheService;
        private readonly IMediator _mediator;

        public TradeController(ILogger<TradeController> logger, ICacheService cacheService, IMediator mediator)
        {
            _logger = logger;
            _cacheService = cacheService;
            _mediator = mediator;

        }

        [HttpGet]
        [Route("current")]
        public async Task<IActionResult> GetStocks([FromQuery] GetGrafik5DkQuery query)
        {

            var result = await _mediator.Send(query);

            return Ok(result);
        }

        [HttpGet]
        [Route("daily")]
        public async Task<IActionResult> GetStocks([FromQuery] GetGrafikGunlukQuery query)
        {
            var result = await _mediator.Send(query);

            return Ok(result);
        }

        [HttpGet]
        [Route("table")]
        public async Task<IActionResult> GetCurrencies([FromQuery] GetGrafik5DkQuery query)
        {
            var result = await _mediator.Send(query);

            return Ok(result);
        }

        [HttpGet]
        [Route("performance")]
        public async Task<IActionResult> GetPerformance([FromQuery] GetYuzeyselPerformanceQuery query)
        {
            var result = await _mediator.Send(query);

            return Ok(result);
        }

        [HttpGet]
        [Route("trend")]
        public async Task<IActionResult> GetTrend([FromQuery] GetYuzeyselTrendQuery query)
        {
            var result = await _mediator.Send(query);

            return Ok(result);
        }
    }
}
