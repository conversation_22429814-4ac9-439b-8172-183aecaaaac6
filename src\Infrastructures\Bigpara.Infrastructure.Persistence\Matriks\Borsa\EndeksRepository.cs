﻿using Bigpara.Application.Contracts.Repositories.Matriks.Borsa;
using Bigpara.Domain.Matriks;
using Microsoft.Data.SqlClient;

namespace Bigpara.Persistence.Matriks.Borsa;

public class EndeksRepository : IEndeksRepository
{
    private readonly IMatriksDbContext _matriksDbContext;

    public EndeksRepository
    (
        IMatriksDbContext matriksDbContext
    )
    {
        _matriksDbContext = matriksDbContext;
    }

    public async Task<List<EndeksYuzeysel>> GetEndeksYuzeysel()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<EndeksYuzeysel>("bp.pGetEndeksDegerleri") ?? new List<EndeksYuzeysel>();
    }

    public async Task<List<EndeksYuzeysel>> GetEndeksDegerleriBySembol(string sembol)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("sembol",sembol),
        };

        return await _matriksDbContext.ExecuteStoredProcedureAsync<EndeksYuzeysel>("bp.pGetEndeksDegerleriBySembol", parameters.ToArray());
    }

    public async Task<List<EndeksYuzeysel>> GetEndeksYuzeyselArtanAzalan()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<EndeksYuzeysel>("bp.pGetEndeksDegerleriArtanAzalan");
    }
}
