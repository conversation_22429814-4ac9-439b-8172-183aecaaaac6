﻿using Bigpara.Domain.Matriks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;
using OfficeOpenXml;
using System.Data;

namespace Bigpara.Service.Hangfire.Jobs;

public class Sektors
{
    public int Id { get; set; }
    public string Name { get; set; }
}

public class MaliOranImportRecurringJob : IRecurringJob
{
    private readonly IOranlarSektorelService _oranlarSektorelService;
    private readonly ILogger<MaliOranImportRecurringJob> _logger;
    private readonly INotificationService _notificationService;
    private readonly ISystemSettingsService _systemSettingsService;

    public MaliOranImportRecurringJob
    (
        IOranlarSektorelService oranlarSektorelService,
        ILogger<MaliOranImportRecurringJob> logger,
        INotificationService notificationService,
        ISystemSettingsService systemSettingsService
    )
    {
        _oranlarSektorelService = oranlarSektorelService;
        _logger = logger;
        _notificationService = notificationService;
        _systemSettingsService = systemSettingsService;
    }

    public string Name => "MaliOranImportService";
    public IEnumerable<string> Crons => ["45 1 * * 2-6"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("MaliOranImportService - Başladı ");
            await ProcessMaliOranImportService();
        }
        catch (Exception exp)
        {
            _logger.LogError(exp.Message);
            //TODO : Check
            //EmailHelper.SendEmail("Video", exp.Message);
            await _notificationService.NotifyErrorAsync("Video", exp.Message);
        }
    }

    public async Task ProcessMaliOranImportService()
    {
        try
        {
            // ExcelPackage.License.SetNonCommercialOrganization("My Noncommercial organization");
            //TODO :  Check
            string excelFileName = (await _systemSettingsService.GetSystemSettingsValueFromCacheAsync("FileConfiguration", "OranSektorelFileName")).Value;

            string fileName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, excelFileName);

            var oranlarSektorels = new List<OranlarSektorel>();
            List<Sektors> sektorIds = new List<Sektors>();

            if (!File.Exists(fileName))
                _logger.LogError("Oran Sektorel" + fileName + " file not exists");
            using (var package = new ExcelPackage())
            {
                package.Load(new FileStream(fileName, FileMode.Open, FileAccess.Read));

                var currentWorksheet = package.Workbook.Worksheets["_Sektorel"];
                var startRow = 1;
                for (var rowNumber = startRow + 1; rowNumber <= currentWorksheet.Dimension.End.Row; rowNumber++)
                {
                    sektorIds.Add(new Sektors
                    {
                        Id = Convert.ToInt32(currentWorksheet.Cells[rowNumber, 1].Value),
                        Name = currentWorksheet.Cells[rowNumber, 2].Value.ToString(),
                    });
                }
                int nSektorId;
                var drSektorId = 1;
                var drSektorler = sektorIds.Where(r => r.Name == "Sektör").Select(r => r).ToArray();
                nSektorId = drSektorler.First().Id;

                for (var rowNumber = startRow + 1; rowNumber <= currentWorksheet.Dimension.End.Row; rowNumber++)
                {
                    int id = Convert.ToInt32(currentWorksheet.Cells[rowNumber, 1].Value);
                    if (id > nSektorId)
                    {
                        nSektorId = Convert.ToInt32(drSektorler[drSektorId].Id);
                        drSektorId++;
                    }

                    var oranlarSektorel = new OranlarSektorel
                    {
                        Id = Convert.ToInt32(currentWorksheet.Cells[rowNumber, 1].Value),
                        UpdatedDateTime = DateTime.Now,
                        Sektor_ID = nSektorId,
                        Senet = currentWorksheet.Cells[rowNumber, 2].Value.ToString(),
                        Donem = currentWorksheet.Cells[rowNumber, 3].Value.ToString(),
                        F_K = (currentWorksheet.Cells[rowNumber, 4].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 4].Value) : (double?)null,
                        FKOrant = (currentWorksheet.Cells[rowNumber, 5].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 5].Value) : (double?)null,
                        PD_DD = (currentWorksheet.Cells[rowNumber, 6].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 6].Value) : (double?)null,
                        NetKar_TopAktif = (currentWorksheet.Cells[rowNumber, 7].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 7].Value) : (double?)null,
                        NetKar_Ozsermaye = (currentWorksheet.Cells[rowNumber, 8].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 8].Value) : (double?)null,
                        PD_NetSatis = (currentWorksheet.Cells[rowNumber, 9].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 9].Value) : (double?)null,
                        PD_EFaalKar = (currentWorksheet.Cells[rowNumber, 10].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 10].Value) : (double?)null,
                        NetKar_NetSatis = (currentWorksheet.Cells[rowNumber, 11].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 11].Value) : (double?)null,
                        EFaalKar_NetSatis = (currentWorksheet.Cells[rowNumber, 12].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 12].Value) : (double?)null,
                        Cari_Oran = (currentWorksheet.Cells[rowNumber, 13].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 13].Value) : (double?)null,
                        EFaalKar_KVBorc = (currentWorksheet.Cells[rowNumber, 14].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 14].Value) : (double?)null,
                        KVBorc_TopAktif = (currentWorksheet.Cells[rowNumber, 15].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 15].Value) : (double?)null,
                        KVBor_TopBorc = (currentWorksheet.Cells[rowNumber, 16].Value != DBNull.Value) ? Convert.ToDouble(currentWorksheet.Cells[rowNumber, 16].Value) : (double?)null,
                    };
                    oranlarSektorels.Add(oranlarSektorel);
                }

            }
            if (oranlarSektorels.Any())
            {
                _oranlarSektorelService.DeleteOranSektorel();

                foreach (var oranlarSektorel in oranlarSektorels)
                {
                    _oranlarSektorelService.InsertOranSektorel(oranlarSektorel);
                }
            }

            await Task.CompletedTask;
        }
        catch (Exception exp)
        {
            _logger.LogError(exp.Message);
            //TODO : Check
            //EmailHelper.SendEmail("Video", exp.Message);
            await _notificationService.NotifyErrorAsync("Video", exp.Message);
        }
    }
}
