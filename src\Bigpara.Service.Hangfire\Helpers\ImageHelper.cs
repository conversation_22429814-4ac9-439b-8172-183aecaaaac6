﻿

using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Helpers;

public static class ImageHelper
{
    public static async Task<string> GetImageUrl(IList<Domain.Bigpara.File> files, ISystemSettingsService systemSettingsService, int width = 0, int height = 0, int index = 0, string options = null)
    {
        var serviceUrl = (await systemSettingsService.GetSystemSettingsValueFromCacheAsync("BigparaJobs", "ImageServerUrl")).Value;
        var noImageId = (await systemSettingsService.GetSystemSettingsValueFromCacheAsync("BigparaJobs", "NoImageId")).Value;

        if (files != null && files.Count > index)
        {
            var ext = System.IO.Path.GetExtension(TextHelper.CleanIllegalFileNameChars(files[index].FileName));
            if (!string.IsNullOrWhiteSpace(ext)) ext = ext.ToLower();
            if (options != null)
            {
                return string.Format("{0}/{1}x{2}/{3}/{4}{5}", serviceUrl, width, height, options, files[index]._Id, ext);
            }

            return string.Format("{0}/{1}x{2}/{3}{4}", serviceUrl, width, height, files[index]._Id, ext);
        }

        return string.Format("{0}/{1}x{2}/{3}", serviceUrl, width, height, noImageId);
    }
}

