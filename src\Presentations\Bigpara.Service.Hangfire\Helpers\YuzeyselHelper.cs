﻿namespace Bigpara.Service.Hangfire.Helpers;

public static class YuzeyselHelper
{
    private static readonly string[] TargetIndexWeights = { "XU100" };

    public static decimal? GetXU100AGFromJson(Dictionary<string, Dictionary<string, decimal>>? indexWeight)
    {
        if (indexWeight == null || !indexWeight.Any())
            return null;

        var targetIndex = TargetIndexWeights.FirstOrDefault(index => index == "XU100" && indexWeight.ContainsKey(index));

        return targetIndex != null ? indexWeight[targetIndex]["wd"] : null;
    }
}
