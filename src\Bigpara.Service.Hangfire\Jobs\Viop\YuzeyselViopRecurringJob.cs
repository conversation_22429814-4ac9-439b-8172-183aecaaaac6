﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Viop;

public class YuzeyselViopRecurringJob : IRecurringJob
{
    private readonly ForeksHttpClient _foreksHttpClient;
    private readonly ILogger<YuzeyselViopRecurringJob> _logger;
    private readonly IConfiguration _configuration;
    private readonly IViopService _viopService;
    private readonly INotificationService _notificationService;
    private readonly int _maxConcurrentTasks;

    public YuzeyselViopRecurringJob(
        ForeksHttpClient foreksHttpClient,
        ILogger<YuzeyselViopRecurringJob> logger,
        IConfiguration configuration,
        IViopService viopService,
        INotificationService notificationService)
    {
        _foreksHttpClient = foreksHttpClient;
        _logger = logger;
        _configuration = configuration;
        _viopService = viopService;
        _notificationService = notificationService;
        _maxConcurrentTasks = _configuration.GetValue("YuzeyselViopProcessing:MaxConcurrentTasks", 10);
    }

    public string Name => "YuzeyselViopRecurringJob";
    public IEnumerable<string> Crons => ["0 0/2 * * * ?"];

    public async Task ExecuteAsync()
    {
        try
        {
            await ProcessDataTypeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{Name} sırasında kritik hata oluştu.");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    public async Task ProcessDataTypeAsync()
    {
        try
        {
            var url = $"{_configuration["Foreks:SnapShotApi"]}/?{_configuration["Foreks:SnapShot:Viop:Url"]}";
            if (string.IsNullOrEmpty(url))
            {
                _logger.LogError("Foreks:SnapShot:Viop:Url değeri bulunamadı.");
                await _notificationService.NotifyErrorAsync(Name, "Foreks:SnapShot:Viop:Url değeri bulunamadı.");
                return;
            }

            var result = await _foreksHttpClient.FetchDataAsync<YuzeyselDto>(url);
            if (result == null || result.Count == 0)
            {
                _logger.LogWarning("Çekilen veri boş veya null.");
                return;
            }

            using var semaphore = new SemaphoreSlim(_maxConcurrentTasks);
           
            foreach (var item in result)
            {
                await semaphore.WaitAsync();

                try
                {
                    await ProcessSingleItemAsync(item);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"İşlem sırasında hata oluştu: {item.SEMBOL}");
                    await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.SEMBOL} , Hata: {ex} , Hata Mesajı: {ex.Message}");
                }
                finally
                {
                    semaphore.Release();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    private async Task ProcessSingleItemAsync(YuzeyselDto item)
    {
        try
        {
            await _viopService.Change(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {item.SEMBOL}");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.SEMBOL} , Hata: {ex} , Hata Mesajı: {ex.Message}");
        }
    }
}
