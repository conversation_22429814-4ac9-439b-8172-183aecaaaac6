﻿using Bigpara.Cache.Interfaces;
using Bigpara.Cache.Redis.Extensions;
using Bigpara.Cache.Memory.Extensions;
using Bigpara.Cache.Redis.Services;
using Bigpara.Persistence;
using Bigpara.Persistence.Matriks.Borsa;
using Bigpara.Persistence.Matriks.Grafiks;
using Bigpara.Persistence.Matriks.Sektor;
using Bigpara.Persistence.Matriks.Sembol;
using Bigpara.Persistence.Matriks.Yuzeysels;
using Bigpara.Persistence.SqlServer.Extensions;
using Bigpara.Application.Contracts;
using Bigpara.Cache.Memory.Services;
using Bigpara.Application.Features.Grafiks.Queries;
using Bigpara.Services.Matriks.Piyasalar;
using Bigpara.Logging.Extensions;
using Bigpara.External.Quark.Extensions;
using Bigpara.Service.Api.Extensions;
using Microsoft.OpenApi.Models;
using Bigpara.Persistence.BigparaDB.UserSymbols;
using Bigpara.Persistence.BigparaDB.Users;
using Bigpara.Application.Common;
using Bigpara.Application.Features.Grafiks.Queries.ViewModels;
using Bigpara.Application.Contracts.Repositories.Matriks.Piyasalar;
using Bigpara.Application.Contracts.Repositories.BigparaDB.Users;
using Bigpara.Application.Contracts.Repositories.Matriks.Borsa;
using Bigpara.Application.Contracts.Repositories.Matriks.Sektor;
using Bigpara.Application.Contracts.Repositories.Matriks.Yuzeysels;
using Bigpara.Application.Contracts.Repositories.Matriks.Sembol;
using Bigpara.Application.Contracts.Repositories.BigparaDB.UserSymbols;
using Bigpara.Application.Contracts.Repositories.Matriks.Grafiks;


var builder = WebApplication.CreateBuilder(args);
builder.AddServiceDefaults();

builder.Services.AddHttpContextAccessor();
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<Grafik5DkViewModel>());

builder.Services.AddCustomMemoryCache();
builder.Services.AddCustomRedisCache(builder.Configuration);
// Add services to the container.

builder.Services.AddCustomSqlServer<IMatriksDbContext, MatriksDbContext>(builder.Configuration, "Matriks");
builder.Services.AddCustomSqlServer<IBigparaDbContext, BigparaDbContext>(builder.Configuration, "Bigpara");

builder.Services.AddScoped(typeof(IGenericRepository<,>), typeof(GenericRepository<,>));
builder.Services.AddScoped<IGrafikRepository, GrafikRepository>();
builder.Services.AddScoped<IHisseRepository, HisseRepository>();
builder.Services.AddScoped<IYuzeyselRepository, YuzeyselRepository>();
builder.Services.AddScoped<ISektorRepository, SektorRepository>();
builder.Services.AddScoped<ISembolRepository, SembolRepository>();
builder.Services.AddScoped<IOranlarSektorelRepository, OranlarSektorelRepository>();
builder.Services.AddScoped<IUserSymbolRepository, UserSymbolRepository>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IPiyasaRepository, PiyasaRepository>();
builder.Services.AddScoped<IRedisCacheService, RedisCacheService>();
builder.Services.AddScoped<ICacheService, MemoryCacheService>();
builder.Services.AddQuarkCms();
builder.Services.AddControllers();
builder.Services.AddVoltran(builder.Environment, builder.Configuration);
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "My API", Version = "v1" });

    // JWT için Security tanımı
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        In = ParameterLocation.Header,
        Description = "Please enter JWT with Bearer into field. Example: Bearer {token}",
        Name = "Authorization",
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    // Global olarak tüm endpoint'lere uygula (opsiyonel)
    c.AddSecurityRequirement(new OpenApiSecurityRequirement()
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },
                Scheme = "oauth2",
                Name = "Bearer",
                In = ParameterLocation.Header,
            },
            new List<string>()
        }
    });
});
builder.Services.UseCustomLogging(builder.Configuration);

var app = builder.Build();

app.MapDefaultEndpoints();

// Configure the HTTP request pipeline.
//if (app.Environment.IsDevelopment())
//{
    app.UseSwagger();
    app.UseSwaggerUI();
//}

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

await app.RunAsync();
