{
  "Version": "1.0.0",
  "SeriLog": {
    "Url": "http://192.168.114.124:9200",
    "Platform": "Bigpara.Service.Hangfire",
    "Env": "Prod"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "Foreks": "",
    "Hangfire": "",
    "Redis": "192.168.113.232:6379,password=BIGpara!com!tr!2014!"
  },
  "Bigpara": {
    "Socket": "http://bigpara-realtime-signalr",
    "Api": "http://bigpara-realtime-signalr"
  },
  "Foreks": {
    "SnapShotApi": "https://snapshot.foreks.com",
    "Socket": "http://",
    "CloudApi": "http://",
    "Auth": {
      "RealTimeUsername": "hurriyetbigparasnapshot",
      "RealTimePassword": "",
      "DelayedUsername": "hurriyetbigpara",
      "DelayedPassword": "",
      "WebUsername": "bigpara-web",
      "WebPassword": ""
    },
    "SnapShot": {
      "Hisse": {
        "Url": "domain=BIST&exchange=BIST&marketSector=Equity&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030,MarketSector"
      },
      "Endeks": {
        "Url": "domain=BIST&exchange=BIST&marketSector=Index&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030,MarketSector"
      },
      "Parite": {
        "Url": "exchange=SPOT&securityType=Cross&marketSector=Currency&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
        //"Url": "code=EURUSD,EURGBP,CHFJPY,GBPJPY,USDJPY,EURJPY,USDSAR,EURAUD,EURCAD,USDGBP,AUDJPY,GBPCAD,CADJPY,CADCHF,AUDCHF,AUDGBP,GBPAUD,EURSAR,AUDUSD,GBPCHF,EURTRL,USDTRL,XAUUSD&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      },
      "Viop": {
        "Url": "domain=VIOP&exchange=BIST&securityType=Future&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      },
      "SerbestPiyasa": {
        "Url": "exchange=GrandBazaar&securityType=Cross&marketSector=Currency&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
        //"Url": "code=SGLD,EURGLD,USGLDKG,SEUR,SGYARIM,SGCEYREK,XSLV,DVZSP1,AKYNBIL22,SG14BIL,SG18BIL,SG22BIL,SGATA,XGLD,R_O_N&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      }
    }
  },
  "Feed": {
    "SymbolApi": "https://feed-definition.foreks.com/symbol/search",
    "SektorApi": "https://feed-definition.foreks.com/sector/search?status=ACTIVE",
    "Parite": {
      "Url": "legacyCode=EUR/USD&legacyCode=EUR/GBP&legacyCode=CHF/JPY&legacyCode=GBP/JPY&legacyCode=USD/JPY&legacyCode=EUR/JPY&legacyCode=USD/SAR&legacyCode=EUR/AUD&legacyCode=EUR/CAD&legacyCode=USD/GBP&legacyCode=AUD/JPY&legacyCode=GBP/CAD&legacyCode=CAD/JPY&legacyCode=CAD/CHF&legacyCode=AUD/CHF&legacyCode=AUD/GBP&legacyCode=GBP/AUD&legacyCode=EUR/SAR&legacyCode=AUD/USD&legacyCode=GBP/CHF&legacyCode=EUR/TRL&legacyCode=USD/TRL&legacyCode=XAU/USD",
      "Id": "3"
    },
    "Hisse": {
      "Url": "domain=BIST&exchange=BIST&marketSector=Equity",
      "Id": "4"
    },
    "Tahvil": {
      "Url": "domain=BAP&exchange=BIST&status=ACTIVE",
      "Id": "5"
    },
    "TCMB": {
      "Url": "exchange=TCMB&status=ACTIVE",
      "Id": "6"
    },
    "Viop": {
      "Url": "domain=VIOP&exchange=BIST",
      "Id": "9"
    },
    "DunyaEndeksleri": {
      "Url": "tag=INDICES&status=ACTIVE",
      "Id": "23"
    },
    "FuturesDelayed": {
      "Url": "domain=VIOP&exchange=BIST&marketSector=Equity&status=ACTIVE",
      "Id": "24"
    },
    "SerbestPiyasa": {
      "Url": "legacyCode=SGLD&legacyCode=EURGLD&legacyCode=USGLDKG&legacyCode=SEUR&legacyCode=SGYARIM&legacyCode=SGCEYREK&legacyCode=XSLV&legacyCode=DVZSP1&legacyCode=AKYNBIL22&legacyCode=SG14BIL&legacyCode=SG18BIL&legacyCode=SG22BIL&legacyCode=SGATA&legacyCode=XGLD&legacyCode=R_O_N",
      "Id": "26"
    },
    "Endeks": {
      "Url": "domain=BIST&exchange=BIST&marketSector=Index",
      "Id": "27"
    }
  }
}
