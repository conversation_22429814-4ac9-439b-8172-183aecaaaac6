﻿using Quark.Core.Helpers;
using Quark.Models.Entities;
using System.Text.RegularExpressions;

namespace Bigpara.Service.Hangfire.Helpers;

public static class ContentHelper
{
    public static List<ContentTag> CreateContentTag(List<string> tags)
    {
        if (tags == null || !tags.Any())
            return new List<ContentTag>();

        var tagList = tags
            .Where(tag => !string.IsNullOrWhiteSpace(tag))
            .Distinct()
            .Select(tag => new ContentTag
            {
                IxName = tag.Slugify(),
                Name = tag
            })
            .ToList();

        return tagList;
    }

    public static string CreateContentSlug(string documentTitle, long iId)
    {
        var returnValue = string.Empty;
        if (string.IsNullOrWhiteSpace(documentTitle))
        {
            return returnValue;
        }
        if (documentTitle.Length > 47)
        {
            documentTitle = documentTitle.Substring(0, 47);
        }
        documentTitle = Regex.Replace(documentTitle, "[‘’“”'\"]", string.Empty);
        documentTitle = documentTitle.CleanForUrl();
        returnValue = $"{documentTitle}_ID{iId}/";
        return returnValue;
    }
}

