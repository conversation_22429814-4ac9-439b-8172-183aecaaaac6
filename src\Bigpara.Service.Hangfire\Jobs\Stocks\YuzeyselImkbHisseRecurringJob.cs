﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients.Interfaces;
using Bigpara.Service.Hangfire.Helpers;

namespace Bigpara.Service.Hangfire.Jobs.Stocks;

public class YuzeyselImkbHisseRecurringJob : IRecurringJob
{
    private readonly ForeksHttpClient _foreksHttpClient;

    private readonly ILogger<YuzeyselImkbHisseRecurringJob> _logger;
    private readonly IConfiguration _configuration;
    private readonly IHisseService _hisseService;
    private readonly INotificationService _notificationService;
    private readonly int _maxConcurrentTasks;

    public YuzeyselImkbHisseRecurringJob(
        ForeksHttpClient foreksHttpClient,
        ILogger<YuzeyselImkbHisseRecurringJob> logger,
        IConfiguration configuration,
        IHisseService hisseService,
        INotificationService notificationService)
    {
        _foreksHttpClient = foreksHttpClient;
        _logger = logger;
        _configuration = configuration;
        _hisseService = hisseService;
        _notificationService = notificationService;
        _maxConcurrentTasks = _configuration.GetValue("ImkbHisseProcessing:MaxConcurrentTasks", 10);
    }

    public string Name => "YuzeyselImkbHisseRecurringJob";
    public IEnumerable<string> Crons => ["0 0/2 * * * ?"];

    public async Task ExecuteAsync()
    {
        try
        {
            await ProcessDataTypeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{Name} sırasında kritik hata oluştu.");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    public async Task ProcessDataTypeAsync()
    {
        try
        {
            var url = $"{_configuration["Foreks:SnapShotApi"]}/?{_configuration["Foreks:SnapShot:Hisse:Url"]}";
            if (string.IsNullOrEmpty(url))
            {
                _logger.LogError("Foreks:SnapShot:Hisse:Url değeri bulunamadı.");
                await _notificationService.NotifyErrorAsync(Name, "Foreks:SnapShot:Hisse:Url değeri bulunamadı.");
                return;
            }

            var result = await _foreksHttpClient.FetchDataAsync<YuzeyselDto>(url);
            if (result == null || result.Count == 0)
            {
                _logger.LogWarning("Çekilen veri boş veya null.");
                return;
            }

            using var semaphore = new SemaphoreSlim(_maxConcurrentTasks);

            foreach (var item in result)
            {
                await semaphore.WaitAsync();

                try
                {
                    await ProcessSingleItemAsync(item);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"İşlem sırasında hata oluştu: {item.SEMBOL}");
                    await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.SEMBOL} , Hata: {ex} , Hata Mesajı: {ex.Message}");
                }
                finally
                {
                    semaphore.Release();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    private async Task ProcessSingleItemAsync(YuzeyselDto item)
    {
        try
        {
            await _hisseService.Change(item);
            //var dic = item.ToDictionary();
            //await _hisseService.SendRealTimeApi(dic);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {item.SEMBOL}");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.SEMBOL} , Hata: {ex} , Hata Mesajı: {ex.Message}");
        }
    }
}
