﻿using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Bitcoin;
using Bigpara.Domain.ExtendedTypes;
using Bigpara.Service.Hangfire.Services.Interfaces;
using Bigpara.Application.Contracts.Repositories.Bitcoin;

namespace Bigpara.Service.Hangfire.Services;

public class BitcoinService : IBitcoinService
{
    #region Constants

    private const string BITCOIN_HISTORY = "bigpara.bitcoin.history";
    private const string BITCOIN_HISTORY_BY_SEMBOL = "bigpara.bitcoin.history.by.sembol.{0}";
    private const string BITCOIN_HISTORY_LASTDAY_BY_SEMBOL = "bigpara.bitcoin.history.lastday.by.sembol.{0}";
    private const string BITCOIN_HISTORY_BY_SEMBOL_DATE = "bigpara.bitcoin.history.by.sembol.date.{0}-{1}";
    private const string BITCOIN_BITFINEX_ALL = "bigpara.bitcoin.bitfinex.all";
    private const string BITCOIN_BITFINEX_BY_SEMBOL = "bigpara.bitcoin.bitfinex.by.sembol.{0}";

    #endregion
    private readonly IBitcoinRepository _bitcoinRepository;
    private readonly IRedisCacheService _redisCacheService;

    public BitcoinService(IBitcoinRepository bitcoinRepository, IRedisCacheService redisCacheService)
    {
        _bitcoinRepository = bitcoinRepository;
        _redisCacheService = redisCacheService;
    }

    public async Task<List<MarketsOnlineDetails>> GetAllMarketOnlines()
    {
        string key = string.Format(BITCOIN_HISTORY);

        return _redisCacheService.GetRedisList(key, () => _bitcoinRepository.GetAllBitcoinHistory().GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_DURATION).ToList();
    }

    public async Task<List<MarketsHistory>> GetMarketHistoryBySymbol(string symbol)
    {
        string key = string.Format(BITCOIN_HISTORY_BY_SEMBOL, symbol);

        return _redisCacheService.GetRedisList(key, () => _bitcoinRepository.GetMarketsOnlinesBySymbol(symbol).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_DURATION).ToList();
    }

    public async Task<MarketsOnline> GetMarketsOnlineBySymbolAndDate(string symbol, DateTime dt)
    {
        //string key = string.Format(BITCOIN_HISTORY_BY_SEMBOL_DATE, symbol, dt.Ticks);
        return await _bitcoinRepository.GetMarketsOnlineBySymbolAndDate(symbol, dt);
    }

    public async Task<int> AddMarketsOnline(MarketsOnline marketsOnline)
    {
        return await _bitcoinRepository.AddMarketsOnline(marketsOnline);
    }

    public async Task<int> UpdateMarketsOnline(MarketsOnline marketsOnline)
    {
        return await _bitcoinRepository.UpdateMarketsOnline(marketsOnline);
    }

    public async Task<List<BitfinexOnline>> GetAllBitfinexOnlines()
    {
        var cacheKey = string.Format(BITCOIN_BITFINEX_ALL);

        return _redisCacheService.GetRedisList(cacheKey, () => _bitcoinRepository.GetAllBitfinexOnlines().GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_15SEC_DURATION).ToList();
    }

    public async Task<List<BitfinexOnline>> GetBitfinexOnlineLastValueBySymbol(string symbol)
    {
        var cacheKey = string.Format(BITCOIN_BITFINEX_BY_SEMBOL, symbol);

        return _redisCacheService.GetRedisList(cacheKey, () => _bitcoinRepository.GetBitfinexOnlineLastValueBySymbol(symbol).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_15SEC_DURATION).ToList();
    }

    public async Task<int> AddBitfinexOnline(BitfinexOnline bitfinexOnline)
    {
        return await _bitcoinRepository.AddBitfinexOnline(bitfinexOnline);
    }

    public async Task<List<MarketsOnline>> GetMarketsOnlineLastDay(string symbol)
    {
        string key = string.Format(BITCOIN_HISTORY_LASTDAY_BY_SEMBOL, symbol);

        return _redisCacheService.GetRedisList(key, () => _bitcoinRepository.GetMarketsOnlineLastDay(symbol).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_DURATION).ToList();
    }
}
