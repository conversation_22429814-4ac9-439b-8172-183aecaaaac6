﻿using Bigpara.Application.Contracts.Repositories.MetaData;
using Bigpara.Domain.Bigpara;
using Microsoft.Data.SqlClient;

namespace Bigpara.Persistence.MetaData;

public class MetaDataRepository : IMetaDataRepository
{
    private readonly IBigparaDbContext _bigparaDbContext;

    public MetaDataRepository
    (
        IBigparaDbContext bigparaDbContext
    )
    {
        _bigparaDbContext = bigparaDbContext;
    }

    public async Task<List<Country>> GetCountries()
    {
        var headLineList = await _bigparaDbContext.ExecuteStoredProcedureAsync<Country>("spGetCountries");

        return headLineList ?? new List<Country>();
    }

    public async Task<List<City>> GetCities()
    {
        var headLineList = await _bigparaDbContext.ExecuteStoredProcedureAsync<City>("spGetCities");

        return headLineList ?? new List<City>();
    }

    public async Task<List<City>> GetCitiesByCountryId(int id)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("countryId", id),
        };

        var headLineList = await _bigparaDbContext.ExecuteStoredProcedureAsync<City>("spGetCitiesByCountryId", parameters.ToArray());

        return headLineList ?? new List<City>();
    }
}
