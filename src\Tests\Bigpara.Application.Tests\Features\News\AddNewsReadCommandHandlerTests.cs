using Bigpara.Application.Features.News.Commands;
using Bigpara.Application.Tests.Common;
using Xunit;

namespace Bigpara.Application.Tests.Features.News;

public class AddNewsReadCommandHandlerTests : TestBase
{
    [Fact]
    public async Task Handle_Valid_Command_Should_Return_Success_Response()
    {
        // Arrange
        var handler = new AddNewsReadCommandHandler();
        var command = new AddNewsReadCommand { id = "123" };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<BaseResponse>(result);
    }

    [Theory]
    [InlineData("1")]
    [InlineData("999")]
    [InlineData("12345")]
    public async Task Handle_Different_Valid_Ids_Should_Return_Success_Response(string newsId)
    {
        // Arrange
        var handler = new AddNewsReadCommandHandler();
        var command = new AddNewsReadCommand { id = newsId };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<BaseResponse>(result);
    }

    [Fact]
    public async Task Handle_Should_Complete_Within_Reasonable_Time()
    {
        // Arrange
        var handler = new AddNewsReadCommandHandler();
        var command = new AddNewsReadCommand { id = "123" };
        var timeout = TimeSpan.FromSeconds(1);

        // Act
        var task = handler.Handle(command, CancellationToken.None);
        var completed = await Task.WhenAny(task, Task.Delay(timeout));

        // Assert
        Assert.Equal(task, completed);
        var result = await task;
        Assert.NotNull(result);
    }
}
