﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Service.Hangfire.Services.Interfaces;

public interface IOranlarSektorelService
{
    Task<List<OranlarSektorel>> GetSektorelMaliOranlar();
    Task<List<OranlarSektorel>> GetOranlarSektorels(string sembol);
    Task<List<OranlarSektorel>> GetOranlarSektorelId(int id);
    Task<int> InsertOranSektorel(OranlarSektorel oranlarSektorel);
    Task<int> DeleteOranSektorel();
}
