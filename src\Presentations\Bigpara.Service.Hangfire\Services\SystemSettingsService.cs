﻿using Bigpara.Application.Contracts.Repositories.SystemSettings;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.SystemSettings;
using Bigpara.Service.Hangfire.Services.Interfaces;
using System.Globalization;

namespace Bigpara.Service.Hangfire.Services;

public class SystemSettingsService : ISystemSettingsService
{
    private readonly ISystemSettingsRepository _systemSettingsRepository;
    private const int CacheDurationInSeconds = 60 * 60;
    private readonly ICacheService _cacheService;

    public SystemSettingsService
    (
        ISystemSettingsRepository systemSettingsRepository,
        ICacheService cacheService
    )
    {
        _systemSettingsRepository = systemSettingsRepository;
        _cacheService = cacheService;
    }

    /// <summary>
    /// Sistem ayarı için cache anahtarını oluşturur.
    /// </summary>
    private string GetCacheKey(string owner, string key) => $"s{SiteId}{owner}{key}";

    private static byte _siteId;

    /// <summary>
    /// Site kimliği. İlk erişimde 1 olarak atanır.
    /// </summary>
    private static byte SiteId => _siteId == 0 ? _siteId = 1 : _siteId;


    /// <summary>
    /// Belirtilen owner ve key'e göre sistem ayarını getirir. Önce cache kontrol edilir, yoksa veritabanından alınır ve cache'e yazılır.
    /// </summary>
    /// <param name="owner">Ayarın ait olduğu uygulama/modül.</param>
    /// <param name="key">Ayar anahtarı.</param>
    /// <returns>Sistem parametresi bilgisi.</returns>
    public async Task<SystemParameterData> GetSystemSettingsValueAsync(string owner, string key)
    {
        var cacheKey = GetCacheKey(owner, key);
        SystemParameterData parameter = null;

        try
        {
            parameter = _cacheService.Get<SystemParameterData>(cacheKey);

            if (parameter == null)
            {
                parameter = await _systemSettingsRepository.GetSystemSettingsValueAsync(owner, key);
                if (parameter != null)
                {
                    _cacheService.Set(cacheKey, parameter, CacheDurationInSeconds);
                }
            }
        }
        catch (Exception)
        {
        }

        return parameter;
    }

    /// <summary>
    /// Önceden cache’e alınmış tüm sistem ayarları üzerinden belirtilen ayarı döner. Veritabanı çağrısı yapılmaz.
    /// </summary>
    /// <param name="owner">Ayarın ait olduğu uygulama/modül.</param>
    /// <param name="key">Ayar anahtarı.</param>
    /// <returns>Sistem parametresi bilgisi (bulunamazsa boş nesne döner).</returns>
    public async Task<SystemParameterData> GetSystemSettingsValueFromCacheAsync(string owner, string key)
    {
        var allSettings = await GetAllSystemSettingsAsync();
        return allSettings.FirstOrDefault(x => x.Owner == owner && x.ConfigKey == key) ?? new SystemParameterData();
    }


    /// <summary>
    /// Tüm sistem ayarlarını veritabanından alır ve cache’e yazar. Cache’te varsa oradan döner.
    /// </summary>
    /// <returns>Tüm sistem ayarlarının listesi.</returns>
    private async Task<List<SystemParameterData>> GetAllSystemSettingsAsync()
    {
        const string cacheKey = "SystemSettings:All";
        var cached = _cacheService.Get<List<SystemParameterData>>(cacheKey);

        if (cached != null)
            return cached;

        var result = await _systemSettingsRepository.GetAllSystemSettingsAsync();

        if (result != null && result.Count > 0)
        {
            _cacheService.Set(cacheKey, result, 60 * 60); // 1 saat cache
        }

        return result;
    }

    /// <summary>
    /// Belirtilen sistem ayarını istenen türde döndürür. Gerekirse tür dönüşümü yapılır.
    /// DateTime tipleri için özel formatlar desteklenir.
    /// </summary>
    /// <typeparam name="T">Beklenen veri tipi.</typeparam>
    /// <param name="owner">Ayarın ait olduğu uygulama/modül.</param>
    /// <param name="key">Ayar anahtarı.</param>
    /// <returns>Ayar değeri istenen türde döner. Hatalıysa default(T) döner.</returns>
    public async Task<T> GetSettingValueAsync<T>(string owner, string key)
    {
        var parameter = await GetSystemSettingsValueAsync(owner, key);

        if (string.IsNullOrEmpty(parameter?.Value) || string.IsNullOrEmpty(parameter.Type))
            return default;

        if (typeof(T) == typeof(DateTime))
        {
            if (DateTime.TryParseExact(parameter.Value, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dt1))
                return (T)(object)dt1;
            if (DateTime.TryParseExact(parameter.Value, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dt2))
                return (T)(object)dt2;
            if (DateTime.TryParseExact(parameter.Value, "dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dt3))
                return (T)(object)dt3;
        }

        return (T)Convert.ChangeType(parameter.Value, typeof(T), CultureInfo.InvariantCulture);
    }
}
