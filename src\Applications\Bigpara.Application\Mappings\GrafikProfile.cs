﻿using AutoMapper;
using Bigpara.Domain.Matriks;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Mappings
{
    public class GrafikProfile : Profile
    {
        public GrafikProfile()
        {
            CreateMap<Grafik, Dtos.GrafikDto>()

                .ForMember(dest => dest.SEMBOL, opt => opt.MapFrom(src => src.SEMBOL))
                .ForMember(dest => dest.TARIH, opt => opt.MapFrom(src => src.TARIH))
                .ForMember(dest => dest.ACILIS, opt => opt.MapFrom(src => src.ACILIS))
                .ForMember(dest => dest.KAPANIS, opt => opt.MapFrom(src => src.KAPANIS))
                .ForMember(dest => dest.HACIMLOT, opt => opt.MapFrom(src => src.HACIMLOT))
                .ForMember(dest => dest.AORT, opt => opt.MapFrom(src => src.AORT));

            CreateMap<GrafikGunluk, Dtos.GrafikDto>()

                .ForMember(dest => dest.SEMBOL, opt => opt.MapFrom(src => src.SEMBOL))
                .ForMember(dest => dest.TARIH, opt => opt.MapFrom(src => src.TARIH))
                .ForMember(dest => dest.ACILIS, opt => opt.MapFrom(src => src.ACILIS))
                .ForMember(dest => dest.KAPANIS, opt => opt.MapFrom(src => src.KAPANIS))
                .ForMember(dest => dest.HACIMLOT, opt => opt.MapFrom(src => src.HACIMLOT))
                .ForMember(dest => dest.AORT, opt => opt.MapFrom(src => src.AORT))
                .ForMember(dest => dest.HACIMTL, opt => opt.MapFrom(src => src.HACIMTL));
        }
    }
}
