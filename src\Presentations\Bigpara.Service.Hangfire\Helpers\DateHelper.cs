﻿namespace Bigpara.Service.Hangfire.Helpers;

public static class DateHelper
{
    public static DateTime UnixTimeToLocalDateTime(double miliseconds)
    {
        var unixStartTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
        var utcTime = unixStartTime.AddMilliseconds(miliseconds);
        return UtcDateTimeToTurkishLocalDateTime(utcTime);
    }

    public static DateTime UtcDateTimeToTurkishLocalDateTime(DateTime utcDateTime)
    {
        return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime,
            TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time"));
    }
}
