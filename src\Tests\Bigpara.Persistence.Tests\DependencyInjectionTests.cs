namespace Bigpara.Persistence.Tests
{
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Configuration;
    using Xunit;
    using Bigpara.Persistence;
    using Bigpara.Persistence.SqlServer.Extensions;
    using Bigpara.Domain;
    using Microsoft.EntityFrameworkCore;
    using Bigpara.Application.Common;

    public class DependencyInjectionTests : IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration;

        public DependencyInjectionTests()
        {
            _configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .Build();

            _serviceProvider = BuildServiceProvider();
        }

        [Fact]
        public void Should_Resolve_MatriksDbContext_And_BigparaDbContext()
        {
            // Act & Assert - MatriksDbContext
            var matriksDb = _serviceProvider.GetService<IMatriksDbContext>();
            Assert.NotNull(matriksDb);
            Assert.IsType<MatriksDbContext>(matriksDb);

            // Act & Assert - BigparaDbContext
            var bigparaDb = _serviceProvider.GetService<IBigparaDbContext>();
            Assert.NotNull(bigparaDb);

            // Act & Assert - Generic Repository Tests
            AssertGenericRepositoryResolution();
            AssertSembollerRepositories();
        }

        [Fact]
        public void Should_Resolve_MatriksDbContext_And_SqlProcedureExecution()
        {
            // Act & Assert
            var matriksDb = _serviceProvider.GetService<IMatriksDbContext>();

           
            Assert.NotNull(matriksDb);

            var matriksSembols = matriksDb.ExecuteQueryAsync<Semboller>("exec [bp].[pGetHisseListesi]")
                .GetAwaiter()
                .GetResult();

            Assert.Equal(615, matriksSembols.Count());
        }

        private IServiceProvider BuildServiceProvider()
        {
            var services = new ServiceCollection();

            // Extension method'lar ile servisleri ekle
            services.AddCustomSqlServer<IMatriksDbContext, MatriksDbContext>(_configuration, "Matriks");
            services.AddCustomSqlServer<IBigparaDbContext, BigparaDbContext>(_configuration, "Bigpara");
            services.AddScoped(typeof(IGenericRepository<,>), typeof(GenericRepository<,>));

            return services.BuildServiceProvider();
        }

        private void AssertGenericRepositoryResolution()
        {
            var genericRepo = _serviceProvider.GetService(typeof(IGenericRepository<Sektorler, MatriksDbContext>));
            Assert.NotNull(genericRepo);
        }

        private void AssertSembollerRepositories()
        {
            // Matriks Sembol Repository Test
            var matriksSembolRepository = _serviceProvider.GetService<IGenericRepository<Semboller, MatriksDbContext>>();
            Assert.NotNull(matriksSembolRepository);
            Assert.IsType<GenericRepository<Semboller, MatriksDbContext>>(matriksSembolRepository);

            var matriksSembols = matriksSembolRepository.FindAll();
            Assert.Equal(114813, matriksSembols.Count());

            // Bigpara Sembol Repository Test
            var bigparaSembolRepository = _serviceProvider.GetService<IGenericRepository<Semboller, BigparaDbContext>>();
            Assert.NotNull(bigparaSembolRepository);
            Assert.IsType<GenericRepository<Semboller, BigparaDbContext>>(bigparaSembolRepository);

            var bigparaSembols = bigparaSembolRepository.FindAll();
            Assert.Equal(114813, bigparaSembols.Count());
        }

        public void Dispose()
        {
            if (_serviceProvider is IDisposable disposableProvider)
            {
                disposableProvider.Dispose();
            }
        }
    }
}