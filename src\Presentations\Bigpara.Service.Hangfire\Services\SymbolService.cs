﻿using Bigpara.Cache.Interfaces;
using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Domain;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructure.Data;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;
public class SymbolService : ISymbolService
{
    private readonly ForeksHttpClient _foreksHttpClient;
    private readonly ICacheService _cacheService;
    private readonly IStoredProcedureParameterService _storedProcedureService;
    private readonly ILogger<SymbolService> _logger;
    private readonly IConfiguration _configuration;
    private readonly int _getCacheDuration;
    private readonly int _setCacheDuration;

    public SymbolService
    (
        ForeksHttpClient foreksHttpClient,
        ICacheService cacheProvider,
        IConfiguration configuration,
        IStoredProcedureParameterService storedProcedureService,
        ILogger<SymbolService> logger
    )
    {
        _foreksHttpClient = foreksHttpClient;
        _cacheService = cacheProvider;
        _configuration = configuration;
        _storedProcedureService = storedProcedureService;
        _logger = logger;

        //Dakika cinsinden önbellek sürelerini al, yoksa varsayılan olarak 60 dakika al
        _getCacheDuration = _configuration.GetValue("CacheDuration:SymbolGet", 60);
        _setCacheDuration = _configuration.GetValue("CacheDuration:SymbolSet", 60);
    }

    /// <summary>
    /// Verilen sembol koduna göre önbellekten veya API'dan sembol bilgisini alır.
    /// </summary>
    /// <param name="symbolCode">Sembol kodu (örneğin: "XU100")</param>
    /// <returns>Sembol bilgisini içeren SymbolDto veya null</returns>
    public async Task<SymbolsCacheDto?> GetSymbolByCodeAsync(string symbolCode)
    {
        var model = await _cacheService.GetAsync(symbolCode, _getCacheDuration, async () =>
        {
            var symbolUrl = $"{_configuration["Feed:SymbolApi"]}?code={symbolCode}";
            var fetchedSymbols = await _foreksHttpClient.FetchDataAsync<ForeksSembolDto>(symbolUrl);

            return fetchedSymbols.Select(s => new SymbolsCacheDto
            {
                Sembol = s
            }).FirstOrDefault();
        });

        return model;
    }

    /// <summary>
    /// Verilen sembol koduna göre önbellekten veya API'dan sembol bilgisini alır.
    /// </summary>
    /// <param name="symbolCode">Sembol kodu (örneğin: "XU100")</param>
    /// <returns>Sembol bilgisini içeren SymbolDto veya null</returns>
    public async Task<SymbolsCacheDto?> GetSymbolByIdAsync(string _id)
    {
        var model = await _cacheService.GetAsync(_id, _getCacheDuration, async () =>
        {
            var symbolUrl = $"{_configuration["Feed:SymbolApi"]}?_id={_id}";
            var fetchedSymbols = await _foreksHttpClient.FetchDataAsync<ForeksSembolDto>(symbolUrl);

            return fetchedSymbols.Select(s => new SymbolsCacheDto
            {
                Sembol = s
            }).FirstOrDefault();
        });

        return model;
    }

    public async Task<SymbolsCacheDto?> GetSymbolByLegacyCodeAsync(string legacyCode)
    {
        var model = await _cacheService.GetAsync(legacyCode, _getCacheDuration, async () =>
        {
            var symbolUrl = $"{_configuration["Feed:SymbolApi"]}?legacyCode={legacyCode}";
            var fetchedSymbols = await _foreksHttpClient.FetchDataAsync<ForeksSembolDto>(symbolUrl);

            return fetchedSymbols.Select(s => new SymbolsCacheDto
            {
                Sembol = s
            }).FirstOrDefault();
        });

        return model;
    }

    /// <summary>
    /// Verilen sembolü önbelleğe kaydeder.
    /// </summary>
    /// <param name="symbol">Önbelleğe kaydedilecek sembol nesnesi</param>
    /// <param name="cacheDuration">Önbellekte kalma süresi</param>
    public Task SetSymbolAsync(string cacheKey, ForeksSembolDto symbol)
    {
        _cacheService.Get(cacheKey, _setCacheDuration, () =>
        {
            var symbolDto = new SymbolsCacheDto
            {
                Sembol = symbol
            };

            return symbolDto;
        });

        return Task.CompletedTask;
    }

    public async Task Change(ForeksSembolDto symbol, int feedItemId, List<Sektorler> allSektorler)
    {
        var matchedSektor = allSektorler.FirstOrDefault(s => s.StrKod == symbol.Sector);
        if (matchedSektor != null)
        {
            symbol.SektorId = matchedSektor.StrSektorId;
        }
        // Sembol üzerindeki socketId gibi bilgileri günceller
        var updateParameters = _storedProcedureService.UpdateSembolIfEmptylStoredProcedureParameters(symbol);
        await _storedProcedureService.ExecuteStoredProcedureAsync("sp_UpdateSembolIfEmpty", updateParameters, symbol.Sembol);

        if (feedItemId != 9)
        {
            symbol.StrPiyasa = SembolHelper.GetStrPiyasa(symbol.MarketSector, symbol.Market, symbol.SecurityType,
                        symbol.ImkbHisseTipString, symbol.Domain);

            if (feedItemId == 23)
            {
                symbol.StrPiyasa = "IMKB_ENDEKS";
            }


            if (string.IsNullOrWhiteSpace(symbol.StrPiyasa))
            {
                _logger.LogInformation($"StrPiyasa hiçbiri ile eşleşmediği için {symbol.Sembol} için özel bir stored procedure çağrılmadı.");
                return;
            }
        }

        if (!string.IsNullOrWhiteSpace(symbol.StatusString) && symbol.StatusString == "ACTIVE")
        {
            symbol.Aktif = true;
        }

        // VİOP için endeks işleme
        if (symbol.Endeks != null && symbol.Endeks.Any() && feedItemId != 4)
        {
            symbol.StrEndeks = SembolHelper.GetStrEndeks(symbol.Endeks);
        }

        if (feedItemId == 9)
        {
            symbol.StrBoard = SembolHelper.FindStrBoard(symbol.ForeksKodYeni);
            if (string.IsNullOrEmpty(symbol.StrBoard))
                return;
        }

        await SetSymbolAsync(symbol.Sembol, symbol);

        Console.WriteLine($"{feedItemId} Piyasa id'li, Sembol {symbol.Sembol}, tarih ==> {DateTime.Now}");

        if (feedItemId == 9)
        {
            var parametersViop = _storedProcedureService.CreateViopSymbolStoredProcedureParameters(symbol);
            await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_ImkbVipContractInfo", parametersViop, symbol.Sembol);
        }
        else
        {
            var parameters = _storedProcedureService.CreateSymbolStoredProcedureParameters(symbol);
            await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_TanimSembol", parameters, symbol.Sembol);
        }
    }

    public async Task ChangePassive(string strKod)
    {
        await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_TanimSembol_Pasif", _storedProcedureService.CreatePassiveSymbolStoredProcedureParameters(strKod), strKod);
    }
}