﻿using Bigpara.Service.Realtime.Hubs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;

namespace Bigpara.Service.Realtime.Controllers
{
    [ApiController]
    public class TradeController : ControllerBase
    {
        private readonly IHubContext<TradeHub> _hubContext;
        private readonly IConfiguration _configuration;
        public TradeController(IHubContext<TradeHub> hubContext, IConfiguration configuration)
        {
            _hubContext = hubContext;
            _configuration = configuration;
        }


        [HttpPost]
        [Route("api/trade")]
        public async Task<IActionResult> PostTradeData([FromBody] Dictionary<string, string> tradeData)
        {
            if (tradeData == null || tradeData.Count == 0)
                return BadRequest("Geçersiz trade verisi");

            await _hubContext.Clients.Group($"symbol_chart_{tradeData["SEMBOL"]}").SendAsync("ReceiveChart", tradeData);
            //Test
            await _hubContext.Clients.Group($"symbol_table_all").SendAsync("ReceiveChart", tradeData);
            if (tradeData.ContainsKey("Type") && !string.IsNullOrEmpty(tradeData["Type"]))
            {
                await _hubContext.Clients.Group($"symbol_detail_{tradeData["SEMBOL"]}").SendAsync("ReceiveDetail", tradeData);
                await _hubContext.Clients.Group($"symbol_table_{tradeData["Type"]}_all").SendAsync("ReceiveChart", tradeData);
            }
            return Ok(new { status = "success", message = "Trade verisi iletildi" });
        }

        [HttpGet]
        [Route("api/version")]
        public ActionResult Version() {

            return Ok(_configuration["version"]);
        }





    }
}