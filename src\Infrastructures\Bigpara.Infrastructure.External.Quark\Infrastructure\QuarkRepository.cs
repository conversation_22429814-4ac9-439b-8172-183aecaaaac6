﻿using Bigpara.External.Quark.Filters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.External.Quark.Infrastructure
{
    public class QuarkRepository<T, TDto> : IQuarkRepository<T, TDto>
    {
        private readonly IQuarkClient _quarkClient;
        public QuarkRepository(IQuarkClient quarkClient)
        {
            _quarkClient = quarkClient;
        }
        /// <summary>
        /// Istek yapilacak Api url'ini belirler
        /// </summary>
        public string BaseApiUrl { get; set; }

        /// <summary>
        /// Istenilen TDto nesnesini getirir 
        /// </summary>
        /// <param name="id">Content id</param>
        /// <param name="select">Geri donmesini istediginiz ozellikler</param>
        /// <returns>TDto</returns>
        public TDto GetById(string id, string select = null)
        {
            Uri uri;
            if (string.IsNullOrWhiteSpace(select))
            {
                uri = new Uri($"{BaseApiUrl}/{id}", UriKind.Relative);
            }
            else
            {
                uri = new Uri($"{BaseApiUrl}/{id}?$select={select}", UriKind.Relative);
            }

            return _quarkClient.Get<TDto>(uri);
        }

        /// <summary>
        /// Istenilen sorguya gore listeyi getirir 
        /// </summary>
        /// <param name="id">Content id</param>
        /// <param name="filter">OData sorgusu icin kullanilacak filtreler</param>
        /// <returns>IEnumerable liste getirir</returns>
        public IList<T> GetListById(string id, ODataFilter filter)
        {
            var odataAndQuery = CreateODataAndQuery(filter);

            var uri = new Uri($"{BaseApiUrl}/{id}?{odataAndQuery}", UriKind.Relative);

            return _quarkClient.Get<IList<T>>(uri);
        }

        /// <summary>
        /// Istenilen TDto nesnesini getirir 
        /// </summary>
        /// <param name="filter">OData sorgusu icin kullanilacak filtreler</param>
        /// <returns>TDto</returns>
        public TDto GetDtoByFilter(ODataFilter filter)
        {
            var odataAndQuery = CreateODataAndQuery(filter, disableTop: true);

            Uri uri;
            if (string.IsNullOrWhiteSpace(filter?.Method))
            {
                uri = new Uri($"{BaseApiUrl}/?{odataAndQuery}", UriKind.Relative);
            }
            else
            {
                uri = new Uri($"{BaseApiUrl}/{filter.Method}()?{odataAndQuery}", UriKind.Relative);
            }

            return _quarkClient.Get<TDto>(uri);
        }

        /// <summary>
        /// Istenilen T nesnesini getirir 
        /// </summary>
        /// <param name="filter">OData sorgusu icin kullanilacak filtreler</param>
        /// <returns>TDto</returns>
        public T GetByFilter(ODataFilter filter)
        {
            var odataAndQuery = CreateODataAndQuery(filter, disableTop: true);

            Uri uri;
            if (string.IsNullOrWhiteSpace(filter?.Method))
            {
                uri = new Uri($"{BaseApiUrl}/?{odataAndQuery}", UriKind.Relative);
            }
            else
            {
                uri = new Uri($"{BaseApiUrl}/{filter.Method}()?{odataAndQuery}", UriKind.Relative);
            }

            return _quarkClient.Get<T>(uri);
        }

        /// <summary>
        /// Istenilen sorguya gore listeyi getirir 
        /// </summary>
        /// <param name="filter">OData sorgusu icin kullanilacak filtreler</param>
        /// <returns>IEnumerable liste getirir</returns>
        public IList<T> List(ODataFilter filter)
        {
            var odataAndQuery = CreateODataAndQuery(filter);

            Uri uri;
            if (string.IsNullOrWhiteSpace(filter?.Method))
            {
                uri = new Uri($"{BaseApiUrl}/?{odataAndQuery}", UriKind.Relative);
            }
            else
            {
                uri = new Uri($"{BaseApiUrl}/{filter.Method}()?{odataAndQuery}", UriKind.Relative);
            }

            return _quarkClient.Get<IList<T>>(uri);
        }

        /// <summary>
        /// Verilen filtreye uygun OData sorgusunu uretir.
        /// </summary>
        /// <param name="filter">Sorguyu olusturmak icin kullanilacak parametreler</param>
        /// <param name="disableTop">Top parametresinin sorguya eklenip eklenmeyecegi</param>
        /// <returns>Sınıfa ait base url'i doner</returns>
        private string CreateODataAndQuery(ODataFilter filter, bool disableTop = false)
        {
            if (filter == null)
            {
                return string.Empty;
            }

            var oDataAndQuery = new List<string>();

            if (!string.IsNullOrWhiteSpace(filter.Filter))
            {
                oDataAndQuery.Add($"$filter={filter.Filter}");
            }

            if (!string.IsNullOrWhiteSpace(filter.Select))
            {
                oDataAndQuery.Add($"$select={filter.Select}");
            }

            if (!string.IsNullOrWhiteSpace(filter.Expand))
            {
                oDataAndQuery.Add($"$expand={filter.Expand}");
            }

            if (!string.IsNullOrWhiteSpace(filter.OrderBy))
            {
                oDataAndQuery.Add($"$orderby={filter.OrderBy}");
            }

            if (!string.IsNullOrWhiteSpace(filter.QueryString))
            {
                oDataAndQuery.Add($"{filter.QueryString}");
            }

            if (filter.Skip > 0)
            {
                oDataAndQuery.Add($"$skip={filter.Skip}");
            }

            if (!disableTop)
            {
                oDataAndQuery.Add($"$top={filter.Top}");
            }

            return string.Join("&", oDataAndQuery);
        }
    }
}
