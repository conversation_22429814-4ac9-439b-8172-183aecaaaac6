﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Service.Hangfire.Services.Interfaces;

public interface IPiyasaTakvimiService
{
    Task<List<PiyasaTakvimi>> GetPiyasaTakvimiTopCount(int topCount);
    Task<List<PiyasaTakvimi>> GetPiyasaTakvimBySelectedDateRange(DateTime baslangictarihi, int gunSayisi, short tarihSayisi);
    Task<bool> InsertOrUpdatePiyasaTakvimi(PiyasaTakvimi piyasaTakvimi);
    Task<int> GetTotalPiyasaTakvimiCount();
    Task<List<PiyasaTakvimi>> GetPiyasaTakvimiByPaging(int intervalStart, int intervalEnd);
    Task<PiyasaTakvimi> GetPiyasaTakvimiById(int id);
    Task DeletePiyasaTakvimi(int Id);
}