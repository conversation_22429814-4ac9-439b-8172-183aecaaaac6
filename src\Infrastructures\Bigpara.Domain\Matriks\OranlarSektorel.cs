﻿namespace Bigpara.Domain.Matriks;

public partial class OranlarSektorel : BaseEntity
{
    public int Id { get; set; }
    public System.DateTime UpdatedDateTime { get; set; }
    public int Sektor_ID { get; set; }
    public string Senet { get; set; }
    public string Donem { get; set; }
    public Nullable<double> F_K { get; set; }
    public Nullable<double> FKOrant { get; set; }
    public Nullable<double> PD_DD { get; set; }
    public Nullable<double> NetKar_TopAktif { get; set; }
    public Nullable<double> NetKar_Ozsermaye { get; set; }
    public Nullable<double> PD_NetSatis { get; set; }
    public Nullable<double> PD_EFaalKar { get; set; }
    public Nullable<double> NetKar_NetSatis { get; set; }
    public Nullable<double> EFaalKar_NetSatis { get; set; }
    public Nullable<double> Cari_Oran { get; set; }
    public Nullable<double> EFaalKar_KVBorc { get; set; }
    public Nullable<double> KVBorc_TopAktif { get; set; }
    public Nullable<double> KVBor_TopBorc { get; set; }

}
