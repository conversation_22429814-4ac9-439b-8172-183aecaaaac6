﻿using Bigpara.Domain;
using Bigpara.Domain.Bigpara;
using Bigpara.Domain.Enums;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.Definitions;
using Bigpara.Domain.Matriks.StoredProcedureResults;
using Bigpara.Domain.SummaryTypes;

namespace Bigpara.Service.Hangfire.Services.Interfaces;

public interface IBorsaService
{
    Task<Semboller> GetSembollerById(int sembolId);
    Task<List<SembolOzet>> GetEncokIslemArtanAzalanlarByType(ShareProcessStatusType type, int? topN);
    Task<List<SembolOzet>> GetHisseAgirlikliOrtalama(string direction, int pageNo, byte pageSize);
    List<SembolOzet> GetHisseAgirlikliOrtalamaByPaging(string direction, int pageNo, byte pageSize, out int totalCount);
    Task<List<Hisse>> GetHisseList();
    Task<List<Hisse>> GetTumHisseList();
    Task<List<HisseYuzeysel>> GetHisseYuzeysel();
    Task<HisseYuzeysel> GetHisseYuzeysel(string sembol);
    Task<List<HisseYuzeysel>> GetHisseYuzeysel(Func<HisseYuzeysel, bool> filterFunction);
    Task<List<HisseYuzeyselOnline>> GetHisseYuzeyselOnline(Func<HisseYuzeyselOnline, bool> filterFunction);
    Task<HisseYuzeysel> GetHisseYuzeyselDegerleriBySembolId(int sembolId);
    Task<List<HisseYuzeyselIndikator>> GetHisseYuzeyselTarihsiz(string sembol);
    Task<List<HisseYuzeysel>> GetHisseYuzeyselTarihsizBySembolId(int sembol);
    List<SembolOzet> GetGecmisKapanislar(out int totalCount, DateTime? selectedDate, MarketTypes marketType, char? selectedLetter, int pageNo, byte pageSize);
    Task<List<DunyaBorsalari>> GetDunyaBorsalari();
    Task<List<DunyaBorsalari>> GetDunyaBorsalariByContinent(Continents continent);
    Task<List<BilancoDonem>> GetBilancoDonems(string sembol, string donem, int cins);
    Task<List<BilancoDonem>> GetBilancoDonemByYil(string sembol, string donem, int cins, int yil);
    Task<List<BilancoDonemSembol>> GetBilancoDonemsBySembol(string sembol);
    Task<List<EndeksYuzeysel>> GetEndeksYuzeysel();
    Task<List<EndeksYuzeysel>> GetEndeksYuzeyselArtanAzalan();
    List<HisseYuzeysel> GetEndeksHisseYuzeysels(string endeks, Func<HisseYuzeysel, bool> filterFunction, short pageNo, int pageSize, out int totalCount);
    Task<List<HisseYuzeysel>> GetHisseFiyatlariByEndeksVeHarf(string endeks, int pageNo, byte pageSize, char? letter);
    Task<List<HisseYuzeysel>> GetUserHisseYuzeysel(int userId);
    Task<List<Haberler>> GetUserHisseHaberleri(int userId);
    Task<List<LiveStockHisse>> GetYuzeselOnlineBySembol(DateTime? tarih);
    Task<List<EndeksYuzeysel>> GetEndeksDegerleriBySembol(string sembol);
    List<HisseYuzeysel> GetHisselerIslemHacmiArtanAzalan(OrderByDirection orderByDirection, int pageNo, byte pageSize, out int totalCount);
    List<HisseYuzeysel> GetHisselerIslemHacmiFiyatArtanAzalan(TradingVolumeFilterType tradingVolumeFilterType, int pageNo, byte pageSize, out int totalCount);
    List<PerformansAnaliz> GetPerformansAnaliz(string endeks, int sembolId, DateTime? startDateTime,
        DateTime? endDateTime, string currency, int orderBy, int pageNo, int pageSize, out int totalCount);
    Task<List<KisaTanimSemboller>> GetUserHisseSembolList(int userId, int pageType);
    Task<int> SaveUserHisse(int userId, int sembolId, int pageType, int opId);
    Task<List<KisaTanimSemboller>> GetPiyasaBandiHisseListe();
    Task<List<KisaTanimSemboller>> GetDefaultPiyasaBandi();
    Task<int> SaveUserAlert(int userId, string sembol, double price, string alertField);
    Task<int> DeleteUserAlert(int userId, int sembolId);
    Task<List<UserAlert>> GetUserAlerts(int userId);
    Task<List<UserAlert>> GetUserAlertResult(int userId);
    Task<int> UserAlertDeactivate(int userId, int id);
    Task<int> UserAlertInstanceAlert(int userId);
    Task<int> SaveUserHisse(int userId, int sembolId, int pageType, int opId, int orderId);
    Task<List<YatirimAraclari>> GetYatirimAraclariDataList();
    Task<List<SembolOzet>> GetHisseDurumByType(ShareProcessStatusType type, SeansTypes seansTypes);
    Task<SembolOzet> GetGecmisKapanisMaxDate();
    Task<List<HisseYuzeysel>> GetHisseFiyatlariByEndeksVeHarfOnline(string endeks, int pageNo, byte pageSize, char? letter);
    Task<List<HisseYuzeysel>> GetUserHisseYuzeyselOnline(int userId);
}