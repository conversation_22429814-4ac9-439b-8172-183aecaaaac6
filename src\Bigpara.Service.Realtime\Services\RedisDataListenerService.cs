﻿namespace Bigpara.Service.Realtime.Services
{

    public class RedisDataListenerService : BackgroundService
    {
        private readonly IRedisSubscriptionService _redisSubscriptionService;
        private readonly ILogger<RedisDataListenerService> _logger;

        public RedisDataListenerService(
            IRedisSubscriptionService redisSubscriptionService,
            ILogger<RedisDataListenerService> logger)
        {
            _redisSubscriptionService = redisSubscriptionService;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Redis Data Listener Service started");

            await _redisSubscriptionService.StartListening();

            // Service çalışmaya devam etsin
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }

            await _redisSubscriptionService.StopListening();
            _logger.LogInformation("Redis Data Listener Service stopped");
        }
    }

}
