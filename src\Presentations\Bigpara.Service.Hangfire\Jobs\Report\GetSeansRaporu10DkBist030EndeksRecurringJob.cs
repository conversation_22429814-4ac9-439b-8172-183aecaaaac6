﻿using Bigpara.Domain.Matriks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Report;

public class GetSeansRaporu10DkBist030EndeksRecurringJob : IRecurringJob
{
    private readonly ISeansRaporlariService _seansRaporlariService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<GetSeansRaporu10DkBist030EndeksRecurringJob> _logger;

    public GetSeansRaporu10DkBist030EndeksRecurringJob
    (
        ISeansRaporlariService seansRaporlariService,
        INotificationService notificationService,
        ILogger<GetSeansRaporu10DkBist030EndeksRecurringJob> logger
    )
    {
        _seansRaporlariService = seansRaporlariService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "GetSeansRaporu10DkBist030EndeksRecurringJob";
     public IEnumerable<string> Crons => ["*/10 9-17 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("GetSeansRaporu10DkBist030EndeksRecurringJob - Başladı ");
            await ProcessGetSeansRaporu10DkBist030Endeks();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    /// <summary>
    /// 10 dk gun ici bist 30 endeks göstergesi bilgisi aliniyor
    /// </summary>
    public async Task ProcessGetSeansRaporu10DkBist030Endeks()
    {
        if (!_seansRaporlariService.IsActive())
            return;

        try
        {
            const string pattern = "BIST 30 endeksi düne göre {0} {1} (%{2}) {3} puanda.";
            var seansRaporuYuzeysel = await _seansRaporlariService.GetSeansRaporu10DkBist030Endeks();

            if (seansRaporuYuzeysel == null) return;
            var result = string.Format(pattern, (int)seansRaporuYuzeysel.DEGISIM, seansRaporuYuzeysel.YUZDEDEGISIM > 0 ? "artışla" : "kayıpla", seansRaporuYuzeysel.YUZDEDEGISIM, seansRaporuYuzeysel.KAPANIS.ToMoneyString("{0:N0}"));

            var yon = 0;
            if (seansRaporuYuzeysel.YUZDEDEGISIM > 0)
            {
                yon = 1;
            }
            else if (seansRaporuYuzeysel.YUZDEDEGISIM < 0)
            {
                yon = -1;
            }
            var seansRaporu = new SeansRaporu
            {
                HisseCumle = result,
                Yon = yon,
                HisseAdi = seansRaporuYuzeysel.SEMBOL,
                EklenmeTarihi = DateTime.Now
            };

            await _seansRaporlariService.CreateAndCacheSeansRaporu(seansRaporu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            //EmailHelper.SendEmail("GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisseler", ex.Message);
            await _notificationService.NotifyErrorAsync($"GetSeansRaporu15DkBist0300EnCokDegerKazanaHisseler", ex.Message);
        }
    }
}
