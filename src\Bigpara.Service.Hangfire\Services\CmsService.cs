﻿using Bigpara.Service.Hangfire.Services.Interfaces;
using Quark.Models.Entities;
using Quark.Models.Entities.Properties;
using Quark.Models.Entities.Containers;
using Quark.Models.Entities.Contents;
using Bigpara.Application.Contracts.Repositories.Quark.Core;

namespace Bigpara.Service.Hangfire.Services;

public class CmsService : ICmsService
{
    private readonly ICmsRepository _cmsRepository;

    public CmsService(ICmsRepository cmsRepository)
    {
        _cmsRepository = cmsRepository;
    }

    public async Task<bool> ContentExistsAsync(string collectionName, string referenceId)
    {
        var existingContent = await _cmsRepository.ContentExistsAsync(collectionName, referenceId);

        return existingContent;
    }

    public async Task<List<Ancestor>> GetAncestorsAsync(string collectionName, string contentPath)
    {
        var ancestors = await _cmsRepository.GetAncestorsAsync(collectionName, contentPath);

        return ancestors;
    }

    public async Task<List<Property>> GetContentPropertiesAsync(string typeName)
    {
        var properties = await _cmsRepository.GetContentPropertiesAsync(typeName);

        return properties;
    }

    public async Task<List<Folder>> GetFoldersAsync(string collectionName)
    {
        var folders = await _cmsRepository.GetFoldersAsync(collectionName);

        return folders;
    }

    public async Task<long> GetLastKapNewsTimestampAsync(string collectionName, string application, string path)
    {
        var lastKapNewsTimestamp = await _cmsRepository.GetLastKapNewsTimestampAsync(collectionName, application, path);

        return lastKapNewsTimestamp;
    }

    public async Task<long> GetNextIncrementIdAsync(string collectionName, string application)
    {
        var nextIncrementId = await _cmsRepository.GetNextIncrementIdAsync(collectionName, application);

        return nextIncrementId;
    }

    public async Task<IList<Permission>> GetPermissionsAsync(string collectionName, string contentPath)
    {
        var permissions = await _cmsRepository.GetPermissionsAsync(collectionName, contentPath);

        return permissions;
    }

    public async Task InsertContentAsync(string collectionName, Article article)
    {
        await _cmsRepository.InsertContentAsync(collectionName, article);
    }
}
