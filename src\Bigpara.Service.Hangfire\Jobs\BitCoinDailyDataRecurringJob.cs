﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Bigpara;
using Bigpara.Domain.Bitcoin;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using System.Data;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs;

public class BitCoinDailyDataRecurringJob : IRecurringJob
{
    private readonly IBitcoinService _bitcoinService;
    private readonly ILogger<BitCoinDailyDataRecurringJob> _logger;
    private readonly BigparaHttpClient _bigparaHttpClient;
    private readonly INotificationService _notificationService;
    private readonly ISystemSettingsService _systemSettingsService;

	private static readonly HashSet<string> SupportedSymbols = new()
	{
		"bitstampUSD", "mtgoxUSD", "mtgoxEUR",
		"mtgoxGBP", "btceEUR", "btceUSD", "cbxUSD"
	};

	public BitCoinDailyDataRecurringJob
    (
        IBitcoinService bitcoinService,
        ILogger<BitCoinDailyDataRecurringJob> logger,
        BigparaHttpClient bigparaHttpClient,
        INotificationService notificationService,
        ISystemSettingsService systemSettingsService
    )
    {
        _bitcoinService = bitcoinService;
        _logger = logger;
        _bigparaHttpClient = bigparaHttpClient;
        _notificationService = notificationService;
        _systemSettingsService = systemSettingsService;
    }

    public string Name => "BitCoinDailyDataRecurringJob";
    public IEnumerable<string> Crons => ["0 1-23 * * *"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("BitCoinDailyDataRecurringJob - Başladı ");
            await ProcessDailyData();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    private async Task ProcessDailyData()
    {
        try
        {
            string sUrl = (await _systemSettingsService.GetSystemSettingsValueFromCacheAsync("FileConfiguration", "BitCoinAPIUrl")).Value;
            var marketResults = await _bigparaHttpClient.FetchObjectAsync<Market[]>(sUrl);

			if (marketResults == null || marketResults.Length <= 0) return;
            var filteredResults = FilterSupportedMarkets(marketResults);

			if (filteredResults.Any())
            {
                foreach (var itemMarket in filteredResults)
                {

                    var dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0);
                    dtDateTime = dtDateTime.AddSeconds(Convert.ToDouble(itemMarket.LatestTrade)).ToLocalTime();

                    var rowToUpdate = await _bitcoinService.GetMarketsOnlineBySymbolAndDate(itemMarket.Symbol,
                        dtDateTime);
                    if (rowToUpdate == null)
                    {
                        var marketsOnline = new MarketsOnline
                        {
                            Symbol = itemMarket.Symbol,
                            DateTime = dtDateTime,
                            UpdatedDateTime = DateTime.Now,
                            HighValue = itemMarket.High,
                            LowValue = itemMarket.Low,
                            CloseValue = itemMarket.Close,
                            AvgValue = itemMarket.AVG,
                            BidValue = itemMarket.Bid,
                            AskValue = itemMarket.Ask,
                            VolumeValue = itemMarket.Volume,
                            Currency = itemMarket.Currency,
                            CurrencyVolume = itemMarket.CurrencyVolume
                        };

                        await _bitcoinService.AddMarketsOnline(marketsOnline);
                    }
                    else
                    {
                        rowToUpdate.UpdatedDateTime = DateTime.Now;
                        await _bitcoinService.UpdateMarketsOnline(rowToUpdate);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.StackTrace);
            // TODO : Check
            // EmailHelper.SendEmail("Bitcoin DailyData", exception.Message);
            await _notificationService.NotifyErrorAsync("Bitcoin DailyData", exception.Message);
        }
    }

	private static List<Market> FilterSupportedMarkets(Market[] marketResults)
	{
		return marketResults.Where(market => SupportedSymbols.Contains(market.Symbol)).ToList();
	}

}
