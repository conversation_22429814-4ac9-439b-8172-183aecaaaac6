using Bigpara.Application.DTOs;
using Bigpara.Application.Validators;
using FluentValidation.TestHelper;
using Xunit;

namespace Bigpara.Application.Tests.Validators;

public class CreateUserDtoValidatorTests
{
    private readonly CreateUserDtoValidator _validator;

    public CreateUserDtoValidatorTests()
    {
        _validator = new CreateUserDtoValidator();
    }

    [Fact]
    public void Should_Have_Error_When_HurPassId_Is_Empty()
    {
        // Arrange
        var dto = new CreateUserDto { HurPassId = "" };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldHaveValidationErrorFor(x => x.HurPassId);
    }

    [Fact]
    public void Should_Have_Error_When_HurPassId_Exceeds_MaxLength()
    {
        // Arrange
        var dto = new CreateUserDto { HurPassId = new string('a', 51) };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldHaveValidationErrorFor(x => x.HurPassId);
    }

    [Fact]
    public void Should_Have_Error_When_FirstName_Is_Empty()
    {
        // Arrange
        var dto = new CreateUserDto { FirstName = "" };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldHaveValidationErrorFor(x => x.FirstName);
    }

    [Fact]
    public void Should_Have_Error_When_FirstName_Contains_Invalid_Characters()
    {
        // Arrange
        var dto = new CreateUserDto { FirstName = "John123" };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldHaveValidationErrorFor(x => x.FirstName);
    }

    [Theory]
    [InlineData("John")]
    [InlineData("Mehmet")]
    [InlineData("Ayşe")]
    [InlineData("Ömer")]
    [InlineData("John Doe")]
    public void Should_Not_Have_Error_When_FirstName_Is_Valid(string firstName)
    {
        // Arrange
        var dto = new CreateUserDto 
        { 
            FirstName = firstName,
            HurPassId = "valid-id",
            LastName = "Doe",
            Email = "<EMAIL>",
            UserTypeId = 1
        };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldNotHaveValidationErrorFor(x => x.FirstName);
    }

    [Fact]
    public void Should_Have_Error_When_Email_Is_Invalid()
    {
        // Arrange
        var dto = new CreateUserDto { Email = "invalid-email" };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldHaveValidationErrorFor(x => x.Email);
    }

    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    public void Should_Not_Have_Error_When_Email_Is_Valid(string email)
    {
        // Arrange
        var dto = new CreateUserDto 
        { 
            Email = email,
            HurPassId = "valid-id",
            FirstName = "John",
            LastName = "Doe",
            UserTypeId = 1
        };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldNotHaveValidationErrorFor(x => x.Email);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(6)]
    public void Should_Have_Error_When_UserTypeId_Is_Invalid(byte userTypeId)
    {
        // Arrange
        var dto = new CreateUserDto { UserTypeId = userTypeId };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldHaveValidationErrorFor(x => x.UserTypeId);
    }

    [Theory]
    [InlineData(-2)]
    [InlineData(2)]
    public void Should_Have_Error_When_Gender_Is_Invalid(int gender)
    {
        // Arrange
        var dto = new CreateUserDto { Gender = gender };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldHaveValidationErrorFor(x => x.Gender);
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(0)]
    [InlineData(1)]
    public void Should_Not_Have_Error_When_Gender_Is_Valid(int gender)
    {
        // Arrange
        var dto = new CreateUserDto 
        { 
            Gender = gender,
            HurPassId = "valid-id",
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            UserTypeId = 1
        };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldNotHaveValidationErrorFor(x => x.Gender);
    }

    [Fact]
    public void Should_Have_Error_When_BirthDate_Is_In_Future()
    {
        // Arrange
        var dto = new CreateUserDto { BirthDate = DateTime.Today.AddDays(1) };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldHaveValidationErrorFor(x => x.BirthDate);
    }

    [Fact]
    public void Should_Have_Error_When_BirthDate_Is_Too_Old()
    {
        // Arrange
        var dto = new CreateUserDto { BirthDate = DateTime.Today.AddYears(-121) };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldHaveValidationErrorFor(x => x.BirthDate);
    }

    [Fact]
    public void Should_Not_Have_Error_When_BirthDate_Is_Valid()
    {
        // Arrange
        var dto = new CreateUserDto 
        { 
            BirthDate = DateTime.Today.AddYears(-25),
            HurPassId = "valid-id",
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            UserTypeId = 1
        };

        // Act & Assert
        var result = _validator.TestValidate(dto);
        result.ShouldNotHaveValidationErrorFor(x => x.BirthDate);
    }
}
