namespace Bigpara.Domain.Bigpara;

public class SystemConfiguration 
{
    public int Id { get; set; }
    public string Owner { get; set; }
    public string ConfigKey { get; set; }
    public string ConfigValue { get; set; }
    public string ConfigText { get; set; }
    public string ConfigCode { get; set; }
    public string ConfigTag { get; set; }
    public string ValueType { get; set; }
    public string PossibleValues { get; set; }
    public string Description { get; set; }
    public bool? IsEncrypt { get; set; }
    public string EncryptType { get; set; }
    public int? ParentId { get; set; }
    public string ListSeparator { get; set; }
    public string ParameterType { get; set; }
}
