﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Service.Hangfire.Services.Interfaces;

public interface IYuzeyselService
{
    Task<Yuzeysel> GetYuzeyselBySembolId(int sembolId);
    Task<Yuzeysel> GetBySembol(string sembol);
    Task<List<YuzeyselOpeation>> GetYuzeysellerList();
    Task<Yuzeysel> GetYuzeyselBySembolTarih(string sembolId, DateTime tarih);
    Task<List<Yuzeysel>> GetBySembols(string sembols);
}
