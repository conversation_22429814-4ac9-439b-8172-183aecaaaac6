using Bigpara.Application.Contracts.Repositories.Matriks.Grafiks;
using Bigpara.Application.Features.Grafiks.Queries;
using Bigpara.Domain.Matriks;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using static Bigpara.Domain.Widget.WidgetSembol;

namespace Bigpara.Core.Tests.Application.Queries.GrafikGunlukQuery
{
    public class GetGrafikGunlukQueryHandlerTests
    {
        [Fact]
        public async Task Handle_ReturnsExpectedResponse()
        {
            // Arrange
            var mockRepo = new Mock<IGrafikRepository>();
            var ids = new List<string> { "THYAO", "GARAN", "AKBNK" };
            var perCount = 3;
            var backDay = 5;
            var query = new GetGrafikGunlukQuery
            {
                Symbols = ids,
                PerCount = perCount,
                BackDay = backDay
            };

            var grafikData = new List<GrafikGunluk>
            {
                new GrafikGunluk { SEMBOLID = 1, SEMBOL="THYAO", TARIH = new DateTime(2024, 6, 1), HACIMTL = 100 },
                new GrafikGunluk { SEMBOLID = 2, SEMBOL="GARAN", TARIH = new DateTime(2024, 5, 31), HACIMTL = 90 },
                new GrafikGunluk { SEMBOLID = 3, SEMBOL="AKBNK", TARIH = new DateTime(2024, 6, 2), HACIMTL = 200 }
            };

            mockRepo.Setup(r => r.GetGrafikGunlukBySembolIds(string.Join(",",query.Symbols),query.PerCount,query.BackDay))
                .ReturnsAsync(grafikData);

            var handler = new GetGrafikGunlukQueryHandler(mockRepo.Object);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(3, result.Data.Count);

            var item1 = result.Data.FirstOrDefault(x => x.Current?.SEMBOL == "GARAN");
            Assert.NotNull(item1);
            Assert.Equal(1, item1.Chart.Count);
            Assert.Equal(90, item1.Current.HACIMTL);

            var item2 = result.Data.FirstOrDefault(x => x.Current?.SEMBOL == "THYAO");
            Assert.NotNull(item2);
            Assert.Single(item2.Chart);
            Assert.Equal(100, item2.Current.HACIMTL);
        }
    }
}
