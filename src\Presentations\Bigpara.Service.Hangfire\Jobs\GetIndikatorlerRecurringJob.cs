﻿using Bigpara.Domain.Matriks;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;
using System.Data;
using System.Xml;

namespace Bigpara.Service.Hangfire.Jobs;

public class GetIndikatorlerRecurringJob : IRecurringJob
{
    private static double? nBBW_20_2_Middle = null;
    private static double? nBBW_20_2_Down = null;
    private static double? nMACD_Trigger_9 = null;
    private static double? nFStoch_D_3 = null;
    private static double? nSStoch_D_3 = null;
    public static DateTime DSeansTime = DateTime.Today.AddHours(12);
    static readonly List<Tuple<string, string>> LErrorSembols = new List<Tuple<string, string>>();
    private readonly IIndikatorlerService _indikatorlerService;
    private readonly IYuzeyselService _yuzeyselService;
    private readonly ILogger<GetIndikatorlerRecurringJob> _logger;
    private readonly ISembollerService _sembollerService;
    private readonly BigparaHttpClient _bigparaHttpClient;
    private readonly IConfiguration _configuration;

    public GetIndikatorlerRecurringJob
    (
        IIndikatorlerService indikatorlerService,
        IYuzeyselService yuzeyselService,
        ILogger<GetIndikatorlerRecurringJob> logger,
        BigparaHttpClient bigparaHttpClient,
        IConfiguration configuration,
        ISembollerService sembollerService
    )
    {
        _indikatorlerService = indikatorlerService;
        _yuzeyselService = yuzeyselService;
        _logger = logger;
        _bigparaHttpClient = bigparaHttpClient;
        _configuration = configuration;
        _sembollerService = sembollerService;
    }

    public string Name => "GetIndikatorlerRecurringJob";
    public IEnumerable<string> Crons => ["0 10 * * 1-5", "40 12 * * 1-5", "00 14 * * 1-5", "10 18 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("Bigpara.Indikatorler - Başladı ");
            await ProcessGetIndikatorler();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    public async Task ProcessGetIndikatorler()
    {
        try
        {
            //Tatil günlerinde çalışmaması için data gelmişmi bakalım
            if (DateTime.Now.Hour > 16) DSeansTime = DateTime.Today.AddHours(17);

            var yuzeyselRow = await _yuzeyselService.GetYuzeyselBySembolTarih("XU100", DSeansTime);

            //Seans için yukarıdaki data mutlaka gelmiş olmalı eğer geldi ise işleme devam
            if (yuzeyselRow == null)
            {
                _logger.LogInformation("YuzeyselRow null");
                Thread.Sleep(5000);
                return;
            }

            //Bazı semboller açık fakat işlem görmüyor. Bu nedenle GRAFIK_GUNLUK tablosunda son 7 günde yoksa bunları devre dışı bırakabiliriz
            DateTime dDate1 = DateTime.Today.AddDays(-7);

            var forexIndikatorBaseUrl = $"{_configuration["Foreks:CloudApi"]}/{_configuration["Foreks:Cloud:Indikator:Url"]}";

            var grafikGunlukSembols = await _sembollerService.GrafikGunlukSembols(dDate1);

            if (grafikGunlukSembols.Any())
            {
                #region MyRegion
                foreach (var itemSembol in grafikGunlukSembols)
                {
                    _logger.LogInformation(itemSembol.Sembol + Environment.NewLine);
                    string sembol = TextHelper.RemoveEscapeCharacters(itemSembol.Sembol);
                    Console.WriteLine(sembol);
                    string sIndicatorURL = string.Empty;
                    int period = 0;
                    int period2 = 0;
                    int period3 = 0;
                    string sembolType = sembol.StartsWith("X") ? "I" : "E";

                    //MOV - Haraketli Ortalamalar: /last için minimum (period + istenengünsayısı - 1) verilmeli. Diğer indikatörlerde geçerli değil
                    period = 5;
                    var indikatorCode = "SMA";
                    var fieldName = $"SMA{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/7?SMA=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_5 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    period = 9;
                    indikatorCode = "SMA";
                    fieldName = $"SMA{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/11?SMA=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_9 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    period = 10;
                    indikatorCode = "SMA";
                    fieldName = $"SMA{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/12?SMA=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_10 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    period = 20;
                    indikatorCode = "SMA";
                    fieldName = $"SMA{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/22?SMA=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_20 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    period = 50;
                    indikatorCode = "SMA";
                    fieldName = $"SMA{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/52?SMA=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_50 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    period = 100;
                    indikatorCode = "SMA";
                    fieldName = $"SMA{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/102?SMA=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_100 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    period = 250;
                    indikatorCode = "SMA";
                    fieldName = $"SMA{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/252?SMA=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_250 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //BBW - Bollinger Band Width
                    indikatorCode = "BOL";
                    fieldName = "BOL";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/20?BOL=per:20-devu:2-devd:2&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    nBBW_20_2_Middle = null;
                    nBBW_20_2_Down = null;
                    double? nBBW_20_2_Up = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //CCI - Commodity Channel Index
                    indikatorCode = "CCI";
                    period = 10;
                    fieldName = $"CCI{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.E.BIST/period/1440/last/20?CCI=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nCCI_10 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    period = 14;
                    fieldName = $"CCI{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/20?CCI=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nCCI_14 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    period = 20;
                    fieldName = $"CCI{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/20?CCI=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nCCI_20 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //MACD
                    fieldName = "MACD";
                    indikatorCode = "MACD"; //min 34 gün (veya 26+12+9-1)
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/34?MACD=fast:12-slow:26-sig:9&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMACD_26_12 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //ADX:
                    period = 3;
                    indikatorCode = "ADX";
                    fieldName = $"ADX{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/6?{indikatorCode}=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nADX_3 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //SAR:
                    period = 3;
                    indikatorCode = "SAR";
                    fieldName = "SAR";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/2?{indikatorCode}=acc:0.002-max:0.2&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nSAR = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //Momentum
                    period = 12;
                    indikatorCode = "MOM";
                    fieldName = "MOM";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/13?{indikatorCode}=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMomentum_12 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //RSI - Relative Strength Index
                    period = 14;
                    indikatorCode = "RSI";
                    fieldName = $"RSI{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/15?{indikatorCode}=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nRSI_14 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //RSI - Relative Strength Index
                    period = 9;
                    indikatorCode = "RSI";
                    fieldName = $"RSI{period}";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/15?{indikatorCode}=per:{period}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nRSI_9 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //Fast Stochastic (5,3)
                    indikatorCode = "STOF";
                    fieldName = "STOF";
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/9?STOF=fastk:5-fastd:3&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nFStoch_K_5 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //Slow Stochastic 
                    //10,3,6
                    indikatorCode = "STOS";
                    fieldName = "STOS_10_3_6";
                    period = 10;
                    period2 = 3;
                    period3 = 6;
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/17?STOS=fastk:{period}-slowk:{period2}-slowd:{period3}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nSStoch_K_5_5 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //5,1,6
                    indikatorCode = "STOS";
                    fieldName = "STOS_5_1_6";
                    period = 5;
                    period2 = 1;
                    period3 = 6;
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/11?STOS=fastk:{period}-slowk:{period2}-slowd:{period3}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nSTC_5_1 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //5,3,6
                    indikatorCode = "STOS";
                    fieldName = "STOS_5_3_6";
                    period = 5;
                    period2 = 3;
                    period3 = 6;
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/13?STOS=fastk:{period}-slowk:{period2}-slowd:{period3}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nSTC_5_3 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //13,1,6
                    indikatorCode = "STOS";
                    fieldName = "STOS_13_1_6";
                    period = 13;
                    period2 = 1;
                    period3 = 6;
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/20?STOS=fastk:{period}-slowk:{period2}-slowd:{period3}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nSTC_13_1 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    //21,1,6
                    indikatorCode = "STOS";
                    fieldName = "STOS_21_1_6";
                    period = 21;
                    period2 = 1;
                    period3 = 6;
                    sIndicatorURL = $"{forexIndikatorBaseUrl}{sembol}.{sembolType}.BIST/period/1440/last/26?STOS=fastk:{period}-slowk:{period2}-slowd:{period3}&e=e";
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nSTC_21_1 = await GetIndikatorForeksData(indikatorCode, sIndicatorURL, fieldName);

                    var rowToUpdate = await _indikatorlerService.GetIndikatorlers(sembol, DateTime.Today);
                    if (rowToUpdate == null || string.IsNullOrEmpty(rowToUpdate.SEMBOL))
                    {
                        var indikatorler = new Indikatorler
                        {
                            SEMBOL = sembol,
                            TARIH = DateTime.Today,
                            UpdatedDateTime = DateTime.Now,
                            MOV_5 = nMOV_5,
                            MOV_9 = nMOV_9,
                            MOV_10 = nMOV_10,
                            MOV_20 = nMOV_20,
                            MOV_50 = nMOV_50,
                            MOV_100 = nMOV_100,
                            MOV_250 = nMOV_250,
                            BBW_20_2_Up = nBBW_20_2_Up,
                            BBW_20_2_Middle = nBBW_20_2_Middle,
                            BBW_20_2_Down = nBBW_20_2_Down,
                            CCI_14 = nCCI_14,
                            MACD_26_12 = nMACD_26_12,
                            MACD_Trigger_9 = nMACD_Trigger_9,
                            Momentum_12 = nMomentum_12,
                            RSI_14 = nRSI_14,
                            FStoch_K_5 = nFStoch_K_5,
                            FStoch_D_3 = nSStoch_K_5_5,
                            SStoch_K_5_5 = nSStoch_K_5_5,
                            SStoch_D_3 = nSStoch_D_3,
                            ADX_3 = nADX_3,
                            CCI_10 = nCCI_10,
                            CCI_20 = nCCI_20,
                            RSI_9 = nRSI_9,
                            SAR = nSAR,
                            STC_5_1 = nSTC_5_1,
                            STC_5_3 = nSTC_5_3,
                            STC_13_1 = nSTC_13_1,
                            STC_21_1 = nSTC_21_1,
                            CCI_20_STR = null,
                            MACD_STR = null,
                            MOV_CCI_10_10 = null,
                            MOV_CCI_10_5 = null,
                            MOV_MACD_5_E = null,
                            STC_5_3_T = null
                        };
                        await _indikatorlerService.CreateOrUpdate(indikatorler);
                    }
                    else
                    {
                        rowToUpdate.UpdatedDateTime = DateTime.Now;
                        rowToUpdate.MOV_5 = nMOV_5;
                        rowToUpdate.MOV_9 = nMOV_9;
                        rowToUpdate.MOV_10 = nMOV_10;
                        rowToUpdate.MOV_20 = nMOV_20;
                        rowToUpdate.MOV_50 = nMOV_50;
                        rowToUpdate.MOV_100 = nMOV_100;
                        rowToUpdate.MOV_250 = nMOV_250;
                        rowToUpdate.BBW_20_2_Up = nBBW_20_2_Up;
                        rowToUpdate.BBW_20_2_Middle = nBBW_20_2_Middle;
                        rowToUpdate.BBW_20_2_Down = nBBW_20_2_Down;
                        rowToUpdate.CCI_14 = nCCI_14;
                        rowToUpdate.MACD_26_12 = nMACD_26_12;
                        rowToUpdate.MACD_Trigger_9 = nMACD_Trigger_9;
                        rowToUpdate.Momentum_12 = nMomentum_12;
                        rowToUpdate.RSI_14 = nRSI_14;
                        rowToUpdate.FStoch_K_5 = nFStoch_K_5;
                        rowToUpdate.FStoch_D_3 = nFStoch_D_3;
                        rowToUpdate.SStoch_K_5_5 = nSStoch_K_5_5;
                        rowToUpdate.SStoch_D_3 = nSStoch_D_3;
                        rowToUpdate.ADX_3 = nADX_3;
                        rowToUpdate.CCI_10 = nCCI_10;
                        rowToUpdate.CCI_20 = nCCI_20;
                        rowToUpdate.RSI_9 = nRSI_9;
                        rowToUpdate.SAR = nSAR;
                        rowToUpdate.STC_5_1 = nSTC_5_1;
                        rowToUpdate.STC_5_3 = nSTC_5_3;
                        rowToUpdate.STC_13_1 = nSTC_13_1;
                        rowToUpdate.STC_21_1 = nSTC_21_1;
                        rowToUpdate.CCI_20_STR = null;
                        rowToUpdate.MACD_STR = null;
                        rowToUpdate.MOV_CCI_10_10 = null;
                        rowToUpdate.MOV_CCI_10_5 = null;
                        rowToUpdate.MOV_MACD_5_E = null;
                        rowToUpdate.STC_5_3_T = null;

                        await _indikatorlerService.CreateOrUpdate(rowToUpdate);
                    }
                }

                #endregion

                // _jobOtomatikTeknikYorum.Start();
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    public async Task Execute_Matriks()
    {
        try
        {
            _logger.LogInformation("Bigpara.Indikatorler - Başladı ");
            //Tatil günlerinde çalışmaması için data gelmişmi bakalım
            if (DateTime.Now.Hour > 16) DSeansTime = DateTime.Today.AddHours(17);

            var yuzeyselRow = await _yuzeyselService.GetYuzeyselBySembolTarih("XU100", DSeansTime);

            //Seans için yukarıdaki data mutlaka gelmiş olmalı eğer geldi ise işleme devam
            if (yuzeyselRow == null)
            {
                _logger.LogInformation("YuzeyselRow null");
                Thread.Sleep(5000);
                return;
            }

            //Bazı semboller açık fakat işlem görmüyor. Bu nedenle GRAFIK_GUNLUK tablosunda son 7 günde yoksa bunları devre dışı bırakabiliriz
            DateTime dDate1 = DateTime.Today.AddDays(-7);

            if ((await _sembollerService.GrafikGunlukSembols(dDate1)).Any())
            {
                #region MyRegion
                foreach (var itemSembol in await _sembollerService.GrafikGunlukSembols(dDate1))
                {
                    _logger.LogInformation(itemSembol.Sembol + Environment.NewLine);
                    string sSembol = TextHelper.RemoveEscapeCharacters(itemSembol.Sembol);
                    Console.WriteLine(sSembol);
                    string sIndicatorURL = string.Empty;
                    int nPeriod1 = 0;
                    int nPeriod2 = 0;
                    int nPeriod3 = 0;

                    //Haraketli Ortalamalar
                    //Gelen Data set üzerinde işlem yapabilmemiz için 10 günlük DATA'yı alıyoruz
                    //Eğer sadece bugün için datayı alırsak Data modeli değişiyor
                    nPeriod1 = 5;
                    DateTime dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}&Indicator1Param3=S", sSembol, "MOV", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_5 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    nPeriod1 = 9;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}&Indicator1Param3=S", sSembol, "MOV", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_9 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    nPeriod1 = 10;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}&Indicator1Param3=S", sSembol, "MOV", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_10 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    nPeriod1 = 20;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}&Indicator1Param3=S", sSembol, "MOV", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_20 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    nPeriod1 = 50;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}&Indicator1Param3=S", sSembol, "MOV", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_50 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    nPeriod1 = 100;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}&Indicator1Param3=S", sSembol, "MOV", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_100 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    nPeriod1 = 250;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}&Indicator1Param3=S", sSembol, "MOV", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMOV_250 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    //BBW - Bollinger Band Width
                    nPeriod1 = 20;
                    nPeriod2 = 2;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}&Indicator1Param3=S&Indicator1Param4={4}", sSembol, "BOL", dDate2.ToString("yyyyMMdd"), nPeriod1, nPeriod2);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    nBBW_20_2_Middle = null;
                    nBBW_20_2_Down = null;
                    double? nBBW_20_2_Up = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1, "BBW");
                    //Console.WriteLine(nBBW_20_2_Up.ToString("N02"));
                    //Console.WriteLine(Convert.ToDecimal(nBBW_20_2_Up.ToString("N02")));

                    //CCI - Commodity Channel Index
                    nPeriod1 = 14;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={3}", sSembol, "CCI", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nCCI_14 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    nPeriod1 = 10;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={3}", sSembol, "CCI", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nCCI_10 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    nPeriod1 = 20;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={3}", sSembol, "CCI", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nCCI_20 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    //MACD
                    nPeriod1 = 26;
                    nPeriod2 = 12;
                    nPeriod3 = 9;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={3}&Indicator1Param2={4}&Indicator1Param3={5}", sSembol, "MACD", dDate2.ToString("yyyyMMdd"), nPeriod1, nPeriod2, nPeriod3);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    nMACD_Trigger_9 = null;
                    double? nMACD_26_12 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1, "MACD");

                    //ADX
                    nPeriod1 = 3;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}", sSembol, "ADX", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nADX_3 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1, "ADX");

                    //SAR
                    nPeriod1 = 3;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={4}&Indicator1Param2={3}", sSembol, "PSAR", dDate2.ToString("yyyyMMdd"), 0.2, 0.002);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nSAR = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1, "PSAR");

                    //Momentum
                    nPeriod1 = 12;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}", sSembol, "MO", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nMomentum_12 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    //RSI - Relative Strength Index
                    nPeriod1 = 14;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}", sSembol, "RSI", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nRSI_14 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    nPeriod1 = 9;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1=4&Indicator1Param2={3}", sSembol, "RSI", dDate2.ToString("yyyyMMdd"), nPeriod1);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    double? nRSI_9 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1);

                    //Fast Stochastic
                    nPeriod1 = 5;
                    nPeriod2 = 3;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={3}&Indicator1Param2={4}", sSembol, "STOF", dDate2.ToString("yyyyMMdd"), nPeriod1, nPeriod2);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    nFStoch_D_3 = null;
                    double? nFStoch_K_5 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1, "STOF");

                    //Slow Stochastic
                    nPeriod1 = 10;
                    nPeriod2 = 3;
                    nPeriod3 = 6;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={3}&Indicator1Param2={4}&Indicator1Param3={5}", sSembol, "STOS", dDate2.ToString("yyyyMMdd"), nPeriod1, nPeriod2, nPeriod3);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    nSStoch_D_3 = null;
                    double? nSStoch_K_5_5 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1, "STOS");

                    nPeriod1 = 5;
                    nPeriod2 = 1;
                    nPeriod3 = 6;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={3}&Indicator1Param2={4}&Indicator1Param3={5}", sSembol, "STOS", dDate2.ToString("yyyyMMdd"), nPeriod1, nPeriod2, nPeriod3);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    nSStoch_D_3 = null;
                    double? nSTC_5_1 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1, "STOS");

                    nPeriod1 = 5;
                    nPeriod2 = 3;
                    nPeriod3 = 6;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={3}&Indicator1Param2={4}&Indicator1Param3={5}", sSembol, "STOS", dDate2.ToString("yyyyMMdd"), nPeriod1, nPeriod2, nPeriod3);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    nSStoch_D_3 = null;
                    double? nSTC_5_3 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1, "STOS");

                    nPeriod1 = 13;
                    nPeriod2 = 1;
                    nPeriod3 = 6;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={3}&Indicator1Param2={4}&Indicator1Param3={5}", sSembol, "STOS", dDate2.ToString("yyyyMMdd"), nPeriod1, nPeriod2, nPeriod3);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    nSStoch_D_3 = null;
                    double? nSTC_13_1 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1, "STOS");

                    nPeriod1 = 21;
                    nPeriod2 = 1;
                    nPeriod3 = 6;
                    dDate2 = DateTime.Now.AddDays(-10);
                    sIndicatorURL = string.Format("https://web.matriksdata.com/FinanceDataCenter/DataXML/DelayDBMatriks.aspx?TickerSymbol={0}&INDICATOR={1}&BeginTime={2}&EndTime=0&Indicator1Param1={3}&Indicator1Param2={4}&Indicator1Param3={5}", sSembol, "STOS", dDate2.ToString("yyyyMMdd"), nPeriod1, nPeriod2, nPeriod3);
                    _logger.LogInformation(sIndicatorURL + Environment.NewLine);
                    nSStoch_D_3 = null;
                    double? nSTC_21_1 = GetIndikatorMatriks(sSembol, sIndicatorURL, 0, 1, "STOS");

                    var rowToUpdate = await _indikatorlerService.GetIndikatorlers(sSembol, DateTime.Today);
                    if (rowToUpdate == null || string.IsNullOrEmpty(rowToUpdate.SEMBOL))
                    {
                        var indikatorler = new Indikatorler
                        {
                            SEMBOL = sSembol,
                            TARIH = DateTime.Today,
                            UpdatedDateTime = DateTime.Now,
                            MOV_5 = nMOV_5,
                            MOV_9 = nMOV_9,
                            MOV_10 = nMOV_10,
                            MOV_20 = nMOV_20,
                            MOV_50 = nMOV_50,
                            MOV_100 = nMOV_100,
                            MOV_250 = nMOV_250,
                            BBW_20_2_Up = nBBW_20_2_Up,
                            BBW_20_2_Middle = nBBW_20_2_Middle,
                            BBW_20_2_Down = nBBW_20_2_Down,
                            CCI_14 = nCCI_14,
                            MACD_26_12 = nMACD_26_12,
                            MACD_Trigger_9 = nMACD_Trigger_9,
                            Momentum_12 = nMomentum_12,
                            RSI_14 = nRSI_14,
                            FStoch_K_5 = nFStoch_K_5,
                            FStoch_D_3 = nSStoch_K_5_5,
                            SStoch_K_5_5 = nSStoch_K_5_5,
                            SStoch_D_3 = nSStoch_D_3,
                            ADX_3 = nADX_3,
                            CCI_10 = nCCI_10,
                            CCI_20 = nCCI_20,
                            RSI_9 = nRSI_9,
                            SAR = nSAR,
                            STC_5_1 = nSTC_5_1,
                            STC_5_3 = nSTC_5_3,
                            STC_13_1 = nSTC_13_1,
                            STC_21_1 = nSTC_21_1,
                            CCI_20_STR = null,
                            MACD_STR = null,
                            MOV_CCI_10_10 = null,
                            MOV_CCI_10_5 = null,
                            MOV_MACD_5_E = null,
                            STC_5_3_T = null
                        };
                        await _indikatorlerService.CreateOrUpdate(indikatorler);
                    }
                    else
                    {
                        rowToUpdate.UpdatedDateTime = DateTime.Now;
                        rowToUpdate.MOV_5 = nMOV_5;
                        rowToUpdate.MOV_9 = nMOV_9;
                        rowToUpdate.MOV_10 = nMOV_10;
                        rowToUpdate.MOV_20 = nMOV_20;
                        rowToUpdate.MOV_50 = nMOV_50;
                        rowToUpdate.MOV_100 = nMOV_100;
                        rowToUpdate.MOV_250 = nMOV_250;
                        rowToUpdate.BBW_20_2_Up = nBBW_20_2_Up;
                        rowToUpdate.BBW_20_2_Middle = nBBW_20_2_Middle;
                        rowToUpdate.BBW_20_2_Down = nBBW_20_2_Down;
                        rowToUpdate.CCI_14 = nCCI_14;
                        rowToUpdate.MACD_26_12 = nMACD_26_12;
                        rowToUpdate.MACD_Trigger_9 = nMACD_Trigger_9;
                        rowToUpdate.Momentum_12 = nMomentum_12;
                        rowToUpdate.RSI_14 = nRSI_14;
                        rowToUpdate.FStoch_K_5 = nFStoch_K_5;
                        rowToUpdate.FStoch_D_3 = nFStoch_D_3;
                        rowToUpdate.SStoch_K_5_5 = nSStoch_K_5_5;
                        rowToUpdate.SStoch_D_3 = nSStoch_D_3;
                        rowToUpdate.ADX_3 = nADX_3;
                        rowToUpdate.CCI_10 = nCCI_10;
                        rowToUpdate.CCI_20 = nCCI_20;
                        rowToUpdate.RSI_9 = nRSI_9;
                        rowToUpdate.SAR = nSAR;
                        rowToUpdate.STC_5_1 = nSTC_5_1;
                        rowToUpdate.STC_5_3 = nSTC_5_3;
                        rowToUpdate.STC_13_1 = nSTC_13_1;
                        rowToUpdate.STC_21_1 = nSTC_21_1;
                        rowToUpdate.CCI_20_STR = null;
                        rowToUpdate.MACD_STR = null;
                        rowToUpdate.MOV_CCI_10_10 = null;
                        rowToUpdate.MOV_CCI_10_5 = null;
                        rowToUpdate.MOV_MACD_5_E = null;
                        rowToUpdate.STC_5_3_T = null;


                        await _indikatorlerService.CreateOrUpdate(rowToUpdate);
                    }
                }

                #endregion

                // _jobOtomatikTeknikYorum.Start();
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    private static double? GetIndikatorMatriks(string sSembol, string sIndicatorURL, int nDateTimeValueIndex, int nDataValueIndex, string sIndicatorName = "")
    {
        DataSet ds = new DataSet();
        try
        {
            XmlTextReader xReader = new XmlTextReader(sIndicatorURL);
            ds.ReadXml(xReader);

            if (ds.Tables.Count > 0)
            {
                if (ds.Tables["Data"] != null && ds.Tables["Data"].Rows.Count > 0)
                {
                    //Burada gelen datanın yapısı nedeni ile değeri bulmak için aşağıdaki şekilde bir işlem yapılıyor
                    //Önce tarihle eşleştirmek için Tarih olan dataların içinde geziliyor.
                    //Buradan hangi ROW olduğu bulunuyor
                    //Bulunan ROW içinden değer alınıyor.
                    DataRow[] drs1 = ds.Tables["Data"].Select(string.Format("clsIndicatorSymbol_Id = {0}", nDateTimeValueIndex));
                    int rowcount = 0;
                    foreach (DataRow dr in drs1)
                    {
                        //Console.WriteLine(dr[0].ToString());
                        DateTime dDate3 = Convert.ToDateTime(dr[0].ToString());
                        if (dDate3.ToString("dd.MM.yyyy") == DateTime.Now.ToString("dd.MM.yyyy"))
                        {
                            //Console.WriteLine(dr[0].ToString());
                            DataRow[] drs2 = ds.Tables["Data"].Select(string.Format("clsIndicatorSymbol_Id = {0}", nDataValueIndex));
                            if (drs2.Any())
                            {
                                //Console.WriteLine(drs2[rowcount][0].ToString());
                                //TODO : statik olduğu için kapatıldı.
                                // _logger.LogInformation(dr[0].ToString() + " - " + drs2[rowcount][0].ToString() + Environment.NewLine);
                                if (string.IsNullOrEmpty(drs2[rowcount][0].ToString()))
                                {
                                    return null;
                                }

                                if (sIndicatorName == "BBW")
                                {
                                    DataRow[] drs3 = ds.Tables["Data"].Select(string.Format("clsIndicatorSymbol_Id = {0}", 2));
                                    if (drs3.Any())
                                    {
                                        nBBW_20_2_Middle = Convert.ToDouble(drs3[rowcount][0].ToString().Replace(".", ","));
                                    }
                                    DataRow[] drs4 = ds.Tables["Data"].Select(string.Format("clsIndicatorSymbol_Id = {0}", 3));
                                    if (drs4.Any())
                                    {
                                        nBBW_20_2_Down = Convert.ToDouble(drs4[rowcount][0].ToString().Replace(".", ","));
                                    }
                                }
                                else if (sIndicatorName == "MACD")
                                {
                                    //MACD Trigger Değeri
                                    DataRow[] drs3 = ds.Tables["Data"].Select(string.Format("clsIndicatorSymbol_Id = {0}", 2));
                                    if (drs3.Any())
                                    {
                                        nMACD_Trigger_9 = Convert.ToDouble(drs3[rowcount][0].ToString().Replace(".", ","));
                                    }
                                }
                                else if (sIndicatorName == "STOF")
                                {
                                    DataRow[] drs3 = ds.Tables["Data"].Select(string.Format("clsIndicatorSymbol_Id = {0}", 2));
                                    if (drs3.Any())
                                    {
                                        nFStoch_D_3 = Convert.ToDouble(drs3[rowcount][0].ToString().Replace(".", ","));
                                    }
                                }
                                else if (sIndicatorName == "STOS")
                                {
                                    DataRow[] drs3 = ds.Tables["Data"].Select(string.Format("clsIndicatorSymbol_Id = {0}", 2));
                                    if (drs3.Any())
                                    {
                                        nSStoch_D_3 = Convert.ToDouble(drs3[rowcount][0].ToString().Replace(".", ","));
                                    }
                                }

                                return Convert.ToDouble(drs2[rowcount][0].ToString().Replace(".", ","));
                            }
                            return null;
                        }
                        rowcount++;
                    }
                }
            }
        }
        catch
        {
            var tuple = Tuple.Create(sSembol, sIndicatorURL);
            LErrorSembols.Add(tuple);
            return null;
        }

        return null;
    }

    private async Task<double?> GetIndikatorForeksData(string sembol, string sIndicatorURL, string sFieldName = "")
    {
        try
        {
            var foreksData = await _bigparaHttpClient.FetchDataAsync<ForeksIndikatorData>(sIndicatorURL);
            if (foreksData == null) { return 0; }

            var data = foreksData.FirstOrDefault(x => DateHelper.UnixTimeToLocalDateTime(x.Date) == DateTime.Today);
            if (data == null) { return 0; }

            IndikatorValue result = (IndikatorValue)data.Result.GetType().GetProperty(sFieldName).GetValue(data.Result, null);

            switch (sembol)
            {
                case "MOM":
                case "SMA":
                    return result.M;
                case "MACD":
                    nMACD_Trigger_9 = result.S;
                    return result.M;
                case "BOL":
                    nBBW_20_2_Middle = result.M;
                    nBBW_20_2_Down = result.L;
                    return result.U;
                case "CCI":
                    return result.C;
                case "ADX":
                    return result.A;
                case "RSI":
                    return result.R;
                case "SAR":
                    return result.S;
                case "STOF":
                    nFStoch_D_3 = result.D;
                    return result.K;
                case "STOS":
                    nSStoch_D_3 = result.D;
                    return result.K;
                default:
                    return 0;
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
            return 0;
        }
    }
}
