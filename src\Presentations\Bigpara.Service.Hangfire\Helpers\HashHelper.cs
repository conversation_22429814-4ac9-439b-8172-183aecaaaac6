﻿using System.Text;

namespace Bigpara.Service.Hangfire.Helpers;

public static class Has<PERSON><PERSON><PERSON><PERSON>
{
    public static string SHA512HashData(this string data)
    {
        using var hasher = System.Security.Cryptography.SHA512.Create();
        byte[] hashedData = hasher.ComputeHash(Encoding.Unicode.GetBytes(data));

        StringBuilder sb = new StringBuilder(hashedData.Length * 2);
        foreach (byte b in hashedData)
        {
            sb.AppendFormat("{0:x2}", b);
        }

        return sb.ToString();
    }
}
