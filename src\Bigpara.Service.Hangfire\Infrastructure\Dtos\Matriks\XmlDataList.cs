using Newtonsoft.Json;

namespace Bigpara.Service.Hangfire.Infrastructure.Dtos.Matriks;

public class XmlDataList
{
    public string Id { get; set; }
    public DateTime? Time { get; set; }
    public string Country { get; set; }
    public string Indicator { get; set; }
    public string Per { get; set; }
    public string Priority { get; set; }
    public string Consensus { get; set; }
    public string Previous { get; set; }
    public string Actual { get; set; }
}

public partial class FinancialCalendar
{
    [JsonProperty("cid")]
    public string NewsId { get; set; }

    [JsonProperty("country")]
    public string Country { get; set; }

    [JsonProperty("event")]
    public string Indicator { get; set; }

    [JsonProperty("period")]
    public string Per { get; set; }

    [JsonProperty("importance")]
    public int Priority { get; set; }

    [JsonProperty("actual")]
    public string Actual { get; set; }

    [JsonProperty("forecast")]
    public string Consensus { get; set; }

    [JsonProperty("previous")]
    public string Previous { get; set; }

    [JsonProperty("lastUpdateDate")]
    public long NewsDate { get; set; }

    [JsonProperty("date")]
    public long UpdateDate { get; set; }

    [JsonProperty("tsi")]
    public string Hour { get; set; }

}