﻿using Quark.Models.Entities;
using Quark.Models.Entities.Containers;
using Quark.Models.Entities.Contents;
using Quark.Models.Entities.Properties;

namespace Bigpara.Application.Contracts.Repositories.Quark.Core;

public interface ICmsRepository
{
    Task<long> GetNextIncrementIdAsync(string collectionName, string application);
    Task<List<Folder>> GetFoldersAsync(string collectionName);
    Task<List<Property>> GetContentPropertiesAsync(string typeName);
    Task InsertContentAsync(string collectionName, Article article);
    Task<List<Ancestor>> GetAncestorsAsync(string collectionName, string contentPath);
    Task<IList<Permission>> GetPermissionsAsync(string collectionName, string contentPath);
    Task<long> GetLastKapNewsTimestampAsync(string collectionName, string application, string path);
    Task<bool> ContentExistsAsync(string collectionName, string referenceId);
}
