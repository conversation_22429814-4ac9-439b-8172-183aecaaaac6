﻿using Bigpara.Application.Common;

namespace Bigpara.Application.Features.Tools.Models
{
    public class GetExchangeRatesDto
    {
        public decimal Amount { get; set; }
        public string Symbol { get; set; }
        public double BidPrice { get; set; }
        public double AskPrice { get; set; }
        public decimal Rate { get; set; }
    }

    public class GetExchangeRatesQueryResponse: BaseResponse
    {
        public GetExchangeRatesQueryResponse()
        {
            Data = new List<GetExchangeRatesDto>();
        }
        public List<GetExchangeRatesDto> Data { get; set; }
   
    }
}
