﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.Services.Client" Version="5.8.5" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Quark.NetStandard.Shared" Version="5.1.108" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Applications\Bigpara.Application\Bigpara.Application.csproj" />
    <ProjectReference Include="..\Bigpara.Infrastructure.Cache\Bigpara.Infrastructure.Cache.csproj" />
  </ItemGroup>

</Project>
