﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs;

public class SektorRecurringJob : IRecurringJob
{
    private readonly ForeksHttpClient _foreksHttpClient;
    private readonly ILogger<SektorRecurringJob> _logger;
    private readonly IConfiguration _configuration;
    private readonly ISectorService _sectorService;
    private readonly INotificationService _notificationService;

    private readonly int _maxConcurrentTasks;

    public SektorRecurringJob(
        ForeksHttpClient foreksHttpClient,
        ILogger<SektorRecurringJob> logger,
        IConfiguration configuration,
        ISectorService sectorService,
        INotificationService notificationService)
    {
        _foreksHttpClient = foreksHttpClient;
        _logger = logger;
        _configuration = configuration;
        _maxConcurrentTasks = _configuration.GetValue("SectorProcessing:MaxConcurrentTasks", 10);
        _sectorService = sectorService;
        _notificationService = notificationService;
    }

    public string Name => "SectorRecurringJob";
     public IEnumerable<string> Crons => ["0 0 8 * * ?"];

    public async Task ExecuteAsync()
    {
        try
        {
            await ProcessDataTypeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{Name} sırasında kritik hata oluştu.");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    public async Task ProcessDataTypeAsync()
    {
        try
        {
            var url = $"{_configuration["Feed:SektorApi"]}";
            if (string.IsNullOrEmpty(url))
            {
                _logger.LogError("Feed:SektorApi değeri bulunamadı.");
                await _notificationService.NotifyErrorAsync(Name, "Feed:SektorApi değeri bulunamadı.");
                return;
            }

            var result = await _foreksHttpClient.FetchDataAsync<SektorlerDto>(url);

            if (result == null || result.Count == 0)
            {
                _logger.LogWarning("Çekilen veri boş veya null.");
                return;
            }

            using var semaphore = new SemaphoreSlim(_maxConcurrentTasks);

            foreach (var item in result)
            {
                await semaphore.WaitAsync();

                try
                {
                    await ProcessSingleItemAsync(item);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"İşlem sırasında hata oluştu: {item.StrKod}");
                    await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.StrKod}, Hata: {ex} , Hata Mesajı: {ex.Message}");
                }
                finally
                {
                    semaphore.Release();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    private async Task ProcessSingleItemAsync(SektorlerDto sektorlerDto)
    {
        try
        {
            await _sectorService.Change(sektorlerDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sektor işleme hatası: {sektorlerDto.StrAd}");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {sektorlerDto.StrKod}, Hata: {ex} , Hata Mesajı: {ex.Message}");
        }
    }
}
