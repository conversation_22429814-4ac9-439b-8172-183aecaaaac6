{"SeriLog": {"Url": "http://***************:9200", "Platform": "Test-Bigpara.Service.Hangfire", "Env": "Test"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Production": "false", "ConnectionStrings": {"Matriks": "Server=***************;Database=DBMATRIKSv2;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;", "Bigpara": "Server=***************;Database=TestBigpara;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;", "Foreks": "Server=***************;Database=TestForeks;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;", "Hangfire": "Server=***************;Database=TestForeksHangfire;User Id=hangfire_user;Password=**********;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;", "Redis": "***************:6379,password=BIGpara!com!tr!2014!", "Mongo": "mongodb://cms_admin:Cc112233..!!@************:27017/CMS"}, "CacheDuration": {"SymbolGet": 720, "SymbolSet": 720}, "EmailSettings": {"SmtpServer": "smtp15.kolaymail.com", "Port": 587, "SenderEmail": "<EMAIL>", "SenderPassword": "kuIxcykKlYGQ", "DefaultRecipient": ["<EMAIL>", "<EMAIL>"], "Environment": "Local"}, "TeamsSettings": {"WebhookUrl": "https://prod-19.westeurope.logic.azure.com:443/workflows/7be2fd24ef604dbb8569548ba1bbe5ce/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=c_OIVFzGlBDwCF6E0JL879m9DgnoEvkbsdocN4vVlQs", "Environment": "Local"}, "Redis": {"ClientHost": "***************", "AuthKey": "BIGpara!com!tr!2014", "ConnectTimeout": "10000", "ResponseTimeout": "5000"}, "RedisDefaultDbIndex": "0"}