﻿using Bigpara.Domain.Bitcoin;
using Bigpara.Domain.ExtendedTypes;

namespace Bigpara.Service.Hangfire.Services.Interfaces;

public interface IBitcoinService
{
    Task<List<MarketsOnlineDetails>> GetAllMarketOnlines();
    Task<List<MarketsHistory>> GetMarketHistoryBySymbol(string symbol);
    Task<MarketsOnline> GetMarketsOnlineBySymbolAndDate(string symbol, DateTime dt);
    Task<List<MarketsOnline>> GetMarketsOnlineLastDay(string symbol);
    Task<int> AddMarketsOnline(MarketsOnline marketsOnline);
    Task<int> UpdateMarketsOnline(MarketsOnline marketsOnline);
    Task<List<BitfinexOnline>> GetAllBitfinexOnlines();
    Task<List<BitfinexOnline>> GetBitfinexOnlineLastValueBySymbol(string symbol);
    Task<int> AddBitfinexOnline(BitfinexOnline bitfinexOnline);
}