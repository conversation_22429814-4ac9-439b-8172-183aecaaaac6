﻿using Bigpara.Domain.Matriks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Report;

public class GetSeansRaporu1DkHisseAktivitiBilgisiRecurringJob : IRecurringJob
{
    private readonly ISeansRaporlariService _seansRaporlariService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<GetSeansRaporu1DkHisseAktivitiBilgisiRecurringJob> _logger;

    public GetSeansRaporu1DkHisseAktivitiBilgisiRecurringJob
    (
        ISeansRaporlariService seansRaporlariService,
        INotificationService notificationService,
        ILogger<GetSeansRaporu1DkHisseAktivitiBilgisiRecurringJob> logger
    )
    {
        _seansRaporlariService = seansRaporlariService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "GetSeansRaporu1DkHisseAktivitiBilgisiRecurringJob";
     public IEnumerable<string> Crons => ["* 9-17 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("GetSeansRaporu1DkHisseAktivitiBilgisiRecurringJob - Başladı ");
            await ProcessGetSeansRaporu1DkHisseAktivitiBilgisi();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    /// <summary>
    /// 1 dk gun ici top 1 hisse son durumu aliniyor
    /// </summary>
    public async Task ProcessGetSeansRaporu1DkHisseAktivitiBilgisi()
    {
        if (!_seansRaporlariService.IsActive())
            return;

        try
        {
            const string pattern = "Yüzde {0} oranında {1} {2} liradan işlem gören hissede {3} adet işlem yapıldı.";
            var data = await _seansRaporlariService.GetSeansRaporu1DkHisseAktivitiBilgisi();

            foreach (var item in data.Take(1))
            {
                var now = new DateTime(item.TARIH.Year, item.TARIH.Month, item.TARIH.Day, 12, 30, 0);
                var result = string.Format(pattern, item.YUZDEDEGISIM, item.YUZDEDEGISIM > 0 ? "artışla" : "düşüşle", item.KAPANIS, item.TARIH < now ? item.HACIM1.ToMoneyString("{0:N0}") : item.HACIM.ToMoneyString("{0:N0}"));

                var yon = 0;
                if (item.YUZDEDEGISIM > 0)
                {
                    yon = 1;
                }
                else if (item.YUZDEDEGISIM < 0)
                {
                    yon = -1;
                }
                var seansRaporu = new SeansRaporu
                {
                    HisseCumle = result,
                    Yon = yon,
                    HisseAdi = item.SEMBOL,
                    EklenmeTarihi = DateTime.Now
                };

                await _seansRaporlariService.CreateAndCacheSeansRaporu(seansRaporu);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            //EmailHelper.SendEmail("GetSeansRaporu10DkIslemHacmi", ex.Message);
            await _notificationService.NotifyErrorAsync($"GetSeansRaporu1DkHisseAktivitiBilgisi", ex.Message);
        }
    }
}
