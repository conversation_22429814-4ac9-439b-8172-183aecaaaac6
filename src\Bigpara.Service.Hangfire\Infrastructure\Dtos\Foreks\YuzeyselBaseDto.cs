﻿using Newtonsoft.Json;

namespace Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;

public abstract class YuzeyselBaseDto
{
    [JsonProperty("Code")]
    public string? SEMBOL { get; set; }

    [JsonProperty("LegacyCode")]
    public string LegacyCode { get; set; }

    [JsonProperty("_id")]
    public string? SocketId { get; set; }

    public DateTime Tarih { get; set; }

    [JsonProperty("DateTime")]
    public long UnixTime { get; set; }


    [JsonProperty("Bid")]
    public decimal? ALIS { get; set; }


    [JsonProperty("Ask")]
    public decimal? SATIS { get; set; }


    [JsonProperty("High")]
    public decimal? YUKSEK { get; set; }


    [JsonProperty("Low")]
    public decimal? DUSUK { get; set; }


    [JsonProperty("Close")]
    public decimal? KAPANIS { get; set; }


    [JsonProperty("PreviousClose")]
    public decimal? DUNKUKAPANIS { get; set; }
}
