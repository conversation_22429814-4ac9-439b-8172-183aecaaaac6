﻿using Bigpara.External.Quark.Filters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.External.Quark.Infrastructure
{
    public interface IQuarkRepository<T, out TDto>
    {
        /// <summary>
        /// Istek yapilacak Api url'ini belirler
        /// </summary>
        string BaseApiUrl { get; set; }

        /// <summary>
        /// Istenilen TDto nesnesini getirir 
        /// </summary>
        /// <param name="id">Content id</param>
        /// <param name="select">Geri donmesini istediginiz ozellikler</param>
        /// <returns>TDto</returns>
        TDto GetById(string id, string select = null);

        /// <summary>
        /// Istenilen sorguya gore listeyi getirir 
        /// </summary>
        /// <param name="id">Content id</param>
        /// <param name="filter">OData sorgusu icin kullanilacak filtreler</param>
        /// <returns>IEnumerable liste getirir</returns>
        IList<T> GetListById(string id, ODataFilter filter);

        /// <summary>
        /// Istenilen TDto nesnesini getirir 
        /// </summary>
        /// <param name="filter">OData sorgusu icin kullanilacak filtreler</param>
        /// <returns>TDto</returns>
        TDto GetDtoByFilter(ODataFilter filter);

        /// <summary>
        /// Istenilen T nesnesini getirir 
        /// </summary>
        /// <param name="filter">OData sorgusu icin kullanilacak filtreler</param>
        /// <returns>TDto</returns>
        T GetByFilter(ODataFilter filter);

        /// <summary>
        /// Istenilen sorguya gore listeyi getirir 
        /// </summary>
        /// <param name="filter">OData sorgusu icin kullanilacak filtreler</param>
        /// <returns>IEnumerable liste getirir</returns>
        IList<T> List(ODataFilter filter);
    }
}
