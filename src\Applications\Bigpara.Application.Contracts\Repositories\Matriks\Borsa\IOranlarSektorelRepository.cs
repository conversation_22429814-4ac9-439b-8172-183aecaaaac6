﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Application.Contracts.Repositories.Matriks.Borsa;

public interface IOranlarSektorelRepository
{
    Task<List<OranlarSektorel>> GetOranlarSektorels(string sembol);
    Task<List<OranlarSektorel>> GetSektorelMaliOranlar();
    Task<List<OranlarSektorel>> GetOranlarSektorelId(int id);
    Task<int> InsertOranSektorel(OranlarSektorel oranlarSektorel);
    Task<int> DeleteOranSektorel();
}
