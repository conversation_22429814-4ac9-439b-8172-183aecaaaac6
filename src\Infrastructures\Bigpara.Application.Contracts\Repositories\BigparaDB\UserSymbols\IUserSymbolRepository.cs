﻿using Bigpara.Domain.Bigpara;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Contracts.Repositories.BigparaDB.UserSymbols
{
    public interface IUserSymbolRepository
    {
        Task AddOrDeleteAsync(int operation,UserSembol userSembol);
        Task<UserSembol> GetUserSembolAsync(int userId, int sembolId);
        Task<List<UserSembol>> ListAsync(int userId, int pageIndex, int paseSize);
    }
}
