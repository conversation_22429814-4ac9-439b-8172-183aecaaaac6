﻿using Bigpara.Domain.Matriks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;


namespace Bigpara.Service.Hangfire.Jobs;

public class GetBistSeansSonaErdiRecurringJob : IRecurringJob
{
    private readonly ISeansRaporlariService _seansRaporlariService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<GetBistSeansSonaErdiRecurringJob> _logger;

    public GetBistSeansSonaErdiRecurringJob
    (
        ISeansRaporlariService seansRaporlariService,
        INotificationService notificationService,
        ILogger<GetBistSeansSonaErdiRecurringJob> logger
    )
    {
        _seansRaporlariService = seansRaporlariService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "GetBistSeansSonaErdiRecurringJob";
    public IEnumerable<string> Crons => ["30 20 * * 1-5"];

    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("GetBistSeansSonaErdiRecurringJob - Başladı ");
            await ProcessBistSeansSonaErdi();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    public async Task ProcessBistSeansSonaErdi()
    {
        if (!_seansRaporlariService.IsActive())
            return;

        try
        {
            var seansRaporuYuzeysel = await _seansRaporlariService.GetSeansRaporuBistAcilisKapanis();
            if (seansRaporuYuzeysel == null) return;
            var seansRaporu = new SeansRaporu
            {
                HisseCumle = "IMKB işlemleri sona erdi ( XU100 ).",
                Yon = 0,
                //TODO : Boş gönderiliyor ?
                HisseAdi = string.Empty,
                EklenmeTarihi = seansRaporuYuzeysel.TARIH
            };

            //CreateSeansRaporu(seansRaporu, MethodName);
            await _seansRaporlariService.CreateAndCacheSeansRaporu(seansRaporu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            //EmailHelper.SendEmail("GetBistSeansSonaErdi", ex.Message);
            await _notificationService.NotifyErrorAsync($"GetBistSeansSonaErdi", ex.Message);
        }
    }
}
