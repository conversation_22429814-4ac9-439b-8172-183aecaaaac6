﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Application.Contracts.Repositories.Matriks.OtomatikTeknikYorum;

public interface IOtomatikTeknikYorumlarRepository
{
    Task<List<OtomatikTeknikYorumlar>> GetOtomatikTeknikYorumlars(string sembol, DateTime dateTime);
    Task<int> CreateOrUpdateOtomatikTeknikYorumlar(OtomatikTeknikYorumlar otomatikTeknikYorumlar);
    Task<List<OtomatikTeknikYorumlar>> GetOtomatikTeknikYorumlars(string sembol);
}
