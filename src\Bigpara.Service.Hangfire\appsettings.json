{
  "Hangfire": {
    "Username": "admin",
    "Password": "123456"
  },
  "SeriLog": {
    "Url": "http://***************:9200",
    "Platform": "Test-Bigpara.Service.Hangfire",
    "Env": "Test"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Production": "true",
  "ConnectionStrings": {
    "Matriks": "Server=***************;Database=DBMATRIKSv2;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;",
    "Bigpara": "Server=***************;Database=TestBigpara;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;",
    "Foreks": "Server=***************;Database=TestForeks;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;",
    "Hangfire": "Server=***************;Database=TestForeksHangfire;User Id=hangfire_user;Password=**********;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;",
    "Redis": "***************:6379,password=BIGpara!com!tr!2014!",
    "Mongo": "mongodb://cms_admin:Cc112233..!!@************:27017/CMS"
  },
  "Bigpara": {
    "Socket": "http://bigpara-realtime-signalr",
    "Api": "http://bigpara-realtime-signalr"
  },
  "Foreks": {
    "SnapShotApi": "https://snapshot.foreks.com",
    "Socket": "http://",
    "CloudApi": "https://cloud.foreks.com",
    "Auth": {
      "RealTimeUsername": "hurriyetbigparasnapshot",
      "RealTimePassword": "87yhb+ol&n",
      "SnapShotRealTimeUsername": "hurriyetbigpara2",
      "SnapShotRealTimePassword": "h7d6eg3d",
      "SnapShotDelayedUsername": "hurriyetbigpara",
      "SnapShotDelayedPassword": "hrrt52ncx50",
      "WebUsername": "bigpara-web",
      "WebPassword": "dfgtBnm55!"
    },
    "SnapShot": {
      "Hisse": {
        "Url": "security=E,F,R,TF1,TF2,TS1&securityType=Stock,Fund,Right&domain=BIST&exchange=BIST&marketSector=Equity&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      },
      "Warrant": {
        "Url": "domain=BIST&exchange=BIST&marketSector=Equity&securityType=Warrant&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      },
      "Endeks": {
        "Url": "domain=BIST&exchange=BIST&marketSector=Index&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      },
      "Parite": {
        //"Url": "exchange=SPOT&securityType=Cross&marketSector=Currency&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
        "Url": "code=JPYTRY,EURUSD,EURGBP,CHFJPY,GBPJPY,USDJPY,EURJPY,USDSAR,EURAUD,EURCAD,AUDJPY,GBPCAD,CADJPY,CADCHF,AUDCHF,GBPAUD,EURSAR,AUDUSD,GBPCHF,EURTRY,USDTRY,XAUUSD,XAGUSD,XAGEUR,CHFTRY,GBPTRY,GBPEUR,EURCHF,GBPUSD,XPDUSD,XPTUSD,COPPERUS:CFD,BRENTSPOT,BTCUSD,ETHUSD,USDZAR,USDBGN,XTIUSD,XPTTRY&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      },
      "Viop": {
        "Url": "domain=VIOP&exchange=BIST&securityType=Future&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      },
      "SerbestPiyasa": {
        //"Url": "exchange=GrandBazaar&securityType=Cross&marketSector=Currency&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
        "Url": "legacyCode=AKYNUSD&legacyCode=AKYNEUR&legacyCode=SKBNKUSD&legacyCode=SKBNKEUR&legacyCode=AKBNKUSD&legacyCode=AKBNKEUR&legacyCode=ISBNKUSD&legacyCode=ISBNKEUR&legacyCode=YKBNKUSD&legacyCode=YKBNKEUR&legacyCode=GARANUSD&legacyCode=GARANEUR&legacyCode=FINBNUSD&legacyCode=FINBNEUR&legacyCode=AKBNKGBP&legacyCode=GARANGBP&legacyCode=YKBNKGBP&legacyCode=AKYNHAS&legacyCode=AKYNBIL22&legacyCode=AKYNCHF&legacyCode=AKYNGBP&legacyCode=AKYNSEK&legacyCode=AKYNJPY&legacyCode=AKYNNOK&legacyCode=AKYNDKK&legacyCode=AKYNAUD&legacyCode=AKYNCAD&legacyCode=GARANSAR&legacyCode=ISBNKSAR&legacyCode=VAKIFUSD&legacyCode=VAKIFEUR&legacyCode=AKYNSAR&legacyCode=ISBNKAUD&legacyCode=ISBNKDKK&legacyCode=ISBNKGBP&legacyCode=ISBNKSEK&legacyCode=ISBNKCHF&legacyCode=ISBNKJPY&legacyCode=ISBNKCAD&legacyCode=ISBNKKWD&legacyCode=ISBNKNOK&legacyCode=SGZIYNET&legacyCode=SGBESLI&legacyCode=SGGREMSE&legacyCode=SGIKIBUCUK&legacyCode=USGLD&legacyCode=AKYNBIL22,G14BIL:HRM,GCEYREK:HRM,GYARIM:HRM,XHGLD,GTAM:HRM,G22BIL:HRM,TAHVIL,TAHVILF,SGLD,EURGLD,USGLDKG,SEUR,SGYARIM,SGCEYREK,XSLV,DVZSP1,AKYNBIL22,SG14BIL,SG18BIL,SG22BIL,SGATA,XGLD,R_O_N,SCUM,SAUD,SCAD,SDKK,SNOK,SSAR,SSEK,SRUB&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      },
      "DunyaEndeksleri": {
        "Url": "code=BEL20,AEX,CAC40,PSI20,AORD,BRENT,PALLADIUM:CFD,PLATINUM:CFD,COPPERUS:CFD,USCOCOA:CFD,USCOFFEEC:CFD,CORN:CFD,USCOTTON2:CFD,HEATINGOIL:CFD,WHEAT:CFD,USDX:CFD,TKC,KLSE,AMX,ATGD,USSUGAR11:CFD,LIVECATTLE:CFD&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      },
      "TahvilKesinOzet": {
        "Url": "domain=BAP&exchange=BIST&market=FKESN&status=ACTIVE&f=Code,Valor,Last,LastRate,CompoundRate,TotalTurnover,LiveStatus"
      },
      "TahvilRepoOzet": {
        "Url": "domain=BAP&exchange=BIST&securityType=Repo&market=FREPN&status=ACTIVE&f=DateTime,Code,Valor,DTE,Low,High,VWAP,TotalTurnover,LiveStatus"
      },
      "TCMB": {
        "Url": "exchange=TCMB&status=ACTIVE&f=Code,LegacyCode,DateTime,Bid,Ask,Open,High,Low,Close,TotalVolume,VWAP,TotalTurnover,PreviousClose,UpperLimit,LowerLimit,YTDHigh,YTDLow,MTDHigh,MTDLow,WTDHigh,WTDLow,PreviousYearClose,PreviousMonthClose,PreviousWeekClose,pttRow,Volume,Capital,NetProfit,LastPeriod,PriceEarning,MarketValue,FreeFloatStockSize,NetCapital,Beta200,SettlementPrice,PreviousSettlementPrice,OpenInterest,SettlementType,Direction,DailyDirection,Last,PreviousLast,DailyChangePercent,DailyChange,TheoPrice,XU030"
      }
    },
    "Cloud": {
      "Bolunme": {
        "Url": "historical-service/split/from"
      },
      "Indikator": {
        "Url": "historical-service/indicator/code/"
      },
      "PiyasaTakvimi": {
        "Url": "historical-service/calendar/from/"
      },
      "Contents": {
        "Url": "api/v3/news",
        "Source": "KAP",
        "Locale": "tr",
        "TakeCount": 10
      }
    }
  },
  "Feed": {
    "SymbolApi": "https://feed-definition.foreks.com/symbol/search",
    "SektorApi": "https://feed-definition.foreks.com/sector/search?status=ACTIVE",
    "Parite": {
      "Url": "legacyCode=JPY/TRL&legacyCode=EUR/USD&legacyCode=EUR/GBP&legacyCode=CHF/JPY&legacyCode=GBP/JPY&legacyCode=USD/JPY&legacyCode=EUR/JPY&legacyCode=USD/SAR&legacyCode=EUR/AUD&legacyCode=EUR/CAD&legacyCode=AUD/JPY&legacyCode=GBP/CAD&legacyCode=CAD/JPY&legacyCode=CAD/CHF&legacyCode=AUD/CHF&legacyCode=GBP/AUD&legacyCode=EUR/SAR&legacyCode=AUD/USD&legacyCode=GBP/CHF&legacyCode=EUR/TRL&legacyCode=USD/TRL&legacyCode=XAU/USD&legacyCode=XAG/USD&legacyCode=XAG/EUR&legacyCode=CHF/TRL&legacyCode=GBP/TRL&legacyCode=GBP/EUR&legacyCode=EUR/CHF&legacyCode=GBP/USD&legacyCode=XPD/USD&legacyCode=XPT/USD&legacyCode=COPPUS,CFD&legacyCode=BRENTSPOT&legacyCode=BTC/USD&legacyCode=ETH/USD&legacyCode=USD/ZAR&legacyCode=USD/BGN&legacyCode=XTI/USD&legacyCode=XPT/TRY&status=ACTIVE",
      "Id": "3"
    },
    "Hisse": {
      "Url": "domain=BIST&exchange=BIST&marketSector=Equity&security=E&security=F&security=R&security=TF1&security=TF2&security=TS1&security=V&status=ACTIVE",
      "Id": "4"
    },
    "TCMB": {
      "Url": "exchange=TCMB&status=ACTIVE",
      "Id": "6"
    },
    "Viop": {
      "Url": "domain=VIOP&exchange=BIST&status=ACTIVE",
      "Id": "9"
    },
    "SerbestPiyasa": {
      "Url": "legacyCode=SGZIYNET&legacyCode=SGBESLI&legacyCode=SGGREMSE&legacyCode=SGIKIBUCUK&legacyCode=USGLD&legacyCode=SG14BIL&legacyCode=SGCEYREK&legacyCode=SGYARIM&legacyCode=SGLD&legacyCode=EURGLD&legacyCode=USGLDKG&legacyCode=SEUR&legacyCode=SGYARIM&legacyCode=SGCEYREK&legacyCode=XSLV&legacyCode=DVZSP1&legacyCode=AKYNBIL22&legacyCode=SG14BIL&legacyCode=SG18BIL&legacyCode=SG22BIL&legacyCode=SGATA&legacyCode=XGLD&legacyCode=R_O_N&legacyCode=SCUM&status=ACTIVE",
      "Id": "26"
    },
    "Endeks": {
      "Url": "domain=BIST&exchange=BIST&marketSector=Index&status=ACTIVE",
      "Id": "27"
    }
  },
  "CacheDuration": {
    "SymbolGet": 720,
    "SymbolSet": 720
  },
  "EmailSettings": {
    "SmtpServer": "smtp15.kolaymail.com",
    "Port": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "kuIxcykKlYGQ",
    "DefaultRecipient": [ "<EMAIL>", "<EMAIL>" ],
    "Environment": "Local"
  },
  "TeamsSettings": {
    "WebhookUrl": "https://prod-70.westeurope.logic.azure.com:443/workflows/9d80d15676994764bed16f11988a88f5/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=ExUCyLGIMAYb34QAtTIEoL3mze7xU4RWAjGDq4gtEWc",
    "Environment": "Local"
  },
  "Redis": {
    "ClientHost": "***************",
    "AuthKey": "BIGpara!com!tr!2014",
    "ConnectTimeout": "10000",
    "ResponseTimeout": "5000"
  },
  "RedisDefaultDbIndex": "0",
  "RealTimeDataActive": true,
  "RecurringJobs": {
    "IsEnabled": true,
    "Currency": {
      "IsEnabled": true
    },
    "CurrencyRealTime": {
      "IsEnabled": false
    },
    "Stocks": {
      "IsEnabled": true
    },
    "GrandBazaar": {
      "IsEnabled": false
    },
    "Symbol": {
      "IsEnabled": true
    },
    "Sector": {
      "IsEnabled": true
    },
    "Split": {
      "IsEnabled": true
    },
    "BistEnd": {
      "IsEnabled": true
    },
    "Viop": {
      "IsEnabled": true
    },
    "Index": {
      "IsEnabled": true
    },
    "Bonds": {
      "IsEnabled": true
    },
    "Doviz": {
      "IsEnabled": false
    },
    "Crypto": {
      "IsEnabled": false
    },
    "Indikators": {
      "IsEnabled": false
    },
    "KapNews": {
      "IsEnabled": false
    },
    "PiyasaTakvimi": {
      "IsEnabled": false
    },
    "Report": {
      "IsEnabled": false
    },
    "UserMetrics": {
      "IsEnabled": false
    },
    "UserServices": {
      "IsEnabled": false
    },
    "MarketAnalytics": {
      "IsEnabled": false
    },
    "Media": {
      "IsEnabled": false
    }
  }
}