﻿namespace Bigpara.Service.Hangfire.Infrastructure.HttpClients.Interfaces
{
    public static class Fields
    {
        public const string Code = "E";

        public const string Time = "t";

        public const string SerialCode = "se";

        public const string ShortMarketCode = "sm";

        public const string Open = "O";

        public const string VWAP = "wa";

        public const string TradeType = "tt";

        public const string BuyerCode = "bC";

        public const string SellerCode = "sC";

        public const string TradeNumber = "T";

        public const string Price = "P";

        public const string Size = "S";

        public const string TradeTime = "TT";

        public const string TransactionDirection = "TD";

        public const string TradedValue = "TV";

        public const string BidOrAskDirection = "BD";

        public const string LastDirection = "LD";

        public const string PreviousDirection = "PD";

        public const string Last = "l";

        public const string PreviousLast = "PL";

        public const string Volume = "v";

        public const string Turnover = "W";

        public const string DailyChange = "c";

        public const string DailyChangePercent = "C";

        public const string WeeklyChange = "wc";

        public const string WeeklyChangePercent = "wp";

        public const string MonthlyChange = "mc";

        public const string MonthlyChangePercent = "mp";

        public const string YearlyChange = "yc";

        public const string YearlyChangePercent = "yp";

        public const string Direction = "d";

        public const string Bid = "b";

        public const string Ask = "a";

        public const string Low = "L";

        public const string High = "h";

        public const string MidPrice = "Mp";

        public const string Volatility = "vo";

        public const string MMCode = "mm";

        public const string MMBidQuotation = "bq";

        public const string MMAskQuotation = "aq";

        public const string MMBidQuotationAmount = "Bq";

        public const string MMAskQuotationAmount = "Aq";

        public const string WTDLow = "Wl";

        public const string WTDHigh = "Wh";

        public const string PreviousWeekClose = "Wc";

        public const string MTDLow = "Ml";

        public const string MTDHigh = "Mh";

        public const string PreviousMonthClose = "Mc";

        public const string YTDLow = "Yl";

        public const string YTDHigh = "Yh";

        public const string PreviousYearClose = "Yc";

        public const string Close = "cl";

        public const string PreviousClose = "pc";

        public const string ClosingSessionPrice = "Cs";

        public const string LowerLimit = "Ll";

        public const string UpperLimit = "Ul";

        public const string TickSize = "tS";

        public const string Group = "Gr";

        public const string BasePrice = "bp";

        public const string TradingMethod = "tm";

        public const string Status = "ss";

        public const string TotalTurnover = "tT";

        public const string TotalVolume = "tV";

        public const string BidRatio = "BR";

        public const string AskRatio = "AR";

        public const string BidVWAP = "ba";

        public const string BidTotalAmount = "bl";

        public const string AskVWAP = "aa";

        public const string AskTotalAmount = "al";

        public const string PeriodicVWAP = "pW";

        public const string BidPrice0 = "b0";

        public const string BidPrice1 = "b1";

        public const string BidPrice2 = "b2";

        public const string BidPrice3 = "b3";

        public const string BidPrice4 = "b4";

        public const string BidPrice5 = "b5";

        public const string BidPrice6 = "b6";

        public const string BidPrice7 = "b7";

        public const string BidPrice8 = "b8";

        public const string BidPrice9 = "b9";

        public const string BidAveragePrice = "bg";

        public const string BidAmount0 = "v0";

        public const string BidAmount1 = "v1";

        public const string BidAmount2 = "v2";

        public const string BidAmount3 = "v3";

        public const string BidAmount4 = "v4";

        public const string BidAmount5 = "v5";

        public const string BidAmount6 = "v6";

        public const string BidAmount7 = "v7";

        public const string BidAmount8 = "v8";

        public const string BidAmount9 = "v9";

        public const string BidAmountTotal = "vt";

        public const string BidQty0 = "q0";

        public const string BidQty1 = "q1";

        public const string BidQty2 = "q2";

        public const string BidQty3 = "q3";

        public const string BidQty4 = "q4";

        public const string BidQty5 = "q5";

        public const string BidQty6 = "q6";

        public const string BidQty7 = "q7";

        public const string BidQty8 = "q8";

        public const string BidQty9 = "q9";

        public const string BidQtyTotal = "qt";

        public const string BidTime0 = "t0";

        public const string BidTime1 = "t1";

        public const string BidTime2 = "t2";

        public const string BidTime3 = "t3";

        public const string BidTime4 = "t4";

        public const string BidTime5 = "t5";

        public const string BidTime6 = "t6";

        public const string BidTime7 = "t7";

        public const string BidTime8 = "t8";

        public const string BidTime9 = "t9";

        public const string AskPrice0 = "a0";

        public const string AskPrice1 = "a1";

        public const string AskPrice2 = "a2";

        public const string AskPrice3 = "a3";

        public const string AskPrice4 = "a4";

        public const string AskPrice5 = "a5";

        public const string AskPrice6 = "a6";

        public const string AskPrice7 = "a7";

        public const string AskPrice8 = "a8";

        public const string AskPrice9 = "a9";

        public const string AskAveragePrice = "ag";

        public const string AskAmount0 = "w0";

        public const string AskAmount1 = "w1";

        public const string AskAmount2 = "w2";

        public const string AskAmount3 = "w3";

        public const string AskAmount4 = "w4";

        public const string AskAmount5 = "w5";

        public const string AskAmount6 = "w6";

        public const string AskAmount7 = "w7";

        public const string AskAmount8 = "w8";

        public const string AskAmount9 = "w9";

        public const string AskAmountTotal = "wt";

        public const string AskQty0 = "k0";

        public const string AskQty1 = "k1";

        public const string AskQty2 = "k2";

        public const string AskQty3 = "k3";

        public const string AskQty4 = "k4";

        public const string AskQty5 = "k5";

        public const string AskQty6 = "k6";

        public const string AskQty7 = "k7";

        public const string AskQty8 = "k8";

        public const string AskQty9 = "k9";

        public const string AskQtyTotal = "kt";

        public const string AskTime0 = "z0";

        public const string AskTime1 = "z1";

        public const string AskTime2 = "z2";

        public const string AskTime3 = "z3";

        public const string AskTime4 = "z4";

        public const string AskTime5 = "z5";

        public const string AskTime6 = "z6";

        public const string AskTime7 = "z7";

        public const string AskTime8 = "z8";

        public const string AskTime9 = "z9";

        public const string Contracts = "cs";

        public const string WOWLow = "wL";

        public const string WOWLowDate = "wD";

        public const string WOWHigh = "wH";

        public const string WOWHighDate = "wI";

        public const string WOWPreviousClose = "wC";

        public const string MOMLow = "mL";

        public const string MOMLowDate = "mD";

        public const string MOMHigh = "mH";

        public const string MOMHighDate = "mI";

        public const string MOMPreviousClose = "mC";

        public const string YOYLow = "yL";

        public const string YOYLowDate = "yD";

        public const string YOYHigh = "yH";

        public const string YOYHighDate = "yI";

        public const string YOYPreviousClose = "yC";

        public const string Valor = "V";

        public const string BidRate = "Br";

        public const string AskRate = "Ar";

        public const string LastTradeDate = "Lt";

        public const string LastRate = "Lr";

        public const string CY = "Cy";

        public const string DTM = "DT";

        public const string AccruedInterest = "AL";

        public const string BidSettlementPrice = "BSP";

        public const string AskSettlementPrice = "ACP";

        public const string LastSettlementPrice = "LCP";

        public const string DTE = "DTE";

        public const string SimpleRate = "Sr";

        public const string CompoundRate = "Cr";

        public const string MMValor = "Mv";

        public const string Type = "TY";

        public const string MMQuotation = "Mq";

        public const string MMQuoteRate = "Mr";

        public const string MMQuoteAmount = "Ma";

        public const string CPI = "CPI";

        public const string PreviousCloseRate = "PR";

        public const string PreviousCloseDate = "pD";

        public const string LowRate = "lR";

        public const string HighRate = "hR";

        public const string Valor2 = "VA";

        public const string Days = "Days";

        public const string VWAR = "vW";

        public const string Date = "D";

        public const string WTDLowDate = "Wd";

        public const string WTDHighDate = "Wi";

        public const string MTDLowDate = "Md";

        public const string MTDHighDate = "Mi";

        public const string YTDLowDate = "Yd";

        public const string YTDHighDate = "Yi";

        public const string ExpireDate = "eD";

        public const string ContinousCode = "on";

        public const string Instrument = "in";

        public const string Board = "br";

        public const string Session = "si";

        public const string PreviousSettlementPrice = "psp";

        public const string SettlementPrice = "sp";

        public const string TotalPremium = "pp";

        public const string OpenInterest = "oi";

        public const string OpenInterestChange = "oc";

        public const string UnderlyingSecurity = "us";

        public const string OptionType = "oy";

        public const string OptionClass = "oC";

        public const string StrikePrice = "Sp";

        public const string SecurityGroup = "Sg";

        public const string SecurityGroupNo = "SG";

        public const string ListingDate = "li";

        public const string ContractMultiplier = "cm";

        public const string SettlementType = "sT";

        public const string ParentContract = "Pc";

        public const string ActionType = "At";

        public const string MarginPercentage = "mP";

        public const string TradingUnitCollateral = "TU";

        public const string ContinousCode2 = "c2";

        public const string ForeksUnderlyingSecurity = "fus";

        public const string SecurityCount = "ic";

        public const string ShortName = "sn";

        public const string ShortNameEnglish = "sne";

        public const string TradeCondition = "rc";

        public const string Source = "So";

        public const string Stocks = "ST";

        public const string Title = "tI";

        public const string BidTradeTotalVolume = "BV";

        public const string AskTradeTotalVolume = "AV";

        public const string BidTradeTotalTurnover = "BT";

        public const string AskTradeTotalTurnover = "AT";

        public const string PriceStep = "pS";

        public const string EquilibriumPrice = "eP";

        public const string EquilibriumVolume = "eV";

        public const string EPBidAmount = "eB";

        public const string EPAskAmount = "eA";

        public const string Capital = "CP";

        public const string PublicOfferingRatio = "Po";

        public const string Indices = "In";

        public const string NetCapital = "Nc";

        public const string NetProfit = "Np";

        public const string LastPeriod = "Lp";

        public const string Nominal = "No";

        public const string ISIN = "Is";

        public const string NetDividend = "Nd";

        public const string FreeFloatRate = "Ffr";

        public const string LastPeriodProfit = "Lpp";

        public const string IndexWeight = "Iw";

        public const string IndexFFR = "If";

        public const string Issuer = "Ir";

        public const string GrossSettlement = "Gs";

        public const string MarketValue = "mv";

        public const string PriceEarning = "pE";

        public const string PublicStockSize = "Pss";

        public const string FreeFloatStockSize = "Fss";

        public const string FreeFloatMarketValue = "Fm";

        public const string EarnigPerShare = "Es";

        public const string DividendYield = "Dy";

        public const string PriceBookValue = "Pb";

        public const string BookValue = "Bv";

        public const string Plasman = "Pl";

        public const string PayoutRatio = "yR";

        public const string Spread = "sP";

        public const string CouponRate = "cR";

        public const string PricingSource = "PS";

        public const string PricingSourceName = "PSn";

        public const string BestBidTime = "BB";

        public const string BestAskTime = "BA";

        public const string Delta = "DE";

        public const string Theta = "TH";

        public const string Gamma = "GA";

        public const string Vega = "VE";

        public const string Rho = "RH";

        public const string Barrier = "Ba";

        public const string BSPrice = "BS";

        public const string BSDiff = "Bd";

        public const string BSChangePercent = "BC";

        public const string SettlementChange = "SC";

        public const string SettlementChangePercent = "SP";

        public const string XU030Weight = "x3";

        public const string XU050Weight = "x5";

        public const string XU100Weight = "x1";

        public const string XUTUMWeight = "xT";

        public const string TheoPrice21 = "T1";

        public const string TheoPrice42 = "T2";

        public const string TheoPrice63 = "T3";

        public const string TheoPrice126 = "T4";

        public const string TheoPrice252 = "T5";

        public const string TheoPrice = "TP";

        public const string TheoPriceChange = "CT";

        public const string TheoPriceChangePercent = "Cp";

        public const string HisVol21 = "H1";

        public const string HisVol42 = "H2";

        public const string HisVol63 = "H3";

        public const string HisVol126 = "H4";

        public const string HisVol252 = "H5";

        public const string HisVol30 = "H6";

        public const string TotalCapital = "TC";

        public const string LiquidAssets = "LA";

        public const string Portfolio = "PO";

        public const string EMM = "EM";

        public const string Asset = "aS";

        public const string NumOfShares = "NS";

        public const string OutstandingShares = "OS";

        public const string PartnerCount = "PU";

        public const string BillsPercentage = "LP";

        public const string BondsPercentage = "BP";

        public const string ReverseRepoPercentage = "RP";

        public const string EquityPercentage = "EQ";

        public const string PrivateDebtsPercentage = "PP";

        public const string ForeignSecPercentage = "FP";

        public const string OtherPercentage = "OP";

        public const string NPDebtPercentage = "NP";

        public const string EMMPercentage = "EP";

        public const string Day91 = "D1";

        public const string Day182 = "D8";

        public const string Day273 = "D2";

        public const string Day365 = "D3";

        public const string Day456 = "D4";

        public const string DayGen = "DG";

        public const string OverNightEqual = "OE";

        public const string FundType = "FT";

        public const string SimpleLeverage = "SL";

        public const string ActiveLeverage = "Al";

        public const string SpotPrice = "SO";

        public const string SpotPriceChange = "SR";

        public const string SpotPriceChangePercent = "SE";

        public const string HighChangePercent = "HC";

        public const string LowChangePercent = "LC";

        public const string OpenChangePercent = "OC";

        public const string Fluctuation = "FL";

        public const string VWAPChange = "VC";

        public const string VWAPChangePercent = "VP";

        public const string DiffTotalTurnover = "Dt";

        public const string DiffTotalTurnoverPercent = "Di";

        public const string WarrantRate = "WR";

        public const string OptionStatus = "OT";

        public const string Name = "Nm";

        public const string Description = "De";

        public const string Ticker = "Ti";

        public const string MarketSector = "MS";

        public const string SecurityType = "SY";

        public const string Currency = "cu";

        public const string Exchange = "ex";

        public const string Precision = "pr";

        public const string ExerciseType = "EX";

        public const string BidAmount = "bv";

        public const string AskAmount = "av";

        public const string DailyDirection = "dd";

        public const string WOWeeklyChange = "WC";

        public const string WOWeeklyChangePercent = "WP";

        public const string MOMonthlyChange = "MC";

        public const string MOMonthlyChangePercent = "MP";

        public const string YOYearlyChange = "YC";

        public const string YOYearlyChangePercent = "YP";

        public const string WTDVolume = "wV";

        public const string WTDTurnover = "wT";

        public const string MTDVolume = "mV";

        public const string MTDTurnover = "mT";

        public const string YTDVolume = "yV";

        public const string YTDTurnover = "yT";

        public const string WOWVolume = "vV";

        public const string WOWTurnover = "vT";

        public const string MOMVolume = "nV";

        public const string MOMTurnover = "nT";

        public const string YOYVolume = "zV";

        public const string YOYTurnover = "zT";

        public const string WeeklyAverageVolume = "WA";

        public const string WeeklyAverageTurnover = "WT";

        public const string WeeklyAverageVolume2 = "WB";

        public const string WeeklyAverageTurnover2 = "WU";

        public const string MonthlyAverageVolume = "MA";

        public const string MonthlyAverageTurnover = "MT";

        public const string MonthlyAverageVolume2 = "MB";

        public const string MonthlyAverageTurnover2 = "MU";

        public const string YearlyAverageVolume = "YA";

        public const string YearlyAverageTurnover = "YT";

        public const string YearlyAverageVolume2 = "YB";

        public const string YearlyAverageTurnover2 = "YU";

        public const string WaitingBidAmount0 = "V0";

        public const string WaitingBidAmount1 = "V1";

        public const string WaitingBidAmount2 = "V2";

        public const string WaitingBidAmount3 = "V3";

        public const string WaitingBidAmount4 = "V4";

        public const string WaitingBidAmount5 = "V5";

        public const string WaitingBidAmount6 = "V6";

        public const string WaitingBidAmount7 = "V7";

        public const string WaitingBidAmount8 = "V8";

        public const string WaitingBidAmount9 = "V9";

        public const string WaitingAskAmount0 = "W0";

        public const string WaitingAskAmount1 = "W1";

        public const string WaitingAskAmount2 = "W2";

        public const string WaitingAskAmount3 = "W3";

        public const string WaitingAskAmount4 = "W4";

        public const string WaitingAskAmount5 = "W5";

        public const string WaitingAskAmount6 = "W6";

        public const string WaitingAskAmount7 = "W7";

        public const string WaitingAskAmount8 = "W8";

        public const string WaitingAskAmount9 = "W9";

        public const string WaitingBidTurnover0 = "y0";

        public const string WaitingBidTurnover1 = "y1";

        public const string WaitingBidTurnover2 = "y2";

        public const string WaitingBidTurnover3 = "y3";

        public const string WaitingBidTurnover4 = "y4";

        public const string WaitingBidTurnover5 = "y5";

        public const string WaitingBidTurnover6 = "y6";

        public const string WaitingBidTurnover7 = "y7";

        public const string WaitingBidTurnover8 = "y8";

        public const string WaitingBidTurnover9 = "y9";

        public const string WaitingAskTurnover0 = "Y0";

        public const string WaitingAskTurnover1 = "Y1";

        public const string WaitingAskTurnover2 = "Y2";

        public const string WaitingAskTurnover3 = "Y3";

        public const string WaitingAskTurnover4 = "Y4";

        public const string WaitingAskTurnover5 = "Y5";

        public const string WaitingAskTurnover6 = "Y6";

        public const string WaitingAskTurnover7 = "Y7";

        public const string WaitingAskTurnover8 = "Y8";

        public const string WaitingAskTurnover9 = "Y9";

        public const string MarketValueUSD = "mu";

        public const string MarketValueEUR = "me";

        public const string LevelChange = "lc";

        public const string SessionStartTime = "SS";

        public const string SessionEndTime = "SN";

        public const string SessionDate = "SD";

        public const string PreviousSessionDate = "pd";

        public const string OpenFlag = "OF";

        public const string RiskLevel = "rl";

        public const string GMT = "GM";

        public const string Maturity = "Mt";

        public const string SMA5 = "s1";

        public const string SMA10 = "s2";

        public const string SMA20 = "s3";

        public const string SMA50 = "s4";

        public const string SMA100 = "s5";

        public const string SMA200 = "s6";

        public const string EMA5 = "e1";

        public const string EMA10 = "e2";

        public const string EMA20 = "e3";

        public const string EMA50 = "e4";

        public const string EMA100 = "e5";

        public const string EMA200 = "e6";

        public const string RSI14 = "r1";

        public const string MACD122609 = "m1";

        public const string BOLUP2002 = "bu";

        public const string BOLDOWN2002 = "bd";

        public const string ADX14 = "ad";

        public const string DABTC = "u0";

        public const string DASTC = "u1";

        public const string DABTQ = "u2";

        public const string DASTQ = "u3";

        public const string DABSCR = "u4";

        public const string DABSQR = "u5";

        public const string DATBSCR = "u6";

        public const string DATBSQR = "u7";

        public const string DAWT = "u8";

        public const string DAWAT = "u9";

        public const string DAWBT = "U0";

        public const string DAWST = "U1";

        public const string DAOC = "U2";

        public const string DATOC = "U3";

        public const string DAOQ = "U4";

        public const string DATOQ = "U5";

        public const string DABOC = "U6";

        public const string DASOC = "U7";

        public const string DABOQ = "U8";

        public const string DASOQ = "U9";

        public const string DAFAKC = "0u";

        public const string DAABQ = "1u";

        public const string DAASQ = "2u";

        public const string DAVBQ = "3u";

        public const string DAVSQ = "4u";

        public const string DACXC = "5u";

        public const string DACXQ = "6u";

        public const string DACXBC = "7u";

        public const string DACXSC = "8u";

        public const string DACXBQ = "9u";

        public const string DACXSQ = "0U";

        public const string DATCXC = "1U";

        public const string DAWCX = "2U";

        public const string DAWCXB = "3U";

        public const string DAWCXS = "4U";

        public const string DACXCR = "5U";

        public const string DACXQR = "6U";

        public const string DATCXCR = "7U";

        public const string DATCXQR = "8U";

        public const string M1 = "M1";

        public const string M3 = "M3";

        public const string M6 = "M6";

        public const string M12 = "M12";

        public const string OptionPremiumTurnover = "Op";

        public const string Locale = "lo";

        public const string Tag = "tg";

        public const string NewsId = "ni";

        public const string Content = "ct";

        public const string Header = "hd";

        public const string Summary = "su";

        public const string TopFiveBrokerBidTradeVolume = "Fb";

        public const string TopFiveBrokerAskTradeVolume = "Fa";

        public const string TopFiveBrokerNetTradeVolume = "Fn";

        public const string Snapshot = "_s";

        public const string RealTradeNumber = "rn";

        public const string TradeId = "ti";

        public const string FixingSettlementPrice = "Fp";

        public const string PreviousSettlementPriceClose = "PC";

        public const string State = "Sa";

        public const string TradeOperation = "TO";

        public const string Alpha200 = "ah";

        public const string Beta200 = "be";

        public const string EquilibriumChangePercent = "eq";

        public const string DiffTotalVolume = "DV";

        public const string AverageRate = "aR";

        public const string DateTime = "dt";

        public const string EstimatedBidPrice = "ebp";

        public const string EstimatedAskPrice = "eap";

        public const string BidRate0 = "o0";

        public const string BidRate1 = "o1";

        public const string BidRate2 = "o2";

        public const string BidRate3 = "o3";

        public const string BidRate4 = "o4";

        public const string BidRate5 = "o5";

        public const string BidRate6 = "o6";

        public const string BidRate7 = "o7";

        public const string BidRate8 = "o8";

        public const string BidRate9 = "o9";

        public const string AskRate0 = "O0";

        public const string AskRate1 = "O1";

        public const string AskRate2 = "O2";

        public const string AskRate3 = "O3";

        public const string AskRate4 = "O4";

        public const string AskRate5 = "O5";

        public const string AskRate6 = "O6";

        public const string AskRate7 = "O7";

        public const string AskRate8 = "O8";

        public const string AskRate9 = "O9";

        public const string NominalOptionPremiumTurnover = "NO";

        public const string TimeMs = "x";

        public const string MMQuoteTime = "q";

        public const string MMQuoteType = "Qt";

        public const string DepthTime = "p";

        public const string BidQty = "qy";

        public const string AskQty = "ay";

        public const string Rate = "r";

        public const string OpenRate = "or";

        public const string MMQuoteBroker = "Mb";

        public const string MBOOrderKey = "Mk";

        public const string MBOBuyOrSell = "Mo";

        public const string MBOOrderClass = "Ms";

        public const string MBOPrice = "Me";

        public const string MBOVolume = "Mu";

        public const string AdditionalReferencePrice = "Rp";

        public const string AccumulatedTradeReportVolume = "rv";

        public const string TotalTurnoverCurrency = "tc";

        public const string AccumulatedTradeReportTurnover = "rt";

        public const string AccumulatedTradeReportTurnoverCurrency = "Rt";

        public const string TurnoverCurrency = "Tc";

        public const string TopFiveBrokerBidTradeTurnover = "Ub";

        public const string TopFiveBrokerAskTradeTurnover = "Ua";

        public const string TopFiveBrokerNetTradeTurnover = "Un";

        public const string TopTenBrokerBidTradeVolume = "fb";

        public const string TopTenBrokerAskTradeVolume = "fa";

        public const string TopTenBrokerNetTradeVolume = "fn";

        public const string TopTenBrokerBidTradeTurnover = "ub";

        public const string TopTenBrokerAskTradeTurnover = "ua";

        public const string TopTenBrokerNetTradeTurnover = "un";

        public const string VWAPRate = "wr";

        public const string VWAPSettlement = "AS";

        public const string VWAPCompoundRate = "Wr";

        public const string Attachments = "at";

        public const string BidCollateralPrice0 = "B0";

        public const string BidCollateralPrice1 = "B1";

        public const string BidCollateralPrice2 = "B2";

        public const string BidCollateralPrice3 = "B3";

        public const string BidCollateralPrice4 = "B4";

        public const string BidCollateralPrice5 = "B5";

        public const string BidCollateralPrice6 = "B6";

        public const string BidCollateralPrice7 = "B7";

        public const string BidCollateralPrice8 = "B8";

        public const string BidCollateralPrice9 = "B9";

        public const string AskCollateralPrice0 = "A0";

        public const string AskCollateralPrice1 = "A1";

        public const string AskCollateralPrice2 = "A2";

        public const string AskCollateralPrice3 = "A3";

        public const string AskCollateralPrice4 = "A4";

        public const string AskCollateralPrice5 = "A5";

        public const string AskCollateralPrice6 = "A6";

        public const string AskCollateralPrice7 = "A7";

        public const string AskCollateralPrice8 = "A8";

        public const string AskCollateralPrice9 = "A9";

        public const string CollateralPrice = "cp";

        public const string TopBidTradeBroker = "Tb";

        public const string TopAskTradeBroker = "Ta";

        public const string TopTotalTradeBroker = "Tt";

        public const string DailyRateChange = "cr";

        public const string Pivot = "PV";

        public const string Support1 = "S1";

        public const string Support2 = "S2";

        public const string Resistance1 = "R1";

        public const string Resistance2 = "R2";

        public const string EquilibriumTurnover = "eQ";

        public const string OpenCompoundRate = "Oc";

        public const string HighCompoundRate = "Hc";

        public const string LowCompoundRate = "Lc";

        public const string SequenceId = "Sq";

        public const string MBOTime = "mt";

        public const string MBOUpdateCode = "mU";

        public const string PublicLeaseCertPercentage = "Plp";

        public const string PrivateLeaseCertPercentage = "plp";

        public const string PreciousMetalsPercentage = "pmp";

        public const string BankBondsPercentage = "bbp";

        public const string ForeignBillsOfExcPercentage = "fbp";

        public const string ForeignDepthsOfExcPercentage = "fdp";

        public const string EurobondsPercentage = "ep";

        public const string CommercialPapersPercentage = "cpp";

        public const string FundCertPercentage = "fcp";

        public const string RealEstateCertPercentage = "rcp";

        public const string ParticipationAccPercentage = "pap";

        public const string DerivativesPercentage = "dp";

        public const string AssetBackedSecPercentage = "asp";

        public const string TermDepositPercentage = "tdp";

        public const string ForeignDebtsPercentage = "fDp";

        public const string ForeignEquityPercentage = "fep";

        public const string RepoPercentage = "rp";

        public const string CrossTrade = "Ct";

        public const string TradableStatus = "Ts";

        public const string Sensitivity = "Ss";

        public const string FGoldD = "f1";

        public const string FGold60m = "f2";

        public const string FGold15m = "f3";

        public const string FGold5m = "f4";

        public const string LegacyCode = "LE";

        public const string RankedBidPrice0 = "Bf0";

        public const string RankedBidPrice1 = "Bf1";

        public const string RankedBidPrice2 = "Bf2";

        public const string RankedBidPrice3 = "Bf3";

        public const string RankedBidPrice4 = "Bf4";

        public const string RankedBidPrice5 = "Bf5";

        public const string RankedBidPrice6 = "Bf6";

        public const string RankedBidPrice7 = "Bf7";

        public const string RankedBidPrice8 = "Bf8";

        public const string RankedBidPrice9 = "Bf9";

        public const string RankedBidAmount0 = "Bh0";

        public const string RankedBidAmount1 = "Bh1";

        public const string RankedBidAmount2 = "Bh2";

        public const string RankedBidAmount3 = "Bh3";

        public const string RankedBidAmount4 = "Bh4";

        public const string RankedBidAmount5 = "Bh5";

        public const string RankedBidAmount6 = "Bh6";

        public const string RankedBidAmount7 = "Bh7";

        public const string RankedBidAmount8 = "Bh8";

        public const string RankedBidAmount9 = "Bh9";

        public const string RankedBidOrderKey0 = "Bo0";

        public const string RankedBidOrderKey1 = "Bo1";

        public const string RankedBidOrderKey2 = "Bo2";

        public const string RankedBidOrderKey3 = "Bo3";

        public const string RankedBidOrderKey4 = "Bo4";

        public const string RankedBidOrderKey5 = "Bo5";

        public const string RankedBidOrderKey6 = "Bo6";

        public const string RankedBidOrderKey7 = "Bo7";

        public const string RankedBidOrderKey8 = "Bo8";

        public const string RankedBidOrderKey9 = "Bo9";

        public const string RankedAskPrice0 = "Af0";

        public const string RankedAskPrice1 = "Af1";

        public const string RankedAskPrice2 = "Af2";

        public const string RankedAskPrice3 = "Af3";

        public const string RankedAskPrice4 = "Af4";

        public const string RankedAskPrice5 = "Af5";

        public const string RankedAskPrice6 = "Af6";

        public const string RankedAskPrice7 = "Af7";

        public const string RankedAskPrice8 = "Af8";

        public const string RankedAskPrice9 = "Af9";

        public const string RankedAskAmount0 = "Ah0";

        public const string RankedAskAmount1 = "Ah1";

        public const string RankedAskAmount2 = "Ah2";

        public const string RankedAskAmount3 = "Ah3";

        public const string RankedAskAmount4 = "Ah4";

        public const string RankedAskAmount5 = "Ah5";

        public const string RankedAskAmount6 = "Ah6";

        public const string RankedAskAmount7 = "Ah7";

        public const string RankedAskAmount8 = "Ah8";

        public const string RankedAskAmount9 = "Ah9";

        public const string RankedAskOrderKey0 = "Ao0";

        public const string RankedAskOrderKey1 = "Ao1";

        public const string RankedAskOrderKey2 = "Ao2";

        public const string RankedAskOrderKey3 = "Ao3";

        public const string RankedAskOrderKey4 = "Ao4";

        public const string RankedAskOrderKey5 = "Ao5";

        public const string RankedAskOrderKey6 = "Ao6";

        public const string RankedAskOrderKey7 = "Ao7";

        public const string RankedAskOrderKey8 = "Ao8";

        public const string RankedAskOrderKey9 = "Ao9";

        public const string MMQuoteUpdateCode = "MD";

        public const string ClearingTradeInfo = "cT";

        public const string LiveStatus = "lv";

        public const string TopFiveBrokerBidTradeVolume5m = "ga";

        public const string TopFiveBrokerAskTradeVolume5m = "gb";

        public const string TopFiveBrokerNetTradeVolume5m = "gc";

        public const string TopFiveBrokerBidTradeTurnover5m = "gd";

        public const string TopFiveBrokerAskTradeTurnover5m = "ge";

        public const string TopFiveBrokerNetTradeTurnover5m = "gf";

        public const string TopFiveBrokerBidTradeVolume10m = "gg";

        public const string TopFiveBrokerAskTradeVolume10m = "gh";

        public const string TopFiveBrokerNetTradeVolume10m = "gi";

        public const string TopFiveBrokerBidTradeTurnover10m = "gj";

        public const string TopFiveBrokerAskTradeTurnover10m = "gk";

        public const string TopFiveBrokerNetTradeTurnover10m = "gl";

        public const string AuctionPrice = "aP";

        public const string AuctionVolume = "aV";

        public const string CapitalRatio = "CR";

        public const string ShortDebtOnTotalAssets = "sa";

        public const string ShortDebtOnTotalDebt = "st";

        public const string ReturnOnEquity = "re";

        public const string SessionHigh = "H";

        public const string SessionLow = "w";

        public const string SessionOpen = "o";

        public const string SessionChange = "g";

        public const string SessionChangePercent = "G";

        public const string SessionTotalVolume = "sv";

        public const string SessionTotalTurnover = "St";

        public const string SessionTotalTurnoverCurrency = "Sc";

        public const string SessionVWAP = "Wa";

        public const string SessionContracts = "cS";

        public const string FinalEvaluation = "fe";

        public const string BestBid = "bB";

        public const string BestAsk = "bA";

        public const string BidTradeCount = "Bc";

        public const string AskTradeCount = "Ac";

        public const string UpTickCount = "uc";

        public const string UpTickVolume = "uv";

        public const string UpTickTurnover = "ut";

        public const string DownTickCount = "dc";

        public const string DownTickVolume = "dv";

        public const string DownTickTurnover = "dT";

        public const string SMA8 = "s7";

        public const string EMA8 = "e7";

        public const string PublicDomesticDebtPercentage = "KIBD";

        public const string PublicLeaseFCPercentage = "KKSD";

        public const string RealEstateInvestmentFundsPercentage = "GYKB";

        public const string PublicForeignLeasePercentage = "KKSYD";

        public const string DepositFCPercentage = "VMD";

        public const string ParticipationAccountFCPercentage = "KHD";

        public const string DepositTLPercentage = "VMTL";

        public const string PSForeignLeasePercentage = "OKSYD";

        public const string ForeignPublicDebtPercentage = "YBKB";

        public const string PrivateExternalBorrowingPercentage = "OSDB";

        public const string TradedFundsParticipationPercentage = "BYF";

        public const string PublicDebtPreciousMetalsPercentage = "KMKBA";

        public const string ForeignTradedFundsPercentage = "YBYF";

        public const string ParticipationAccountAUPercentage = "KHAU";

        public const string ForeignPrivateSectorDebtPercentage = "YBOSB";

        public const string MutualFundsParticipationPercentage = "YYF";

        public const string DepositAUPercentage = "VMAU";

        public const string ParticipationAccountTLPercentage = "KHTL";

        public const string FuturesCashCollateralsPercentage = "VINT";

        public const string PreciousMetalsETFPercentage = "KMBYF";

        public const string PrivateEquityInvestmentFundsPercentage = "GSYKB";

        public const string PublicLeaseTLPercentage = "KKSTL";

        public const string PublicLeasePreciousMetalsPercentage = "KMKKS";

        public const string BidPrice10 = "b10";

        public const string BidQty20 = "q20";

        public const string BidQty24 = "q24";

        public const string AskPrice13 = "a13";

        public const string AskPrice17 = "a17";

        public const string BidQty11 = "q11";

        public const string BidQty15 = "q15";

        public const string AskPrice10 = "a10";

        public const string AskPrice14 = "a14";

        public const string AskAmount10 = "w10";

        public const string BidPrice13 = "b13";

        public const string AskPrice21 = "a21";

        public const string BidPrice21 = "b21";

        public const string BidAmount15 = "v15";

        public const string BidAmount16 = "v16";

        public const string AskPrice23 = "a23";

        public const string AskAmount12 = "w12";

        public const string AskAmount13 = "w13";

        public const string AskAmount18 = "w18";

        public const string AskQty17 = "k17";

        public const string AskQty23 = "k23";

        public const string BidPrice12 = "b12";

        public const string BidQty22 = "q22";

        public const string BidAmount18 = "v18";

        public const string BidQty23 = "q23";

        public const string AskQty21 = "k21";

        public const string AskPrice20 = "a20";

        public const string AskAmount20 = "w20";

        public const string BidPrice18 = "b18";

        public const string BidPrice11 = "b11";

        public const string BidPrice19 = "b19";

        public const string AskPrice12 = "a12";

        public const string AskPrice15 = "a15";

        public const string AskAmount24 = "w24";

        public const string AskQty11 = "k11";

        public const string AskQty19 = "k19";

        public const string BidAmount10 = "v10";

        public const string BidQty10 = "q10";

        public const string AskAmount16 = "w16";

        public const string AskAmount22 = "w22";

        public const string AskQty18 = "k18";

        public const string BidAmount19 = "v19";

        public const string BidPrice20 = "b20";

        public const string BidAmount21 = "v21";

        public const string BidQty14 = "q14";

        public const string AskPrice16 = "a16";

        public const string AskQty13 = "k13";

        public const string AskQty16 = "k16";

        public const string BidAmount14 = "v14";

        public const string BidQty19 = "q19";

        public const string BidQty21 = "q21";

        public const string AskAmount11 = "w11";

        public const string AskAmount17 = "w17";

        public const string AskQty14 = "k14";

        public const string BidAmount13 = "v13";

        public const string BidPrice24 = "b24";

        public const string BidQty18 = "q18";

        public const string AskPrice22 = "a22";

        public const string AskQty20 = "k20";

        public const string BidAmount11 = "v11";

        public const string BidAmount12 = "v12";

        public const string BidAmount22 = "v22";

        public const string BidQty13 = "q13";

        public const string BidQty16 = "q16";

        public const string AskAmount14 = "w14";

        public const string AskAmount23 = "w23";

        public const string AskQty10 = "k10";

        public const string AskQty22 = "k22";

        public const string BidPrice16 = "b16";

        public const string BidPrice17 = "b17";

        public const string BidPrice22 = "b22";

        public const string BidPrice23 = "b23";

        public const string AskAmount19 = "w19";

        public const string AskQty12 = "k12";

        public const string BidPrice14 = "b14";

        public const string BidPrice15 = "b15";

        public const string BidQty17 = "q17";

        public const string AskPrice11 = "a11";

        public const string AskPrice24 = "a24";

        public const string AskAmount15 = "w15";

        public const string BidAmount24 = "v24";

        public const string AskPrice18 = "a18";

        public const string AskPrice19 = "a19";

        public const string BidAmount17 = "v17";

        public const string BidAmount20 = "v20";

        public const string BidAmount23 = "v23";

        public const string BidQty12 = "q12";

        public const string AskAmount21 = "w21";

        public const string AskQty15 = "k15";

        public const string AskQty24 = "k24";

        public const string NewsSourceRef = "nr";

        public const string SplitRatio = "sra";

        public const string LastInCurrency = "lC";

        public const string TotalTradeCount = "tC";

        public const string NewsBehalfFundTitle = "Bt";

        public const string NewsBehalfFundId = "Bf";

        public const string SplitRatioDate = "sd";

        public const string RealEstateInvestments = "GYY";

        public const string VentureCapitalInvestments = "GSYY";
    }
    public class ResponseArgs : EventArgs
    {
        public bool Success { get; set; }

        public short Code { get; set; }

        public object Result { get; set; }

        public string RawString { get; set; }

        public byte[] Bytes { get; set; }

        public ResponseArgs()
        {
            Success = false;
            Code = -1;
            Result = null;
            RawString = "";
            Bytes = new byte[0];
        }

        public override string ToString()
        {
            return " success: " + Success + "\n code: " + Code + "\n rawString: " + RawString + "\n result: " + Result?.ToString() + "\n";
        }
    }
    public interface IForeksSocketClient
    {

        event EventHandler<ResponseArgs> OnConnected;
        event Func<object,ResponseArgs,Task> OnLogin;
        event Func<object,ResponseArgs,Task> OnData;
        event EventHandler<ErrorEventArgs> OnError;
        event EventHandler<ResponseArgs> OnDisconnected;
        event EventHandler<ResponseArgs> OnSubscriptionConfirmed;

        bool IsConnected { get; }
        bool IsLogin { get; }

        Task ConnectAsync(string socketAddress);

        Task LoginAsync(string username, string password);
        Task SendDataAsync(object data);
        Task Subscribe(Dictionary<string, string> symbols, List<string> fields);
        Task Listen();
        Task UnSubscribe(List<string> symbols);

        Task DisconnectAsync();

    }
}
