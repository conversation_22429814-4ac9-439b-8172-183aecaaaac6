﻿using Microsoft.IdentityModel.Tokens;
using System.Security.Cryptography.X509Certificates;

namespace Bigpara.Service.Api.Extensions
{
    public static class AuthenticationExtension
    {
        public static IServiceCollection AddVoltran(this IServiceCollection services, IWebHostEnvironment webHostEnvironment,IConfiguration configuration)
        {
            var keyFilePath = Path.Combine(@"./Certs", $"idsrv4.pfx");
            var keyFilePassword = "12345";

            var crt = new X509Certificate2(keyFilePath, keyFilePassword);
            var key = new X509SecurityKey(crt);

            services.AddAuthentication("Bearer").AddJwtBearer("Bearer", options =>
             {
                 options.Authority = configuration["Auth:Voltran:Url"];
                 options.RequireHttpsMetadata = false;
                 options.TokenValidationParameters = new TokenValidationParameters
                 {
                     ValidateAudience = false,
                     ValidateIssuerSigningKey = true,
                     ValidIssuer = configuration["Auth:Voltran:Issuer"],
                     IssuerSigningKey = new X509SecurityKey(crt),
                     IssuerSigningKeyResolver = (string token, SecurityToken securityToken, string kid,
                     TokenValidationParameters validationParameters) => new List<X509SecurityKey> { new X509SecurityKey(crt) },
                 };
               
                 options.TokenValidationParameters.ValidTypes = new[] { "at+jwt" };
                 options.ForwardDefaultSelector = ForwardReferenceToken("Introspection");

             })
             .AddOAuth2Introspection("Introspection", options =>
             {
                 options.Authority = configuration["Auth:Voltran:Url"];
                 options.ClientId = "api1";
                 options.ClientSecret = "api1";
             });

             return services;
        }

        private static Func<HttpContext, string> ForwardReferenceToken(string introspectionScheme)
        {
            return Select;
            string Select(HttpContext context)
            {
                var (text, text2) = GetSchemeAndCredential(context);
                if (text.Equals("Bearer", StringComparison.OrdinalIgnoreCase) && !text2.Contains("."))
                {
                    return introspectionScheme;
                }

                return null;
            }
        }

        private static (string, string) GetSchemeAndCredential(HttpContext context)
        {
            string text = context.Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(text))
            {
                return ("", "");
            }

            string[] array = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (array.Length != 2)
            {
                return ("", "");
            }

            return (array[0], array[1]);
        }
    }
}
