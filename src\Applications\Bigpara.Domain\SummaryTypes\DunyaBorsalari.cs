﻿namespace Bigpara.Domain.SummaryTypes;

public partial class DunyaBorsalari
{
    public int SEMBOLID { get; set; }
    public string SEMBOL { get; set; }
    public Nullable<System.DateTime> TARIH { get; set; }
    public Nullable<double> ALIS { get; set; }
    public Nullable<double> SATIS { get; set; }
    public Nullable<double> ACILIS { get; set; }
    public Nullable<double> YUKSEK { get; set; }
    public Nullable<double> YUKSEK1 { get; set; }
    public Nullable<double> YUKSEK2 { get; set; }
    public Nullable<double> DUSUK { get; set; }
    public Nullable<double> DUSUK1 { get; set; }
    public Nullable<double> DUSUK2 { get; set; }
    public Nullable<double> <PERSON><PERSON><PERSON>IS { get; set; }
    public Nullable<double> KAPANIS1 { get; set; }
    public Nullable<double> KAPANIS2 { get; set; }
    public Nullable<decimal> HACIMLOT { get; set; }
    public Nullable<decimal> HACIMLOT1 { get; set; }
    public Nullable<decimal> HACIMLOT2 { get; set; }
    public Nullable<double> AORT { get; set; }
    public Nullable<double> AORT1 { get; set; }
    public Nullable<double> AORT2 { get; set; }
    public Nullable<decimal> HACIMTL { get; set; }
    public Nullable<decimal> HACIMTL1 { get; set; }
    public Nullable<decimal> HACIMTL2 { get; set; }
    public Nullable<double> DUNKUKAPANIS { get; set; }
    public Nullable<double> ONCEKIKAPANIS { get; set; }
    public Nullable<double> IZAFIKAPANIS { get; set; }
    public Nullable<double> TAVAN { get; set; }
    public Nullable<double> TABAN { get; set; }
    public Nullable<double> YILYUKSEK { get; set; }
    public Nullable<double> YILDUSUK { get; set; }
    public Nullable<double> AYYUKSEK { get; set; }
    public Nullable<double> AYDUSUK { get; set; }
    public Nullable<double> HAFTAYUKSEK { get; set; }
    public Nullable<double> HAFTADUSUK { get; set; }
    public Nullable<double> ONCEKIYILKAPANIS { get; set; }
    public Nullable<double> ONCEKIAYKAPANIS { get; set; }
    public Nullable<double> ONCEKIHAFTAKAPANIS { get; set; }
    public Nullable<double> YILORTALAMA { get; set; }
    public Nullable<double> AYORTALAMA { get; set; }
    public Nullable<double> HAFTAORTALAMA { get; set; }
    public Nullable<double> FIYATADIMI { get; set; }
    public Nullable<double> LOTADET { get; set; }
    public Nullable<double> KAYKAR { get; set; }
    public Nullable<double> SERMAYE { get; set; }
    public Nullable<double> SAKLAMAOR { get; set; }
    public Nullable<double> NETKAR { get; set; }
    public string DONEM { get; set; }
    public Nullable<double> FIYATKAZ { get; set; }
    public Nullable<double> PIYDEG { get; set; }
    public Nullable<double> SENETSAY { get; set; }
    public Nullable<double> WAVRMTX { get; set; }
    public Nullable<double> WAVRS1MTX { get; set; }
    public Nullable<double> WAVRDAYMTX { get; set; }
    public Nullable<double> XU030AG { get; set; }
    public Nullable<double> XU050AG { get; set; }
    public Nullable<double> XU100AG { get; set; }
    public Nullable<double> XUTUMAG { get; set; }
    public Nullable<double> AMORTISMAN { get; set; }
    public Nullable<double> OZSERMAYE { get; set; }
    public Nullable<double> TEMETTUVERIMLILIGI { get; set; }
    public Nullable<double> NAKITNETTEMETTU { get; set; }
    public Nullable<double> TEORIKFIYAT { get; set; }
    public Nullable<double> BETA { get; set; }
    public Nullable<double> VOB_UZL { get; set; }
    public Nullable<double> VOB_ONCUZL { get; set; }
    public Nullable<double> VOB_SAYI { get; set; }
    public Nullable<double> VOB_OPINTDEG { get; set; }
    public Nullable<double> VOB_UZLDEG { get; set; }
    public Nullable<double> EK1 { get; set; }
    public Nullable<double> EK2 { get; set; }
    public Nullable<double> EK3 { get; set; }
    public Nullable<double> EK4 { get; set; }
    public Nullable<double> EK5 { get; set; }
    public string ACIKLAMA { get; set; }
    public byte PIYASAID { get; set; }
    public string ENDEKS { get; set; }
    public string IMKBPAZARKOD { get; set; }
    public string IMKBHISSETIP { get; set; }
    public Nullable<byte> SEKTORID { get; set; }
    public bool SEANSVAR { get; set; }
    public bool DERINLIKVAR { get; set; }
    public byte ONDALIKBASAMAK { get; set; }
    public bool AKTIF { get; set; }
    public string HISSEGRUBU { get; set; }
    public string ISLEMTURU { get; set; }
    public string BRUTTAKAS { get; set; }
    public string SACIKLAMA { get; set; }
    public Nullable<double> FARK { get; set; }
    public Nullable<decimal> YUZDEDEGISIM { get; set; }
    public string ULKE { get; set; }
    public int SIRA1 { get; set; }
    public string SIRA2 { get; set; }
}
