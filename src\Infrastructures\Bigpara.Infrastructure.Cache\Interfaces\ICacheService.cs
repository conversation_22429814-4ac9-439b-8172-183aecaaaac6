﻿namespace Bigpara.Cache.Interfaces
{
    public interface ICacheService
    {
        /// <summary>
        /// Default cache timeout in minutes
        /// </summary>
        int CacheDuration { get; }

        /// <summary>
        /// Get item from cache or load with fallback if not exists (sync)
        /// </summary>
        T Get<T>(string key, int cacheTime, Func<T> acquire);

        /// <summary>
        /// Get item from cache or load with fallback if not exists (async)
        /// </summary>
        Task<T> GetAsync<T>(string key, int cacheTime, Func<Task<T>> acquire);

        /// <summary>
        /// Get item by key, returns default(T) if not found
        /// </summary>
        T Get<T>(string key);

        /// <summary>
        /// Try get item by key, returns success status
        /// </summary>
        T Get<T>(string key, out bool isSucceeded);

        /// <summary>
        /// Get multiple items by list of keys
        /// </summary>
        IDictionary<string, T> Get<T>(IList<string> keys);

        /// <summary>
        /// Insert item with default cache duration
        /// </summary>
        void Set<T>(string key, T value);

        /// <summary>
        /// Insert item with specific cache duration in seconds
        /// </summary>
        void Set<T>(string key, T value, int cacheTime);

        /// <summary>
        /// Insert item with specific timeout
        /// </summary>
        void Set<T>(string key, T value, TimeSpan timeout);

        /// <summary>
        /// Check if item exists in cache
        /// </summary>
        bool IsSet(string key);

        /// <summary>
        /// Remove item by key
        /// </summary>
        void Remove(string key);

        /// <summary>
        /// Remove items by pattern (if supported)
        /// </summary>
        void RemoveByPattern(string pattern);

        /// <summary>
        /// Clear all cache
        /// </summary>
        void Clear();

        /// <summary>
        /// Update an existing item in a list or collection by key
        /// </summary>
        void Update<T>(string listKey, T value);
    }
}
