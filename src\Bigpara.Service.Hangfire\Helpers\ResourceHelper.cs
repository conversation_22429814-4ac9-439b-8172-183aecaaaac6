﻿namespace Bigpara.Service.Hangfire.Helpers;

public static class ResourceHelper
{
    public static string HeaderFormatString(List<string>? sembol, string sembolTitle, string header)
    {
        return sembol == null ? $"{sembolTitle} ({header})" : $"***{string.Join("* *", sembol)}*** {sembolTitle} ({header})";
    }

    public static string GetSembolString(List<string>? sembol)
    {
        if (sembol == null)
        {
            return "";
        }
        return string.Join(",", sembol);
    }
}
