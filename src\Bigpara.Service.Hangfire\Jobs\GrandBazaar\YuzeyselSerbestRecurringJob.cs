﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.GrandBazaar;

public class YuzeyselSerbestRecurringJob : IRecurringJob
{
    private readonly ForeksHttpClient _foreksHttpClient;
    private readonly ILogger<YuzeyselSerbestRecurringJob> _logger;
    private readonly IConfiguration _configuration;
    private readonly ISerbestPiyasaService _serbestPiyasaService;
    private readonly INotificationService _notificationService;
    private readonly int _maxConcurrentTasks;

    public YuzeyselSerbestRecurringJob(
        ForeksHttpClient foreksHttpClient,
        ILogger<YuzeyselSerbestRecurringJob> logger,
        IConfiguration configuration,
        ISerbestPiyasaService serbestPiyasaService,
        INotificationService notificationService)
    {
        _foreksHttpClient = foreksHttpClient;
        _logger = logger;
        _configuration = configuration;
        _serbestPiyasaService = serbestPiyasaService;
        _notificationService = notificationService;
        _maxConcurrentTasks = _configuration.GetValue("SerbestProcessing:MaxConcurrentTasks", 10);
    }

    public string Name => "YuzeyselSerbestRecurringJob";
    public IEnumerable<string> Crons => ["0 0/1 * * * ?"];

    public async Task ExecuteAsync()
    {
        try
        {
            await ProcessDataTypeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{Name} sırasında kritik hata oluştu.");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    public async Task ProcessDataTypeAsync()
    {
        try
        {
            var url = $"{_configuration["Foreks:SnapShotApi"]}/?{_configuration["Foreks:SnapShot:SerbestPiyasa:Url"]}";
            if (string.IsNullOrEmpty(url))
            {
                _logger.LogError("Foreks:SnapShot:SerbestPiyasa:Url değeri bulunamadı.");
                await _notificationService.NotifyErrorAsync(Name, "Foreks:SnapShot:SerbestPiyasa:Url değeri bulunamadı.");
                return;
            }

            var result = await _foreksHttpClient.FetchDataAsync<YuzeyselDto>(url);
            if (result == null || result.Count == 0)
            {
                _logger.LogWarning("Çekilen veri boş veya null.");
                return;
            }

            using var semaphore = new SemaphoreSlim(_maxConcurrentTasks);

            foreach (var item in result)
            {
                if (item.SEMBOL == "TAHVIL" || item.SEMBOL == "TAHVILF")
                {
                    item.ALIS = item.Son;
                    item.SATIS = item.Son;
                }

                if (!item.ALIS.HasValue || !item.SATIS.HasValue)
                {
                    continue;
                }

                await semaphore.WaitAsync();

                try
                {
                    await ProcessSingleItemAsync(item);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"İşlem sırasında hata oluştu: {item.SEMBOL}");
                    await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.SEMBOL} , Hata: {ex} , Hata Mesajı: {ex.Message}");
                }
                finally
                {
                    semaphore.Release();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    private async Task ProcessSingleItemAsync(YuzeyselDto yuzeyselDto)
    {
        try
        {
            await _serbestPiyasaService.Change(yuzeyselDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {yuzeyselDto.SEMBOL}");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {yuzeyselDto.SEMBOL} , Hata: {ex} , Hata Mesajı: {ex.Message}");
        }
    }
}
