﻿using Xunit;
using Moq;
using StackExchange.Redis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Bigpara.Cache.Redis.Services;
using System;
using System.Collections.Generic;

namespace Bigpara.Cache.Redis.Tests.Services
{
    public class RedisCacheServiceTests
    {
        private readonly Mock<IDatabase> _mockDatabase;
        private readonly Mock<IConnectionMultiplexer> _mockConnectionMultiplexer;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<ILogger<RedisCacheService>> _mockLogger;
        private readonly RedisCacheService _service;

        public RedisCacheServiceTests()
        {
            _mockDatabase = new Mock<IDatabase>();
            _mockConnectionMultiplexer = new Mock<IConnectionMultiplexer>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockLogger = new Mock<ILogger<RedisCacheService>>();

            // <PERSON><PERSON><PERSON>, aşağ<PERSON><PERSON><PERSON> satırı mevcut kodunuzda kullanabilirsiniz:
            _mockConnectionMultiplexer.Setup(x => x.GetDatabase(It.IsAny<int>(), It.IsAny<object>())).Returns(_mockDatabase.Object);
            _mockConfiguration.Setup(x => x["RedisDefaultDbIndex"]).Returns("0");

            _service = new RedisCacheService(_mockConnectionMultiplexer.Object, _mockConfiguration.Object, _mockLogger.Object);
        }

        [Fact]
        public void Add_ShouldSetValue()
        {
            string key = "test-key";
            string value = "test-value";

            _service.Add(key, value, 10);

            _mockDatabase.Verify(db => db.StringSet(key, It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), When.Always, CommandFlags.None), Times.Once);
        }

        [Fact]
        public void Get_ShouldReturnValue()
        {
            string key = "test-key";
            string value = "test-value";
            _mockDatabase.Setup(db => db.StringGet(key, It.IsAny<CommandFlags>())).Returns(Newtonsoft.Json.JsonConvert.SerializeObject(value));

            var result = _service.Get<string>(key);

            Assert.Equal(value, result);
        }

        [Fact]
        public void Remove_ShouldDeleteKey()
        {
            string key = "test-key";
            _mockDatabase.Setup(db => db.KeyExists(key, It.IsAny<CommandFlags>())).Returns(true);

            _service.Remove(key);

            _mockDatabase.Verify(db => db.KeyDelete(key, It.IsAny<CommandFlags>()), Times.Once);
        }

        [Fact]
        public void ContainsKey_ShouldReturnFalse_WhenKeyNotExists()
        {
            string key = "test-key";
            _mockDatabase.Setup(db => db.KeyTimeToLive(key, It.IsAny<CommandFlags>())).Returns((TimeSpan?)null);

            var result = _service.ContainsKey(key);

            Assert.False(result);
        }

        [Fact]
        public void ContainsKey_ShouldReturnTrue_WhenKeyExists()
        {
            string key = "test-key";
            _mockDatabase.Setup(db => db.KeyTimeToLive(key, It.IsAny<CommandFlags>())).Returns(TimeSpan.FromMinutes(1));
            _mockDatabase.Setup(db => db.KeyExists(key, It.IsAny<CommandFlags>())).Returns(true);

            var result = _service.ContainsKey(key);

            Assert.True(result);
        }

        [Fact]
        public void AddTypedList_ShouldSetHash()
        {
            string key = "list-key";
            var list = new List<string> { "a", "b" };

            _service.AddTypedList(key, list, 10);

            _mockDatabase.Verify(db => db.HashSet(key, It.IsAny<HashEntry[]>(), It.IsAny<CommandFlags>()), Times.Once);
            _mockDatabase.Verify(db => db.KeyExpire(key, It.IsAny<TimeSpan?>(), It.IsAny<CommandFlags>()), Times.Once);
        }

        [Fact]
        public void GetTypedList_ShouldReturnList()
        {
            string key = "list-key";
            var list = new List<string> { "a", "b" };
            var hashEntries = new[] { new HashEntry(key, Newtonsoft.Json.JsonConvert.SerializeObject(list)) };
            _mockDatabase.Setup(db => db.HashGetAll(key, It.IsAny<CommandFlags>())).Returns(hashEntries);

            var result = _service.GetTypedList<string>(key);

            Assert.Equal(list, result);
        }
    }
}
