﻿using Bigpara.Application.Common;
using Bigpara.Application.Contracts.Repositories.BigparaDB.Users;
using Bigpara.Application.Contracts.Repositories.BigparaDB.UserSymbols;
using Bigpara.Application.Contracts.Repositories.Matriks.Sembol;
using Bigpara.Domain.Bigpara;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Features.Symbols.Commands
{
    public class AddOrRemoveFollowSymbolCommand 
    {
        public string Symbol { get; set; }
        public int  PageType { get; set; }
        /// <summary>
        /// -1 remove
        /// 1  Add
        /// 2  Truncate
        /// </summary>
        public int Operation { get; set; }
    }

    public class AddOrRemoveFollowSymbolCommandHandler
    {
        private readonly IUserSymbolRepository _repository;
        private readonly ISembolRepository _sembolRepository;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IUserRepository _userRepository;
        public AddOrRemoveFollowSymbolCommandHandler(IUserSymbolRepository repository, ISembolRepository sembolRepository)
        {
            _repository = repository;
            _sembolRepository = sembolRepository;
        }
        public async Task<BaseResponse> Handle(AddOrRemoveFollowSymbolCommand command)
        {
            var currentUser = _httpContextAccessor.HttpContext.User;
            if(currentUser == null || !currentUser.Identity.IsAuthenticated)
            {
                return new BaseResponse { Errors = { "Kullanıcı oturumu bulunamadı." } };
            }

            var email = currentUser.Claims.FirstOrDefault(c => c.Type == "email")?.Value;
            if (string.IsNullOrEmpty(email))
            {
                return new BaseResponse { Errors = { "Kullanıcı oturumu bulunamadı." } };
            }
            var user = await _userRepository.GetUserByEmail(email);

            if (user==null)
            {
                return new BaseResponse { Errors = { "Kullanıcı oturumu bulunamadı." } };
            }

            BaseResponse response = new BaseResponse();
            var symbol= await  _sembolRepository.GetSembollerByCode(command.Symbol);
            if (symbol ==null)
            {
                response.Errors.Add("Geçersiz sembol tanımı");
                return response;
            }
            var userSymbol = new UserSembol
            {
                UserId = user.Id,
                SembolId = symbol.SembolId,
                PageType = command.PageType
            };
            await _repository.AddOrDeleteAsync(command.Operation, userSymbol);

            return response;
        }
    }


}
