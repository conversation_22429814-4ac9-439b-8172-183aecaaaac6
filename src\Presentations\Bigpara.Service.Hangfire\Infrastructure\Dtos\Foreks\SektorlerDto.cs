﻿using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;

namespace Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;

public class SektorlerDto
{
    [JsonProperty("code")]
    public string StrKod { get; set; }

    [JsonProperty("name")]
    public string? StrAd { get; set; }

    [JsonIgnore]
    public string? StrSektorKodu { get; set; }

    [JsonIgnore]
    public string? StrSektorId { get; set; }

    [JsonProperty("status")]
    [NotMapped]
    private string? StatusString { get; set; }

    [JsonIgnore]
    public bool Aktif
    {
        get
        {
            return StatusString?.Trim().ToUpperInvariant() switch
            {
                "ACTIVE" => true,
                "PASSIVE" => false,
                _ => false
            };
        }
        set => StatusString = value ? "ACTIVE" : "PASSIVE";
    }
}
