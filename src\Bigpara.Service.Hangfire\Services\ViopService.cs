﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructure.Data;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class ViopService : IViopService
{
    private readonly ILogger<ViopService> _logger;
    private readonly IStoredProcedureParameterService _storedProcedureService;
    private readonly TimeZoneInfo _turkeyTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");

    public ViopService
    (
        IStoredProcedureParameterService storedProcedureService,
        ILogger<ViopService> logger
    )
    {
        _storedProcedureService = storedProcedureService;
        _logger = logger;
    }

    public async Task Change(YuzeyselDto item)
    {
        try
        {
            var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

            item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
                _turkeyTimeZone);

            item.StrBoard = SembolHelper.FindStrBoard(item.LegacyCode);

            var imkbVipSessionParameters = storedProcedureHelper.CheckImkbVipSessionParameter(item);
            if (imkbVipSessionParameters != null)
                await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_ImkbVipSession", imkbVipSessionParameters, item?.SEMBOL);

            var imkbVipSessionHD1PParameters = storedProcedureHelper.CheckImkbVipSessionHD1PParameter(item);
            if (imkbVipSessionHD1PParameters != null)
                await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_ImkbVipSessionHD1P", imkbVipSessionHD1PParameters, item?.SEMBOL);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {item.SEMBOL}");
        }
    }

    public async Task ChangeHistorical(YuzeyselDto item)
    {
        try
        {
            var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

            item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
                _turkeyTimeZone);

            var imkbViopHistoricalParameters = storedProcedureHelper.CheckImkbViopHistoricalParameter(item);
            if (imkbViopHistoricalParameters != null)
                await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_ImkbViopTarihsel", imkbViopHistoricalParameters, item?.SEMBOL);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {item.SEMBOL}");
        }
    }
}