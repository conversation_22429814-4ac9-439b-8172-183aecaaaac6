﻿using Bigpara.Application.Contracts.Repositories.Quark.News;
using Bigpara.External.Quark.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.External.Quark.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddQuarkCms(this IServiceCollection services) {

            services.AddScoped<IQuarkClient, QuarkClient>();
            services.AddScoped(typeof(IQuarkRepository<,>), typeof(QuarkRepository<,>));
            services.AddScoped<INewsSearchRepository, NewsSearchRepository>();
            return services;
        }
    }
}
