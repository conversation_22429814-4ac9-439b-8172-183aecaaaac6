﻿using Bigpara.Application.Contracts.Repositories.BigparaDB.Users;
using Bigpara.Domain.Bigpara;
using Microsoft.Data.SqlClient;
using System.Data;

namespace Bigpara.Persistence.BigparaDB.Users;

public class UserRepository : IUserRepository
{
    private readonly IBigparaDbContext _bigparaDbContext;

    public UserRepository
    (
        IBigparaDbContext bigparaDbContext
    )
    {
        _bigparaDbContext = bigparaDbContext;
    }

    public async Task<User> RegisterUser(User user)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("hurPassId", user.HurPassId),
            new SqlParameter("userTypeId",(int)user.UserType),
            new SqlParameter("firstname", user.FirstName),
            new SqlParameter("lastName", user.LastName),
            new SqlParameter("addDate", user.AddDate),
            new SqlParameter("address", user.Address),
            new SqlParameter("email", user.Email ?? ""),
            new SqlParameter("isActive",true)
        };

        user.Id = await _bigparaDbContext.ExecuteNonQueryAsync("spCreateUser", parameters.ToArray());

        return user;
    }

    public async Task<User> RegisterOrUpdateUser(User user)
    {
        if (user.CountryName == null)
            user.CountryName = string.Empty;
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("hurPassId", user.HurPassId),
            new SqlParameter("firstname", user.FirstName),
            new SqlParameter("lastName", user.LastName),
            new SqlParameter("birthDate", user.BirthDate),
            new SqlParameter("email", user.Email ?? ""),
            new SqlParameter("cityId",user.CityId),
            new SqlParameter("countryId",user.CountryId),
            new SqlParameter("address", user.Address),
            new SqlParameter("gender",user.Gender),
            new SqlParameter("fbProfile",user.FBProfile),
            new SqlParameter("liProfile",user.LIProfile),
            new SqlParameter("twProfile",user.TWProfile),
            new SqlParameter("isHurPass",user.IsHurPassData),
            new SqlParameter("addressType",user.AddressType),
            new SqlParameter("tckNumber",user.TckNumber),
            new SqlParameter("taxNumber",user.TaxNumber),
            new SqlParameter("companyName",user.CompanyName),
            new SqlParameter("taxAdministration",user.TaxAdministration)
        };

        if (user.ZipCode == 0)
            parameters.Add(new SqlParameter("zipCode", null));
        else
            parameters.Add(new SqlParameter("zipCode", user.ZipCode));

        user.Id = await _bigparaDbContext.ExecuteNonQueryAsync("spCreateOrUpdateUserV2", parameters.ToArray());

        return user;
    }

    public async Task<int> InsertUserPaymentDetails(UserPayment details)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("Id", details.Id),
            new SqlParameter("Subscription_Domain",details.Subscription_Domain),
            new SqlParameter("Start_Date", details.Start_Date),
            new SqlParameter("End_Date", details.End_Date),
            new SqlParameter("User_Id", details.User_Id),
            new SqlParameter("Crm_Product_Id", details.Crm_Product_Id),
            new SqlParameter("Domain_Product_Id", details.Domain_Product_Id)
        };

        int result = await _bigparaDbContext.ExecuteNonQueryAsync("spInsUpdUserPaymentResult", parameters.ToArray());

        return result;
    }

    public async Task<User> GetUserByUsernamePassword(string username, string password)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("Email", username),
            new SqlParameter("Password", password)
        };

        return await _bigparaDbContext.ExecuteQuerySingleAsync<User>("spGetUserByUsernamePassword", parameters.ToArray());
    }

    public async Task<int> LoginLogger(UserActivityLogger userActivityLogger)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("HurPassId", userActivityLogger.HurPassId),
            new SqlParameter("LoginDate",DateTime.Now),
            new SqlParameter("SourceType", userActivityLogger.SourceType),
            new SqlParameter("ClientIP", userActivityLogger.ClientIP),
            new SqlParameter("HurPassSessionId", userActivityLogger.HurPassSessionId)
        };

        int returnValue = await _bigparaDbContext.ExecuteNonQueryAsync("spCreateLoginCount", parameters.ToArray());

        return returnValue;
    }

    public async Task<int> CanliBorsaViewLog(UserActivityLogger userActivityLogger)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("HurPassId", userActivityLogger.HurPassId),
            new SqlParameter("ViewDate",DateTime.Now),
            new SqlParameter("SourceType", userActivityLogger.SourceType),
            new SqlParameter("ClientIP", userActivityLogger.ClientIP),
            new SqlParameter("HurPassSessionId", userActivityLogger.HurPassSessionId),
            new SqlParameter("UserId", userActivityLogger.UserId)
        };

        int returnValue = await _bigparaDbContext.ExecuteNonQueryAsync("spCreateLiveStockActivityLog", parameters.ToArray());

        return returnValue;
    }

    public async Task<int> CanliBorsaFormFillLog(UserActivityLogger userActivityLogger)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("HurPassId", userActivityLogger.HurPassId),
            new SqlParameter("ViewDate",DateTime.Now),
            new SqlParameter("SourceType", userActivityLogger.SourceType),
            new SqlParameter("ClientIP", userActivityLogger.ClientIP),
            new SqlParameter("HurPassSessionId", userActivityLogger.HurPassSessionId),
             new SqlParameter("UserId", userActivityLogger.UserId)
        };

        int returnValue = await _bigparaDbContext.ExecuteNonQueryAsync("spCreateLiveStockFormFilledActivityLog", parameters.ToArray());

        return returnValue;
    }

    public async Task<User> GetUserByHurpassID(string hurpassID)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("hurpassId", hurpassID)
        };

        return await _bigparaDbContext.ExecuteQuerySingleAsync<User>("spGetUserByHurPassId", parameters.ToArray());
    }

    public async Task<User> GetUserByEmail(string email)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("email", email)
        };

        return await _bigparaDbContext.ExecuteQuerySingleAsync<User>("spGetUserByEmail", parameters.ToArray());
    }

    public async Task<UserPayment> GetLicencedUserByHurpassID(string hurpassID)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("User_Id", hurpassID)
        };

        return await _bigparaDbContext.ExecuteQuerySingleAsync<UserPayment>("spGetUserPaymentDetails", parameters.ToArray());
    }

    public List<User> GetUserList(int pageIndex, int pageSize, out int totalCount)
    {
        var outTotalCountParameter = new SqlParameter() { ParameterName = "totalCount", SqlDbType = SqlDbType.Int, Direction = ParameterDirection.Output };

        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("pageNo",pageIndex),
            new SqlParameter("pageSize", pageSize),
            outTotalCountParameter
        };

        var result = _bigparaDbContext.ExecuteStoredProcedureAsync<User>("dbo.spGetUserList", parameters.ToArray()).GetAwaiter().GetResult();

        totalCount = (int)(outTotalCountParameter.Value ?? 0);

        return result ?? new List<User>();
    }

    public async Task<int> UpdateUserLocation(User user)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("userId", user.Id),
            new SqlParameter("address",user.Address),
            new SqlParameter("zipCode", user.ZipCode),
            new SqlParameter("cityId", user.CityId),
            new SqlParameter("countryId", user.CountryId),
            new SqlParameter("email", user.Email??string.Empty)
        };

        var result = await _bigparaDbContext.ExecuteNonQueryAsync("spUpdateUserLocation", parameters.ToArray());

        return result;
    }

    public async Task UpdateUserLastLoginDate(User user)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("userId",user.Id),
            new SqlParameter("lastLoginDate",user.LastLoginDate)
        };

        await _bigparaDbContext.ExecuteNonQueryAsync("spUpdateUserLastLoginDate", parameters.ToArray());
    }

    public async Task<int> DeleteUser(User user)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("Id", user.Id)
        };

        return await _bigparaDbContext.ExecuteNonQueryAsync("spDeleteUser", parameters.ToArray());
    }

    public async Task<bool> IsEmptyUserAddress(string hurpassID)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("hurPassId", hurpassID)
        };

        return await _bigparaDbContext.ExecuteNonQueryAsync("spIsEmptyUserAddress", parameters.ToArray()) == 1 ? true : false;
    }
}
