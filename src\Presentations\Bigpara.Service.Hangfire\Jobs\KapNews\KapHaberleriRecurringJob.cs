﻿using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Bigpara;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.KapNews;

public class KapHaberleriRecurringJob : IRecurringJob
{
    private readonly IBorsaService _borsaService;
    private readonly IRedisCacheService _redisCacheService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<KapHaberleriRecurringJob> _logger;


    public KapHaberleriRecurringJob
    (
        IBorsaService borsaService,
        IRedisCacheService redisCacheService,
        INotificationService notificationService,
        ILogger<KapHaberleriRecurringJob> logger
    )
    {
        _borsaService = borsaService;
        _redisCacheService = redisCacheService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "KapHaberleriRecurringJob";
    public IEnumerable<string> Crons => ["*/3 * * * *"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("KapHaberleriRecurringJob - Başladı ");
            await ProcessKapHaberleri();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    public async Task ProcessKapHaberleri()
    {
        try
        {
            var hisseList = await _borsaService.GetHisseList();

            foreach (var sembol in hisseList)
            {
                try
                {
                    var map = new Dictionary<string, List<SymbolNews>>();

                    //var haber = await _haberService.GetNewsBySymbol(sembol.SEMBOLID);

                    // map[sembol.SEMBOLID.ToString()] = haber;
                    _redisCacheService.Store("kaphaberleri", map);
                }
                catch (Exception ex)
                {

                    _logger.LogError(ex.Message);
                    //TODO: Check
                    //EmailHelper.SendEmail("KapHaberleri", exp.Message);
                    await _notificationService.NotifyErrorAsync($"KapHaberleri", ex.Message);
                }
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }
}
