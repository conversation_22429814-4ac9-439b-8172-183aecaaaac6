﻿using Bigpara.Domain;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.Data;
using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Configuration;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Symbol;

public class DailyMarketSymbolSyncJob : IRecurringJob
{
    private readonly ForeksHttpClient _foreksHttpClient;
    private readonly ILogger<DailyMarketSymbolSyncJob> _logger;
    private readonly IConfiguration _configuration;
    private readonly ISymbolService _symbolService;
    private readonly ISembollerService _sembollerService;
    private readonly FeedConfigurationService _feedConfigService;
    private readonly INotificationService _notificationService;
    private readonly IStoredProcedureParameterService _storedProcedureService;

    private readonly int _maxConcurrentTasks = 1;

    public DailyMarketSymbolSyncJob(
        ForeksHttpClient foreksHttpClient,
        ILogger<DailyMarketSymbolSyncJob> logger,
        IConfiguration configuration,
        ISymbolService symbolService,
        FeedConfigurationService feedConfigService,
        ISembollerService sembollerService,
        INotificationService notificationService,
        IStoredProcedureParameterService storedProcedureService)
    {
        _foreksHttpClient = foreksHttpClient;
        _logger = logger;
        _configuration = configuration;
        _symbolService = symbolService;
        _feedConfigService = feedConfigService;
        _sembollerService = sembollerService;
        _notificationService = notificationService;
        _maxConcurrentTasks = _configuration.GetValue("DailyMarketSymbolSyncProcessing:MaxConcurrentTasks", 1);
        _storedProcedureService = storedProcedureService;
    }

    public string Name => "DailyMarketSymbolSyncJob";
    public IEnumerable<string> Crons => ["0 0 8 * * ?"];

    public async Task ExecuteAsync()
    {
        try
        {
            await ProcessAllDataTypesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{Name} sırasında kritik hata oluştu.");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    private async Task ProcessAllDataTypesAsync()
    {
        try
        {
            var passiveSymbols = new List<Semboller>();
            //var allSektorler = await _sektorService.GetSektorler();

            var allSektorler = await _storedProcedureService.ExecuteStoredProcedureAsync<Sektorler>("sp_foreks_GetTanimSektorler");

            var feedConfig = _feedConfigService.LoadFeedConfiguration();
            if (string.IsNullOrEmpty(feedConfig.SymbolApi))
            {
                _logger.LogError("Feed:SymbolApi değeri bulunamadı!");
                await _notificationService.NotifyErrorAsync(Name, "Feed:SymbolApi değeri bulunamadı!");
                return;
            }

            foreach (var feedItem in feedConfig.Items)
            {
                var url = $"{feedConfig.SymbolApi}?{feedItem.Value.Url}";

                if (string.IsNullOrWhiteSpace(url))
                {   
                    _logger.LogError($"{feedItem.Key} için URL boş veya null!");
                    await _notificationService.NotifyErrorAsync(Name, $"{feedItem.Key} için URL boş veya null!");
                    continue;
                }

                try
                {
                    var dbSybmols = await _sembollerService.GetSembollersByPiyasaIdAsync(feedItem.Value.Id);
                    var symbols = await _foreksHttpClient.FetchDataAsync<ForeksSembolDto>(url);

                    if (symbols == null || symbols.Count == 0)
                    {
                        _logger.LogWarning($"{feedItem.Key} için boş veri alındı!");
                        continue;
                    }

                    await ProcessActiveSymbolsBatchAsync(symbols, feedItem.Value.Id, allSektorler);

                    if (feedItem.Value.Id == 4 || feedItem.Value.Id == 9 || feedItem.Value.Id == 27)
                    {
                        var deprecatedSymbols = dbSybmols.Where(db => !symbols.Any(s => s.Sembol == db.Sembol)).ToList();
                        passiveSymbols.AddRange(deprecatedSymbols);
                        await ProcessPassiveSymbolsBatchAsync(passiveSymbols);
                    }
                }
                catch (HttpRequestException httpEx)
                {
                    _logger.LogError($"HTTP hatası: {httpEx.Message} | URL: {url}");
                    await _notificationService.NotifyErrorAsync(Name, $"HTTP hatası: {httpEx.Message} | URL: {url}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Genel Hata: {ex} , Hata Mesajı: {ex.Message} | URL: {url}");
                    await _notificationService.NotifyErrorAsync(Name, $"Genel Hata: {ex} , Hata Mesajı: {ex.Message} | URL: {url}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"İşlem sırasında hata oluştu, Hata: {ex} , Hata Mesajı: {ex.Message}");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu, Hata: {ex} , Hata Mesajı: {ex.Message}");
        }
    }

    private async Task ProcessActiveSymbolsBatchAsync(IEnumerable<ForeksSembolDto> symbols, int feedItemId,
        List<Sektorler> allSektorler)
    {
        using var semaphore = new SemaphoreSlim(_maxConcurrentTasks);

        foreach (var symbol in symbols)
        {
            await semaphore.WaitAsync();

            try
            {
                await _symbolService.Change(symbol, feedItemId, allSektorler);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Sembol işleme hatası: {symbol.Sembol}");
                await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {symbol.Sembol}, Hata: {ex} , Hata Mesajı: {ex.Message}");
            }
            finally
            {
                semaphore.Release();
            }
        }
    }

    private async Task ProcessPassiveSymbolsBatchAsync(List<Semboller> symbols)
    {
        using var semaphore = new SemaphoreSlim(_maxConcurrentTasks);

        foreach (var symbol in symbols)
        {
            await semaphore.WaitAsync();

            try
            {
                await _symbolService.ChangePassive(symbol.Sembol);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Sembol işleme hatası: {symbol.Sembol}");
                await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {symbol.Sembol}, Hata: {ex} , Hata Mesajı: {ex.Message}");
            }
            finally
            {
                semaphore.Release();
            }
        }
    }
}