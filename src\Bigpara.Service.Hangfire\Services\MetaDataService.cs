﻿
using Bigpara.Application.Contracts.Repositories.MetaData;
using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Bigpara;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class MetaDataService : IMetaDataService
{

    private readonly ICacheService _cacheService;
    private readonly IMetaDataRepository _metaDataRepository;

    public MetaDataService
    (
        IMetaDataRepository metaDataRepository,
        ICacheService cacheService
    )
    {
        _metaDataRepository = metaDataRepository;
        _cacheService = cacheService;
    }

    public async Task<List<Country>> GetCountries()
    {
        string key = string.Format(CacheKeys.META_DATA_KEY, "countries");

        return await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, () => _metaDataRepository.GetCountries());
    }

    public async Task<List<City>> GetCities()
    {
        string key = string.Format(CacheKeys.META_DATA_KEY, "cities");

        return await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, () => _metaDataRepository.GetCities());
    }

    public async Task<List<City>> GetCitiesByCountryId(int id)
    {
        string key = string.Format(CacheKeys.META_DATA_KEY_BY_ID, "cities", id);

        return await _cacheService.GetAsync(
            key,
            CacheKeys.CACHE_DATA_DURATION,
            async () =>
            {
                var allCities = await GetCities();
                return allCities.Where(r => r.CountryId == id).ToList();
            });
    }
}
