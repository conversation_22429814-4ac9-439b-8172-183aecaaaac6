<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Realtime Trade Takip</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .buy { color: green; }
        .sell { color: red; }
    </style>
</head>
<body>
    <h1>Realtime Trade Verisi</h1>
    <table id="tradeTable">
        <thead>
            <tr>
                <th>Sembol</th>
                <th>Fiyat</th>
                <th>Miktar</th>
                <th>Yön</th>
                <th><PERSON><PERSON></th>
            </tr>
        </thead>
        <tbody id="tradeBody">
            <!-- Trade verileri buraya eklenecek -->
        </tbody>
    </table>

    <script>
        const connection = new signalR.HubConnectionBuilder()
            .withUrl("https://localhost:7115/tradehub")
            .configureLogging(signalR.LogLevel.Information)
            .build();

        connection.on("ReceiveTradeData", (tradeData) => {
            console.log("Trade verisi alındı:", tradeData);
            addTradeRow(tradeData);
        });

        async function start() {
            try {
                await connection.start();
                console.log("SignalR bağlantısı kuruldu.");
            } catch (err) {
                console.error("SignalR bağlantısı kurulamadı:", err);
                setTimeout(start, 5000);
            }
        }

        connection.onclose(async () => {
            await start();
        });

        function addTradeRow(trade) {
            const tbody = document.getElementById("tradeBody");
            const row = document.createElement("tr");
            
            // ISO formatına çevir
            const date = new Date(parseInt(trade.timestamp));
            const formattedTime = date.toLocaleTimeString();
            
            row.innerHTML = `
                <td>${trade.symbol}</td>
                <td>${trade.price}</td>
                <td>${trade.quantity}</td>
                <td class="${trade.side.toLowerCase()}">${trade.side}</td>
                <td>${formattedTime}</td>
            `;
            
            // Tablonun en üstüne ekle
            if (tbody.firstChild) {
                tbody.insertBefore(row, tbody.firstChild);
            } else {
                tbody.appendChild(row);
            }
            
            // Sadece son 20 satırı göster
            while (tbody.children.length > 20) {
                tbody.removeChild(tbody.lastChild);
            }
        }

        // Bağlantıyı başlat
        start();
    </script>
</body>
</html>