using Bigpara.Application.Tests.Common;
using Bigpara.Domain.Bigpara;
using Bigpara.Domain.Events;
using Xunit;

namespace Bigpara.Application.Tests.Domain;

public class UserTests : TestBase
{
    [Fact]
    public void Create_User_Should_Set_Properties_Correctly()
    {
        // Arrange
        var hurPassId = "test-hurpass-id";
        var firstName = "John";
        var lastName = "Doe";
        var email = "<EMAIL>";
        var userType = UserType.Bigpara;

        // Act
        var user = User.Create(hurPassId, firstName, lastName, email, userType);

        // Assert
        Assert.Equal(hurPassId, user.HurPassId);
        Assert.Equal(firstName, user.FirstName);
        Assert.Equal(lastName, user.LastName);
        Assert.Equal(email, user.Email);
        Assert.Equal(userType, user.UserType);
        Assert.Equal((byte)userType, user.UserTypeId);
        Assert.True(user.IsActive);
        Assert.False(user.IsVerified);
        Assert.False(user.IsSecure);
        Assert.True(user.CreatedAt <= DateTime.UtcNow);
        Assert.True(user.LastLoginDate <= DateTime.UtcNow);
        Assert.True(user.LastActivityDate <= DateTime.UtcNow);
    }

    [Fact]
    public void Create_User_Should_Add_UserRegisteredEvent()
    {
        // Arrange
        var hurPassId = "test-hurpass-id";
        var firstName = "John";
        var lastName = "Doe";
        var email = "<EMAIL>";
        var userType = UserType.Bigpara;

        // Act
        var user = User.Create(hurPassId, firstName, lastName, email, userType);

        // Assert
        Assert.Single(user.DomainEvents);
        Assert.IsType<UserRegisteredEvent>(user.DomainEvents.First());
        
        var domainEvent = (UserRegisteredEvent)user.DomainEvents.First();
        Assert.Equal(user.Id, domainEvent.UserId);
        Assert.Equal(email, domainEvent.Email);
    }

    [Fact]
    public void UpdateLastLogin_Should_Update_Timestamps()
    {
        // Arrange
        var user = User.Create("test-id", "John", "Doe", "<EMAIL>", UserType.Bigpara);
        var originalLoginDate = user.LastLoginDate;
        var originalActivityDate = user.LastActivityDate;

        // Wait a bit to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        user.UpdateLastLogin();

        // Assert
        Assert.True(user.LastLoginDate > originalLoginDate);
        Assert.True(user.LastActivityDate > originalActivityDate);
        Assert.NotNull(user.UpdatedAt);
    }

    [Fact]
    public void AddSymbol_Should_Add_Symbol_To_User()
    {
        // Arrange
        var user = User.Create("test-id", "John", "Doe", "<EMAIL>", UserType.Bigpara);
        var symbolId = 123;
        var pageType = 1;
        var orderId = 1;

        // Act
        user.AddSymbol(symbolId, pageType, orderId);

        // Assert
        Assert.Single(user.UserSymbols);
        var userSymbol = user.UserSymbols.First();
        Assert.Equal(symbolId, userSymbol.SembolId);
        Assert.Equal(pageType, userSymbol.PageType);
        Assert.Equal(orderId, userSymbol.OrderId);
    }

    [Fact]
    public void AddSymbol_Should_Not_Add_Duplicate_Symbol()
    {
        // Arrange
        var user = User.Create("test-id", "John", "Doe", "<EMAIL>", UserType.Bigpara);
        var symbolId = 123;
        var pageType = 1;
        var orderId = 1;

        // Act
        user.AddSymbol(symbolId, pageType, orderId);
        user.AddSymbol(symbolId, pageType, orderId); // Try to add same symbol again

        // Assert
        Assert.Single(user.UserSymbols);
    }

    [Fact]
    public void RemoveSymbol_Should_Remove_Symbol_From_User()
    {
        // Arrange
        var user = User.Create("test-id", "John", "Doe", "<EMAIL>", UserType.Bigpara);
        var symbolId = 123;
        var pageType = 1;
        var orderId = 1;

        user.AddSymbol(symbolId, pageType, orderId);

        // Act
        user.RemoveSymbol(symbolId, pageType);

        // Assert
        Assert.Empty(user.UserSymbols);
    }

    [Fact]
    public void Verify_Should_Set_IsVerified_To_True()
    {
        // Arrange
        var user = User.Create("test-id", "John", "Doe", "<EMAIL>", UserType.Bigpara);

        // Act
        user.Verify();

        // Assert
        Assert.True(user.IsVerified);
        Assert.NotNull(user.UpdatedAt);
    }

    [Fact]
    public void Deactivate_Should_Set_IsActive_To_False()
    {
        // Arrange
        var user = User.Create("test-id", "John", "Doe", "<EMAIL>", UserType.Bigpara);

        // Act
        user.Deactivate();

        // Assert
        Assert.False(user.IsActive);
        Assert.NotNull(user.UpdatedAt);
    }

    [Fact]
    public void Activate_Should_Set_IsActive_To_True()
    {
        // Arrange
        var user = User.Create("test-id", "John", "Doe", "<EMAIL>", UserType.Bigpara);
        user.Deactivate();

        // Act
        user.Activate();

        // Assert
        Assert.True(user.IsActive);
        Assert.NotNull(user.UpdatedAt);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void Create_User_With_Invalid_Email_Should_Handle_Gracefully(string email)
    {
        // Arrange & Act
        var user = User.Create("test-id", "John", "Doe", email, UserType.Bigpara);

        // Assert
        Assert.Equal(email, user.Email);
    }
}
