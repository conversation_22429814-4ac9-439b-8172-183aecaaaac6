using Bigpara.Service.Hangfire.Extensions;
using Bigpara.Service.Hangfire.Filters;
using Bigpara.Service.Hangfire.Jobs;
using Google.Protobuf.WellKnownTypes;
using Hangfire;
using Microsoft.Extensions.Configuration;
using MongoDB.Bson;
using StackExchange.Redis;

internal class Program
{
    private static async Task Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
		ConfigureServices(builder);

		var app = builder.Build();
		ConfigureJobs(app, builder);
		ConfigureMiddleware(app,builder);


		await app.RunAsync();
    }

    private static void ConfigureServices(WebApplicationBuilder builder)
    {
		builder.Services.AddBigparaServices(builder.Configuration);
		builder.Services.AddBigparaHttpClients(builder.Configuration);
		builder.Services.AddBigparaHangfireJobs(builder.Configuration);
		builder.AddServiceDefaults();
		builder.Services.AddControllersWithViews();
	}

    private static void ConfigureJobs(WebApplication app, WebApplicationBuilder builder)
	{
		var isRecurringJobEnabled = builder.Configuration.GetValue("RecurringJobs:IsEnabled", true);
		if (isRecurringJobEnabled)
		{
			app.UseHangfireDashboard("/hangfire", new DashboardOptions
			{
				Authorization = new[] { new BasicAuthAuthorizationFilter(builder.Configuration) }
			});

			//IRecurringJob’u kullanarak tekrarlayan görev ekleyin
			using (var scope = app.Services.CreateScope())
			{
				var recurringJobManager = scope.ServiceProvider.GetRequiredService<IRecurringJobManager>();
				var jobs = scope.ServiceProvider.GetServices<IRecurringJob>(); // Tüm jobları al
				var timeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");

				foreach (var job in jobs)
				{
					var cronList = job.Crons.ToList();

					if (!cronList.Any())
					{
						Console.WriteLine($"Warning: {job.Name} job'ında cron tanımı bulunamadı.");
						continue;
					}

					for (int i = 0; i < cronList.Count; i++)
					{
						var cron = cronList[i];
						var jobName = cronList.Count == 1 ? job.Name : $"{job.Name}_{i + 1}";

						var options = new RecurringJobOptions
						{
							TimeZone = timeZone
						};

						try
						{
							recurringJobManager.AddOrUpdate(
								jobName,
								() => job.ExecuteAsync(),
								cron,
								options
							);

							Console.WriteLine($"Job kaydedildi: {jobName} - {cron}");
						}
						catch (Exception ex)
						{
							Console.WriteLine($"Job kaydedilemedi: {jobName} - {ex.Message}");
						}
					}
				}
			}
		}
	}
    private static void ConfigureMiddleware(WebApplication app,WebApplicationBuilder builder) {
		
		app.MapDefaultEndpoints();

		// Configure the HTTP request pipeline.
		if (!app.Environment.IsDevelopment())
		{
			app.UseExceptionHandler("/Home/Error");
			// The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
			app.UseHsts();
		}

		


		app.UseHttpsRedirection();
		app.UseStaticFiles();

		app.UseRouting();

		app.UseAuthorization();

		
		app.MapControllerRoute(
			name: "default",
			pattern: "{controller=Home}/{action=Index}/{id?}");
	}
}