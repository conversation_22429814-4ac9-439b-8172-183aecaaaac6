﻿using Bigpara.Domain.Enums;
using Bigpara.Domain.SummaryTypes;

namespace Bigpara.Application.Contracts.Repositories.Matriks.Borsa;

public interface IGecmisKapanisRepository
{
    List<SembolOzet> GetGecmisKapanisList(DateTime? selectedDate, MarketTypes marketType, int pageNo, byte pageSize, char? letter, out int totalCount);
    Task<List<SembolOzet>> GetGecmisKapanisList(DateTime? selectedDate, MarketTypes marketType, char? letter);
    Task<SembolOzet> GetGecmisKapanisMaxDate();
}
