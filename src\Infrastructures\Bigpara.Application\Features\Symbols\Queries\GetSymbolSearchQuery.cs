﻿
using Bigpara.Application.Contracts.Repositories.Matriks.Piyasalar;
using Bigpara.Application.Contracts.Repositories.Matriks.Sembol;
using Bigpara.Application.Features.Symbols.Queries.ViewModels;
using MediatR;

namespace Bigpara.Application.Features.Symbols.Queries;


#region Query
public class GetSymbolSearchQuery : IRequest<SymbolSearchQueryViewModel>
{
    public string Type { get; set; }
    public string Keyword { get; set; }
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
    public int BaazarId { get; set; }
}

#endregion

#region Handler
public class GetSymbolSearchQueryHandler : IRequestHandler<GetSymbolSearchQuery, SymbolSearchQueryViewModel>
{
    private readonly ISembolRepository _sembollerService;
    private readonly IPiyasaRepository _piyasaRepository;
    public GetSymbolSearchQueryHandler(ISembolRepository sembollerService, IPiyasaRepository piyasaRepository)
    {
        _sembollerService = sembollerService;
        _piyasaRepository = piyasaRepository;
    }

    public async Task<SymbolSearchQueryViewModel> Handle(GetSymbolSearchQuery request, CancellationToken cancellationToken)
    {
        var cachedIndexSymbols = await _sembollerService.GetSembollerList();
        var quearyableSymbols = cachedIndexSymbols.AsQueryable().Where(p => string.IsNullOrEmpty(request.Type) || p.SecurityType.ToLower() == request.Type.ToLower());

        var skip = request.PageIndex * request.PageSize;

        if (skip > 0)
            quearyableSymbols = quearyableSymbols.Skip(skip);

        if (request.PageSize > 0)
            quearyableSymbols = quearyableSymbols.Take(request.PageSize);


        var filteredSymbols = quearyableSymbols
            .Where(p => string.IsNullOrEmpty(request.Keyword) ||
                        p.Sembol.ToLower().StartsWith(request.Keyword.ToLower()) ||
                        p.Aciklama != null && p.Aciklama.ToLower().StartsWith(request.Keyword.ToLower()));

        // Paging
        var pagedSymbols = filteredSymbols
            .ToList();

        var sembols = new List<SembolItemModel>();
        foreach (var x in pagedSymbols)
        {
            var piyasa = await _piyasaRepository.GetById(x.PiyasaId.Value);
            
            sembols.Add(new SembolItemModel
            {
                Symbol = x.Sembol,
                Description = x.Aciklama,
                Icon = $"{x.SembolId}.png",
                Id = x.SembolId,
                Bazaar = piyasa?.PIYASAKODU,
                Exchange = x.PiyasaId != 3 ? "BIST" : "SPOT",
                Type = x.SecurityType,
            });
        }

        var response = new SymbolSearchQueryViewModel
        {
            Data = sembols
        };

        return response;
    }
}

#endregion


