﻿using Bigpara.Application.Contracts.Repositories.Matriks.OtomatikTeknikYorum;
using Bigpara.Domain.Matriks;
using Microsoft.Data.SqlClient;

namespace Bigpara.Persistence.Matriks.OtomatikTeknikYorum;

public class OtomatikTeknikYorumlarRepository : IOtomatikTeknikYorumlarRepository
{
    private readonly IMatriksDbContext _matriksDbContext;

    public OtomatikTeknikYorumlarRepository(IMatriksDbContext matriksDbContext)
    {
        _matriksDbContext = matriksDbContext;
    }

    private static List<SqlParameter> Parameters
    {
        get { return new List<SqlParameter>(); }
    }

    public async Task<List<OtomatikTeknikYorumlar>> GetOtomatikTeknikYorumlars(string sembol, DateTime dateTime)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembol", sembol));
        parameters.Add(new SqlParameter("tarih ", dateTime));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<OtomatikTeknikYorumlar>("bp.pGetOtomatikTeknikYorumlarBySembolTarih", parameters.ToArray());
    }

    public async Task<int> CreateOrUpdateOtomatikTeknikYorumlar(OtomatikTeknikYorumlar otomatikTeknikYorumlar)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembol", otomatikTeknikYorumlar.SEMBOL));
        parameters.Add(new SqlParameter("tarih ", otomatikTeknikYorumlar.TARIH));
        parameters.Add(new SqlParameter("yorum1", otomatikTeknikYorumlar.Yorum1));
        parameters.Add(new SqlParameter("yorum2", otomatikTeknikYorumlar.Yorum2));
        parameters.Add(new SqlParameter("tablo1", otomatikTeknikYorumlar.Tablo1));
        parameters.Add(new SqlParameter("tablo2", otomatikTeknikYorumlar.Tablo2));
        parameters.Add(new SqlParameter("tablo3", otomatikTeknikYorumlar.Tablo3));

        return await _matriksDbContext.ExecuteNonQueryAsync("bp.CreateOrUpdateOtomatikTeknikYorumlar", parameters.ToArray());
    }

    public virtual async Task<List<OtomatikTeknikYorumlar>> GetOtomatikTeknikYorumlars(string sembol)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembol", sembol));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<OtomatikTeknikYorumlar>("bp.pGetOtomatikTeknikYorumlar", parameters.ToArray());
    }
}

