﻿using Bigpara.Application.Contracts.Repositories.Matriks.PiyasaTakvim;
using Bigpara.Domain.Matriks;
using Microsoft.Data.SqlClient;

namespace Bigpara.Persistence.Matriks.PiyasaTakvim;

public class PiyasaTakvimiRepository : IPiyasaTakvimiRepository
{
    private readonly IMatriksDbContext _matriksDbContext;

    public PiyasaTakvimiRepository(IMatriksDbContext matriksDbContext)
    {
        _matriksDbContext = matriksDbContext;
    }

    public async Task<List<PiyasaTakvimi>> GetPiyasaTakvimiTopData(int topCount)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("topCount", topCount)
        };

        return await _matriksDbContext.ExecuteStoredProcedureAsync<PiyasaTakvimi>("bp.pGetPiyasaTakvimiTopCount", parameters.ToArray());
    }

    public async Task<List<PiyasaTakvimi>> GetPiyasaTakvimiList(DateTime baslangicTarihi, int gunSayisi, short tarihSayisi)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("DateBegin", baslangicTarihi),
            new SqlParameter("DayCount", gunSayisi),
            new SqlParameter("CountDate", tarihSayisi)
        };

        return await _matriksDbContext.ExecuteStoredProcedureAsync<PiyasaTakvimi>("bp.pGetPiyasaTakvimi", parameters.ToArray());
    }

    public async Task<bool> InsertOrUpdatePiyasaTakvimi(PiyasaTakvimi piyasaTakvimi)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("NewsId", piyasaTakvimi.NewsId),
            new SqlParameter("Country", piyasaTakvimi.Country),
            new SqlParameter("Indicator", piyasaTakvimi.Indicator),
            new SqlParameter("Per", piyasaTakvimi.Per),
            new SqlParameter("Priority", piyasaTakvimi.Priority),
            new SqlParameter("Actual", piyasaTakvimi.Actual),
            new SqlParameter("Consensus", piyasaTakvimi.Consensus),
            new SqlParameter("Previous", piyasaTakvimi.Previous),
            new SqlParameter("NewsDate", piyasaTakvimi.NewsDate)
        };

        var result = await _matriksDbContext.ExecuteNonQueryAsync("[bp].[PiyasaTakvimiEkleGuncelle]", parameters.ToArray());

        return result > 0;
    }

    public async Task<int> GetTotalPiyasaTakvimiCount()
    {
        //TODO : check
        return await _matriksDbContext.ExecuteNonQueryAsync("pGetTotalPiyasaTakvimiCount");
    }

    public async Task<List<PiyasaTakvimi>> GetPiyasaTakvimiByPaging(int intervalStart, int intervalEnd)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("IntervalStart",intervalStart),
            new SqlParameter("IntervalEnd",intervalEnd)
        };

        return await _matriksDbContext.ExecuteStoredProcedureAsync<PiyasaTakvimi>("GetPiyasaTakvimiByPaging", parameters.ToArray());
    }

    public async Task<PiyasaTakvimi> GetPiyasaTakvimiById(int id)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("Id",id)
        };

        return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<PiyasaTakvimi>("[bp].[pGetPiyasaTakvimiById]", parameters.ToArray());
    }

    public async Task DeletePiyasaTakvimi(int Id)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("Id",Id)
        };

        await _matriksDbContext.ExecuteNonQueryAsync("[bp].[pDeletePiyasaTakvimi]", parameters.ToArray());
    }
}