﻿using Bigpara.Application.Features.News.Commands;
using Bigpara.Application.Features.News.Queries;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Bigpara.Service.Api.Controllers
{
    [Route("api/news")]
    [ApiController]
    public class NewsController : ControllerBase
    {
        private readonly ILogger<NewsController> _logger;
        private readonly IMediator _mediator;
        public NewsController(ILogger<NewsController> logger, IMediator mediator)
        {
            _logger = logger;
            _mediator = mediator;
        }
        [HttpGet("search")]
        public async Task<IActionResult> Filter([FromQuery]GetNewsKeywordSearchQuery query)
        {
            var result=  await _mediator.Send(query);
             
            return Ok(result);

        }

        [HttpGet("trends")]
        public async Task<IActionResult> Trends([FromQuery] GetNewsMostReadingQuery query)
        {
            var result = await _mediator.Send(query);

            return Ok(result);

        }

        [HttpPost("read")]
        public async Task<IActionResult> UpdateRead([FromBody] AddNewsReadCommand command)
        {

            var result= await _mediator.Send(command);
            return Ok(result);
        }


    }
}
