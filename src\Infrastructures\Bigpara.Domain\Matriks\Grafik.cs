﻿using System.Text.Json.Serialization;

namespace Bigpara.Domain.Matriks
{
    public class Grafik
    {
        [Json<PERSON>ropertyName("Symbol")]
        public string SEMBOL { get; set; }
        public int SEMBOLID { get; set; }
        [JsonPropertyName("Date")]
        public System.DateTime TARIH { get; set; }
        [Json<PERSON>ropertyName("Open")]
        public Nullable<double> ACILIS { get; set; }
        [Json<PERSON>ropertyName("High")]
        public Nullable<double> YUKSEK { get; set; }
        [Json<PERSON>ropertyName("Low")]
        public Nullable<double> DUSUK { get; set; }
        [JsonPropertyName("Close")]
        public Nullable<double> KAPANIS { get; set; }
        [J<PERSON><PERSON>ropertyName("TotalVolume")]
        public Nullable<decimal> HACIMLOT { get; set; }
        [Json<PERSON>ropertyName("VWap")]
        public Nullable<double> AORT { get; set; }
        
    }
}
