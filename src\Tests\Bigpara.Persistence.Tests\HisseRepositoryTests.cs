using Bigpara.Domain.Bigpara;
using Bigpara.Domain.Enums;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.Definitions;
using Bigpara.Domain.Matriks.StoredProcedureResults;
using Bigpara.Domain.SummaryTypes;
using Bigpara.Persistence.Matriks.Borsa;
using Microsoft.Data.SqlClient;
using Moq;

namespace Bigpara.Persistence.Tests.Matriks.Borsa
{
    public class HisseRepositoryTests
    {
        private readonly Mock<IMatriksDbContext> _dbContextMock;
        private readonly HisseRepository _repository;

        public HisseRepositoryTests()
        {
            _dbContextMock = new Mock<IMatriksDbContext>();
            _repository = new HisseRepository(_dbContextMock.Object);
        }

        [Fact]
        public async Task GetYuzeyselBySembolId_ThrowsNotImplementedException()
        {
            await Assert.ThrowsAsync<NotImplementedException>(() => _repository.GetYuzeyselBySembolId(1));
        }

        [Fact]
        public async Task GetYuzeysellerList_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<YuzeyselOpeation>(It.IsAny<string>()))
                .ReturnsAsync(new List<YuzeyselOpeation> { new YuzeyselOpeation() });

            var result = await _repository.GetYuzeysellerList();
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetHisseDurumByType_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<SembolOzet>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<SembolOzet> { new SembolOzet() });

            var result = await _repository.GetHisseDurumByType(ShareProcessStatusType.MostIncreased);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetHisseDurumByType_WithSeansTypes_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<SembolOzet>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<SembolOzet> { new SembolOzet() });

            var result = await _repository.GetHisseDurumByType(ShareProcessStatusType.MostIncreased, SeansTypes.Gun);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetAgirlikliOrtalamaDurum_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<SembolOzet>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<SembolOzet> { new SembolOzet() });

            var result = await _repository.GetAgirlikliOrtalamaDurum("OrderBy", 1, 10);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetAgirlikliOrtalamaDurumPaging_ReturnsPagingResult()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<SembolOzet>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<SembolOzet> { new SembolOzet() });

            var result = await _repository.GetAgirlikliOrtalamaDurumPaging("OrderBy", 1, 10);
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task GetAgirlikliOrtalamaDurumV2_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<SembolOzet>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<SembolOzet> { new SembolOzet() });

            var result = await _repository.GetAgirlikliOrtalamaDurumV2("OrderBy");
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetHisseYuzeysel_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetHisseYuzeysel();
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetHisseYuzeyselDegerleriBySembolId_ReturnsItem()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureSingleAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new HisseYuzeysel());

            var result = await _repository.GetHisseYuzeyselDegerleriBySembolId(1);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetHisseYuzeyselOnline_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeyselOnline>(It.IsAny<string>()))
                .ReturnsAsync(new List<HisseYuzeyselOnline> { new HisseYuzeyselOnline() });

            var result = await _repository.GetHisseYuzeyselOnline();
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetHisseYuzeyselTarihsiz_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeyselIndikator>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeyselIndikator> { new HisseYuzeyselIndikator() });

            var result = await _repository.GetHisseYuzeyselTarihsiz("X");
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetHisseYuzeyselTarihsizBySembolId_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetHisseYuzeyselTarihsizBySembolId(1);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetHisseList_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<Hisse>(It.IsAny<string>()))
                .ReturnsAsync(new List<Hisse> { new Hisse() });

            var result = await _repository.GetHisseList();
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetTumSemboller_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<Hisse>(It.IsAny<string>()))
                .ReturnsAsync(new List<Hisse> { new Hisse() });

            var result = await _repository.GetTumSemboller();
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetDunyaBorsalariData_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<DunyaBorsalari>(It.IsAny<string>()))
                .ReturnsAsync(new List<DunyaBorsalari> { new DunyaBorsalari() });

            var result = await _repository.GetDunyaBorsalariData();
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetBilancoDonems_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<BilancoDonem>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<BilancoDonem> { new BilancoDonem() });

            var result = await _repository.GetBilancoDonems("X", "2023", 1);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetBilancoDonemByYil_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<BilancoDonem>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<BilancoDonem> { new BilancoDonem() });

            var result = await _repository.GetBilancoDonemByYil("X", "2023", 1, 2023);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetBilancoDonemsBySembol_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<BilancoDonemSembol>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<BilancoDonemSembol> { new BilancoDonemSembol() });

            var result = await _repository.GetBilancoDonemsBySembol("X");
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetEndeksHisseYuzeysels_ReturnsPagingResult()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetEndeksHisseYuzeysels("X", "Y", 1, 10, 'X');
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task GetEndeksHisseYuzeysels_OnlyEndeks_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetEndeksHisseYuzeysels("X");
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetHisseFiyatlariByEndeksVeHarf_ReturnsPagingResult()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetHisseFiyatlariByEndeksVeHarf("X", 1, 10, null);
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task GetHisseFiyatlariByEndeksVeHarfOnline_ReturnsPagingResult()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetHisseFiyatlariByEndeksVeHarfOnline("X", 1, 10, null);
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task GetPerformansAnaliz_Paging_ReturnsPagingResult()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<PerformansAnaliz>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<PerformansAnaliz> { new PerformansAnaliz() });

            var result = await _repository.GetPerformansAnaliz("X", 1, DateTime.Now, DateTime.Now, "TRY", 1, 1, 10);
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task GetPerformansAnaliz_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<PerformansAnaliz>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<PerformansAnaliz> { new PerformansAnaliz() });

            var result = await _repository.GetPerformansAnaliz("X", 1, DateTime.Now, DateTime.Now, "TRY", 1);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetUserHisseYuzeysel_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetUserHisseYuzeysel(1);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetUserHisseYuzeyselOnline_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetUserHisseYuzeyselOnline(1);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetDefaultPiyasaBandi_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<KisaTanimSemboller>(It.IsAny<string>()))
                .ReturnsAsync(new List<KisaTanimSemboller> { new KisaTanimSemboller() });

            var result = await _repository.GetDefaultPiyasaBandi();
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetUserHisseSembolList_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<KisaTanimSemboller>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<KisaTanimSemboller> { new KisaTanimSemboller() });

            var result = await _repository.GetUserHisseSembolList(1, 0);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetUserHisseHaberleri_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<Haberler>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<Haberler> { new Haberler() });

            var result = await _repository.GetUserHisseHaberleri(1);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetUserSembolHaberleri_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<UserHisseHaber>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<UserHisseHaber> { new UserHisseHaber() });

            var result = await _repository.GetUserSembolHaberleri(1);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetUserHisseList_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<UserSembol>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<UserSembol> { new UserSembol() });

            var result = await _repository.GetUserHisseList(1);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetYuzeselOnlineBySembol_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<LiveStockHisse>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<LiveStockHisse> { new LiveStockHisse() });

            var result = await _repository.GetYuzeselOnlineBySembol(DateTime.Now);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetHisselerIslemHacmiArtanAzalan_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetHisselerIslemHacmiArtanAzalan(OrderByDirection.Asc);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task SaveUserHisse_ReturnsInt()
        {
            _dbContextMock.Setup(x => x.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(1);

            var result = await _repository.SaveUserHisse(1, 1, 1, 1);
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task SaveUserHisse_WithOrderId_ReturnsInt()
        {
            _dbContextMock.Setup(x => x.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(1);

            var result = await _repository.SaveUserHisse(1, 1, 1, 1, 1);
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task GetHisselerIslemHacmiFiyatArtanAzalan_ReturnsPagingResult()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetHisselerIslemHacmiFiyatArtanAzalan(TradingVolumeFilterType.VolumeUpPriceDown, 1, 10);
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task GetHisselerIslemHacmiFiyatArtanAzalan_OnlyType_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<HisseYuzeysel>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<HisseYuzeysel> { new HisseYuzeysel() });

            var result = await _repository.GetHisselerIslemHacmiFiyatArtanAzalan(TradingVolumeFilterType.VolumeUpPriceDown);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetPiyasaBandiHisseListe_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<KisaTanimSemboller>(It.IsAny<string>()))
                .ReturnsAsync(new List<KisaTanimSemboller> { new KisaTanimSemboller() });

            var result = await _repository.GetPiyasaBandiHisseListe();
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task SaveUserAlert_ReturnsInt()
        {
            _dbContextMock.Setup(x => x.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(1);

            var result = await _repository.SaveUserAlert(1, "X", 1.0, "field");
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task GetAgirlikliOrtalama_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<SembolOzet>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<SembolOzet> { new SembolOzet() });

            var result = await _repository.GetAgirlikliOrtalama("OrderBy", 1, 10);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task DeleteUserAlert_ReturnsInt()
        {
            _dbContextMock.Setup(x => x.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(1);

            var result = await _repository.DeleteUserAlert(1, 1);
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task GetUserAlerts_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<UserAlert>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<UserAlert> { new UserAlert() });

            var result = await _repository.GetUserAlerts(1);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetUserAlertResult_ReturnsList()
        {
            _dbContextMock.Setup(x => x.ExecuteStoredProcedureAsync<UserAlert>(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<UserAlert> { new UserAlert() });

            var result = await _repository.GetUserAlertResult(1);
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task UserAlertDeactivate_ReturnsInt()
        {
            _dbContextMock.Setup(x => x.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(1);

            var result = await _repository.UserAlertDeactivate(1, 1);
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task UserAlertInstanceAlert_ReturnsInt()
        {
            _dbContextMock.Setup(x => x.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(1);

            var result = await _repository.UserAlertInstanceAlert(1);
            Assert.Equal(1, result);
        }
    }
}
