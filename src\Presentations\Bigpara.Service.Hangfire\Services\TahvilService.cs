﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Service.Hangfire.Infrastructure.Data;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class TahvilService : ITahvilService
{
    private readonly ILogger<TahvilService> _logger;
    private readonly IStoredProcedureParameterService _storedProcedureService;
    private readonly TimeZoneInfo _turkeyTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");

    public TahvilService
    (
        IStoredProcedureParameterService storedProcedureService,
        ILogger<TahvilService> logger
    )
    {
        _storedProcedureService = storedProcedureService;
        _logger = logger;
    }

    public async Task ChangeTahvilKesinOzet(TahvilDto tahvilDto)
    {
        try
        {
            var tahvilKesinOzetParameters = _storedProcedureService.CreateTahvilKesinOzetStoredProcedureParameters(tahvilDto);
            if (tahvilKesinOzetParameters != null)
                await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_TahvilKesinOzet", tahvilKesinOzetParameters, tahvilDto?.StrMat);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {tahvilDto.StrMat}");
        }
    }

    public async Task ChangeTahvilRepoOzet(TahvilDto tahvilDto)
    {
        try
        {
            tahvilDto.DtmTarih = TimeZoneInfo.ConvertTimeFromUtc(
           DateTimeOffset.FromUnixTimeMilliseconds(tahvilDto.UnixDate).UtcDateTime,
           _turkeyTimeZone);

            var tahvilRepoOzetParameters = _storedProcedureService.CreateTahvilRepoOzetStoredProcedureParameters(tahvilDto);
            if (tahvilRepoOzetParameters != null)
                await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_TahvilRepoOzet", tahvilRepoOzetParameters, tahvilDto?.StrMat);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {tahvilDto.StrMat}");
        }
    }
}