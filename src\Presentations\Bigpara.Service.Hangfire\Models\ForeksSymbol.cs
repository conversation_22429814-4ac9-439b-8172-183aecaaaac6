﻿using System.Text.Json.Serialization;

namespace Bigpara.Jobs.Hangfire.Models
{
    public class ForeksSymbol
    {
        public ForeksSymbol()
        {
            tag = new List<string>();
        }
        [JsonPropertyName("_id")]
        public string _Id { get; set; }

        [JsonPropertyName("code")]
        public string Code { get; set; }

        [Json<PERSON>ropertyName("securityDesc")]
        public string Name  { get; set; }

        [JsonPropertyName("security")]
        public string Security { get; set; }

        [JsonPropertyName("securityType")]
        public string SecurityType { get; set; }

        [JsonPropertyName("tag")]
        public List<string> tag { get; set; }
    }
}
