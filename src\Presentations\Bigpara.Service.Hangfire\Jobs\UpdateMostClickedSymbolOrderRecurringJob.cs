﻿using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Redis;
using Bigpara.Notifications;

namespace Bigpara.Service.Hangfire.Jobs;

public class UpdateMostClickedSymbolOrderRecurringJob : IRecurringJob
{
    private readonly IRedisCacheService _redisCacheService;
    private readonly ILogger<UpdateMostClickedSymbolOrderRecurringJob> _logger;
    private readonly INotificationService _notificationService;


    public UpdateMostClickedSymbolOrderRecurringJob
    (
        IRedisCacheService redisCacheService,
        ILogger<UpdateMostClickedSymbolOrderRecurringJob> logger,
        INotificationService notificationService
    )
    {
        _redisCacheService = redisCacheService;
        _logger = logger;
        _notificationService = notificationService;
    }

    public string Name => "UpdateMostClickedSymbolOrderRecurringJob";
    public IEnumerable<string> Crons => ["30 */5 * * *"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("UpdateMostClickedSymbolOrderRecurringJob - Başladı ");
            await ProcessUpdateMostClickedSymbolOrder();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    public async Task ProcessUpdateMostClickedSymbolOrder()
    {
        try
        {
            IList<RedisReadCount> redisDataList = _redisCacheService.GetTypedOrderList<RedisReadCount>(CacheKeys.GetMostClickedStockKey, c => c.Daily, OrderByDirections.Desc);
            if (redisDataList != null && redisDataList.Any())
            {
                for (int index = 0; index < redisDataList.Count; index++)
                {
                    RedisReadCount redisReadCount = redisDataList[index];
                    redisReadCount.Order = (index + 1);

                    _redisCacheService.Update(CacheKeys.GetMostClickedStockKey, redisReadCount, c => c.Id == redisReadCount.Id);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            await _notificationService.NotifyErrorAsync("En Çok Tıklanan Sembol Sıralama", ex.Message);

            //TODO : Check
            //EmailHelper.SendEmail("En Çok Tıklanan Sembol Sıralama", ex.Message);
        }
    }
}
