﻿using System;
using System.Collections.Generic;
using System.Data.Services.Client;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.External.Quark.Infrastructure
{
    public interface IQuarkClient
    {
        DataServiceQuery<T> CreateQuery<T>(string entitySetName) where T : class;
        Task<T> GetAsync<T>(IQueryable query, CancellationToken cancelToken = default, string extraQuery = null);
        T Get<T>(IQueryable query, string extraQuery = null);
        Task<T> GetAsync<T>(Uri uri, CancellationToken cancelToken = default);
        T Get<T>(Uri uri);
        Task<T> PostAsync<T>(Uri uri, T value, CancellationToken cancelToken = default);
        Task<TOut> PostAsync<T, TOut>(Uri uri, T value, CancellationToken cancelToken = default, string dataServiceUrl = null);
        Task<T> PostAsStringAsync<T>(Uri uri, T value, CancellationToken cancelToken = default);
        Task<TOut> PostDataAsync<TOut>(Uri uri, MultipartFormDataContent values, CancellationToken cancelToken = default);
        Task<TOut> PostDataAsync<TOut>(Uri uri, Stream value, string fileName, string contentType, CancellationToken cancelToken = default);
        Task<T> PutAsync<T>(Uri uri, T value, CancellationToken cancelToken = default) where T : class;
        Task DeleteAsync(Uri uri, CancellationToken cancelToken = default);
        Task<TOut> PostDataAsyncWithWait<TOut>(Uri uri, Stream value, string fileName, string contentType, CancellationToken cancelToken = default);
        Task<TOut> PostAsyncWithWait<T, TOut>(Uri uri, T value);
        Task<TOut> PostAsyncWithWait<T, TOut>(Uri uri, T value, CancellationToken cancelToken = default);
        Task<T> PutAsyncWithWait<T>(Uri uri, T value, CancellationToken cancelToken = default) where T : class;
        Task DeleteAsyncWithWait(Uri uri, CancellationToken cancelToken = default);
    }
}
