﻿using Bigpara.Application.Contracts.Repositories.Bitcoin;
using Bigpara.Domain.Bitcoin;
using Bigpara.Domain.ExtendedTypes;
using Microsoft.Data.SqlClient;

namespace Bigpara.Persistence.Bitcoin;

public class BitcoinRepository : IBitcoinRepository
{
    private readonly IBigparaDbContext _bigparaDbContext;

    public BitcoinRepository(IBigparaDbContext bigparaDbContext)
    {
        _bigparaDbContext = bigparaDbContext;
    }

    public async Task<List<MarketsOnlineDetails>> GetAllBitcoinHistory()
    {
        return await _bigparaDbContext.ExecuteStoredProcedureAsync<MarketsOnlineDetails>("[btc].[spGetAllBitcoinHistory]");
    }

    public async Task<List<MarketsHistory>> GetMarketsOnlinesBySymbol(string symbol)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("Symbol", symbol)
        };

        return await _bigparaDbContext.ExecuteStoredProcedureAsync<MarketsHistory>("[btc].[spGetBitcoinHistoryBySymbol]", parameters.ToArray());
    }

    public async Task<List<MarketsOnline>> GetMarketsOnlineLastDay(string symbol)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("Symbol", symbol)
        };

        return await _bigparaDbContext.ExecuteStoredProcedureAsync<MarketsOnline>("[btc].[spGetMarketsOnlineBySymbol]", parameters.ToArray());
    }

    public async Task<MarketsOnline> GetMarketsOnlineBySymbolAndDate(string symbol, DateTime selectedDate)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("Symbol", symbol),
            new SqlParameter("SelectedDate", selectedDate)
        };

        return await _bigparaDbContext.ExecuteStoredProcedureSingleAsync<MarketsOnline>("btc.spGetMarketsOnlineDataBySymbolAndDate", parameters.ToArray());
    }

    public async Task<int> AddMarketsOnline(MarketsOnline marketsOnline)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("Symbol",marketsOnline.Symbol),
            new SqlParameter("DateTime",marketsOnline.DateTime),
            new SqlParameter("HighValue",marketsOnline.HighValue),
            new SqlParameter("LowValue",marketsOnline.LowValue),
            new SqlParameter("CloseValue",marketsOnline.CloseValue),
            new SqlParameter("AvgValue",marketsOnline.AvgValue),
            new SqlParameter("BidValue",marketsOnline.BidValue),
            new SqlParameter("AskValue",marketsOnline.AskValue),
            new SqlParameter("VolumeValue",marketsOnline.VolumeValue),
            new SqlParameter("Currency",marketsOnline.Currency),
            new SqlParameter("CurrencyVolume",marketsOnline.CurrencyVolume),
            new SqlParameter("UpdatedDateTime",marketsOnline.UpdatedDateTime)
        };

        return await _bigparaDbContext.ExecuteNonQueryAsync("btc.spMarketsOnlineCreate", parameters.ToArray());
    }

    public async Task<int> UpdateMarketsOnline(MarketsOnline marketsOnline)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("Symbol",marketsOnline.Symbol),
            new SqlParameter("DateTime",marketsOnline.DateTime),
            new SqlParameter("HighValue",marketsOnline.HighValue),
            new SqlParameter("LowValue",marketsOnline.LowValue),
            new SqlParameter("CloseValue",marketsOnline.CloseValue),
            new SqlParameter("AvgValue",marketsOnline.AvgValue),
            new SqlParameter("BidValue",marketsOnline.BidValue),
            new SqlParameter("AskValue",marketsOnline.AskValue),
            new SqlParameter("VolumeValue",marketsOnline.VolumeValue),
            new SqlParameter("Currency",marketsOnline.Currency),
            new SqlParameter("CurrencyVolume",marketsOnline.CurrencyVolume),
            new SqlParameter("UpdatedDateTime",marketsOnline.UpdatedDateTime)
        };

        return await _bigparaDbContext.ExecuteNonQueryAsync("btc.spMarketsOnlineUpdate", parameters.ToArray());
    }


    public async Task<List<BitfinexOnline>> GetAllBitfinexOnlines()
    {
        return await _bigparaDbContext.ExecuteStoredProcedureAsync<BitfinexOnline>("[btc].[spGetBitcoinBitfinexAll]");
    }

    public async Task<List<BitfinexOnline>> GetBitfinexOnlineLastValueBySymbol(string symbol)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("Symbol", symbol)
        };

        return await _bigparaDbContext.ExecuteStoredProcedureAsync<BitfinexOnline>("[btc].[spGetBitcoinBitfinexBySymbol]", parameters.ToArray());
    }

    public async Task<int> AddBitfinexOnline(BitfinexOnline bitfinexOnline)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("Symbol",bitfinexOnline.Symbol),
            new SqlParameter("Ticket",bitfinexOnline.Ticket),
            new SqlParameter("Name",bitfinexOnline.Name),
            new SqlParameter("BId",bitfinexOnline.BId),
            new SqlParameter("DailyChangePerc",bitfinexOnline.DailyChangePerc),
            new SqlParameter("InsertDate",bitfinexOnline.InsertDate),
            new SqlParameter("SymbolOrder",bitfinexOnline.SymbolOrder)
        };

        return await _bigparaDbContext.ExecuteNonQueryAsync("btc.spBitfinexOnlineCreate", parameters.ToArray());
    }
}
