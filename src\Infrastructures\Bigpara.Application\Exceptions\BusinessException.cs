namespace Bigpara.Application.Exceptions;

public class BusinessException : Exception
{
    public string Code { get; }
    public object Details { get; }

    public BusinessException(string message) : base(message)
    {
        Code = "BUSINESS_ERROR";
    }

    public BusinessException(string code, string message) : base(message)
    {
        Code = code;
    }

    public BusinessException(string code, string message, object details) : base(message)
    {
        Code = code;
        Details = details;
    }

    public BusinessException(string message, Exception innerException) : base(message, innerException)
    {
        Code = "BUSINESS_ERROR";
    }
}





