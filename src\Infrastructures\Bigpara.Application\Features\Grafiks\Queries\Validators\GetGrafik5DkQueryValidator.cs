﻿using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Features.Grafiks.Queries.Validators
{
    public class GetGrafik5DkQueryValidator: AbstractValidator<GetGrafik5DkQuery>
    {
        public GetGrafik5DkQueryValidator()
        {
             RuleFor(x => x.Symbols)
                .NotEmpty().WithMessage("Symbols cannot be empty.")
                .Matches(@"^[a-zA-Z0-9,]+$").WithMessage("Symbols must be alphanumeric and separated by commas.");
        }
    }
}
