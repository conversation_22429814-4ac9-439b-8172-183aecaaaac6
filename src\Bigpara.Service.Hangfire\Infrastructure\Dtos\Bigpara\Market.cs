﻿using Newtonsoft.Json;

namespace Bigpara.Service.Hangfire.Infrastructure.Dtos.Bigpara;

public class Market
{
    [JsonProperty("high")]
    public double? High { get; set; }

    [JsonProperty("latest_trade")]
    public string LatestTrade { get; set; }

    [JsonProperty("bid")]
    public double? Bid { get; set; }

    [JsonProperty("volume")]
    public double? Volume { get; set; }

    [JsonProperty("currency")]
    public string Currency { get; set; }

    [JsonProperty("currency_volume")]
    public double? CurrencyVolume { get; set; }

    [JsonProperty("ask")]
    public double? Ask { get; set; }

    [JsonProperty("close")]
    public double? Close { get; set; }

    [JsonProperty("avg")]
    public double? AVG { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("low")]
    public double? Low { get; set; }
}
