﻿@model List<ForeksSymbol>

<h1>Realtime Trade Verisi</h1>
<table id="tradeTable">
    <thead>
        <tr>
            <th>Sembol</th>
            <th>Code</th>
            <th>Tanım</th>
            <th><PERSON><PERSON>ş</th>
            <th><PERSON><PERSON><PERSON>ş</th>
            <th>Oran</th>
            <th>Oran2</th>
            <th>Yön</th>
            <th>Tarih</th>
        </tr>
    </thead>
    <tbody id="tradeBody">
       @foreach (var item in Model)
        {
            <tr data-id="@item._Id">
                <td>@item._Id</td>
                <td>@item.Code</td>
                <td>@item.Name</td>
                <td data-val="a">-</td>
                <td data-val="b">-</td>
                <td data-val="C">-</td>
                <td data-val="c">-</td>
                <td data-val="d">-</td>
                <td data-val="dt">-</td>
            </tr>
        }
    </tbody>
</table>
