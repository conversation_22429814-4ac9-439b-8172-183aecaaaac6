﻿using Microsoft.AspNetCore.SignalR;

namespace Bigpara.Service.Realtime.Hubs;


public class TradeHub : Hub
{
    private readonly ILogger<TradeHub> _logger;

    public TradeHub(ILogger<TradeHub> logger)
    {
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation($"Client bağlandı: {Context.ConnectionId}");
        await base.OnConnectedAsync();
    }

    public async Task SubscribeToSymbols(List<string> symbols)
    {
        foreach (var item in symbols)
        {
            await SubscribeToSymbol(item);
        }
    }

    public async Task SubscribeToSymbol(string symbol)
    {
        if (string.IsNullOrEmpty(symbol)) return;

        symbol = symbol.ToUpper();
        var groupName = $"symbol_chart_{symbol}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

        _logger.LogDebug($"Client {Context.ConnectionId} subscribed to {symbol}");
    }

    public async Task SubscribeToSymbolDetail(string symbol)
    {
        if (string.IsNullOrEmpty(symbol)) return;

        symbol = symbol.ToUpper();
        var groupName = $"symbol_detail_{symbol}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

        _logger.LogDebug($"Client {Context.ConnectionId} subscribed to detail {symbol}");
    }

    public async Task SubscribeAll()
    {
        var groupName = "symbol_table_all";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

        _logger.LogDebug($"Client {Context.ConnectionId} subscribed to all symbols");
    }


    public async Task UnsubscribeFromSymbol(string symbol)
    {
        if (string.IsNullOrEmpty(symbol)) return;

        symbol = symbol.ToUpper();
        try
        {
            var groupName = $"symbol_chart_{symbol}";
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            _logger.LogDebug($"Client {Context.ConnectionId} unsubscribed from {symbol}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error unsubscribing from {symbol} for {Context.ConnectionId}");
        }
    }

    public async Task UnsubscribeFromSymbolDetail(string symbol)
    {
        if (string.IsNullOrEmpty(symbol)) return;

        symbol = symbol.ToUpper();
        try
        {
            var groupName = $"symbol_detail_{symbol}";
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            _logger.LogDebug($"Client {Context.ConnectionId} unsubscribed from detail {symbol}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error unsubscribing from detail {symbol} for {Context.ConnectionId}");
        }
    }

    public override async Task OnDisconnectedAsync(Exception exception)
    {
        _logger.LogInformation($"Client ayrıldı: {Context.ConnectionId}");
        await base.OnDisconnectedAsync(exception);
    }
}


