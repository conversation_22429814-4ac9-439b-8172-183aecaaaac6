using Bigpara.Domain.Bitcoin;
using Bigpara.Domain.ExtendedTypes;

namespace Bigpara.Application.Contracts.Repositories.Bitcoin;

public interface IBitcoinRepository
{
    Task<List<MarketsOnlineDetails>> GetAllBitcoinHistory();
    Task<List<MarketsHistory>> GetMarketsOnlinesBySymbol(string symbol);
    Task<MarketsOnline> GetMarketsOnlineBySymbolAndDate(string symbol, DateTime selectedDate);
    Task<List<MarketsOnline>> GetMarketsOnlineLastDay(string symbol);
    Task<int> AddMarketsOnline(MarketsOnline marketsOnline);
    Task<int> UpdateMarketsOnline(MarketsOnline marketsOnline);
    Task<List<BitfinexOnline>> GetAllBitfinexOnlines();
    Task<List<BitfinexOnline>> GetBitfinexOnlineLastValueBySymbol(string symbol);
    Task<int> AddBitfinexOnline(BitfinexOnline bitfinexOnline);
}