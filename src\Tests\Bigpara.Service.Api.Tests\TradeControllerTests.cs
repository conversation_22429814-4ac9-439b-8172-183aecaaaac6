using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Bigpara.Cache.Interfaces;
using Bigpara.Application.Features.Grafiks.Queries;
using Bigpara.Application.Features.Grafiks.Queries.ViewModels;
using Bigpara.Application.Features.Yuzeysels.Queries;
using Bigpara.Application.Features.Yuzeysels.ViewModels;
using Bigpara.Service.Api.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Bigpara.Service.Api.Tests.Controllers
{
    public class TradeControllerTests
    {
        private readonly Mock<ILogger<TradeController>> _loggerMock;
        private readonly Mock<ICacheService> _cacheServiceMock;
        private readonly Mock<IMediator> _mediatorMock;
        private readonly TradeController _controller;

        public TradeControllerTests()
        {
            _loggerMock = new Mock<ILogger<TradeController>>();
            _cacheServiceMock = new Mock<ICacheService>();
            _mediatorMock = new Mock<IMediator>();
            _controller = new TradeController(_loggerMock.Object, _cacheServiceMock.Object, _mediatorMock.Object);
        }

        [Fact]
        public async Task GetStocks_Current_ReturnsOk()
        {
            // Arrange
            var query = new GetGrafik5DkQuery { Symbols = "THYAO" , PerCount = 5 };
            var expected = new Grafik5DkViewModel();
            _mediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>())).ReturnsAsync(expected);

            // Act
            var result = await _controller.GetStocks(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expected, okResult.Value);
        }

        [Fact]
        public async Task GetStocks_Daily_ReturnsOk()
        {
            // Arrange
            var query = new GetGrafikGunlukQuery { Symbols = new List<string> { "XYZ" }, PerCount = 10, BackDay = 2 };
            var expected = new GrafikGunlukViewModel();
            _mediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>())).ReturnsAsync(expected);

            // Act
            var result = await _controller.GetStocks(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expected, okResult.Value);
        }

        [Fact]
        public async Task GetCurrencies_Table_ReturnsOk()
        {
            // Arrange
            var query = new GetGrafik5DkQuery { Symbols =  "USDTRY", PerCount = 3 };
            var expected = new Grafik5DkViewModel();
            _mediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>())).ReturnsAsync(expected);

            // Act
            var result = await _controller.GetCurrencies(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expected, okResult.Value);
        }

        [Fact]
        public async Task GetPerformance_ReturnsOk()
        {
            // Arrange
            var query = new GetYuzeyselPerformanceQuery { Symbols = "ABC,XYZ" };
            var expected = new YuzeyselPerformanceResponseModel();
            _mediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>())).ReturnsAsync(expected);

            // Act
            var result = await _controller.GetPerformance(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expected, okResult.Value);
        }
    }
}
