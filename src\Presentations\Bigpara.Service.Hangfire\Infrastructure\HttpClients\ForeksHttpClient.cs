﻿using Bigpara.Notifications;
using Newtonsoft.Json;
using System.Text;

namespace Bigpara.Service.Hangfire.Infrastructure.HttpClients;

public class ForeksHttpClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ForeksHttpClient> _logger;
    private readonly string _username;
    private readonly string _password;
    private readonly INotificationService _notificationService;

    public ForeksHttpClient(
        IHttpClientFactory httpClientFactory,
        ILogger<ForeksHttpClient> logger,
        IConfiguration configuration,
        INotificationService notificationService)
    {
        _httpClient = httpClientFactory.CreateClient("ForeksFeedService");
        _logger = logger;
        _username = configuration["Foreks:Auth:SnapShotDelayedUsername"];
        _password = configuration["Foreks:Auth:SnapShotDelayedPassword"];
        SetBasicAuthentication();
        _notificationService = notificationService;
    }

    private void SetBasicAuthentication()
    {
        var credentials = $"{_username}:{_password}";
        var encodedCredentials = Convert.ToBase64String(Encoding.UTF8.GetBytes(credentials));
        _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", encodedCredentials);
    }

    public async Task<List<T>> FetchDataAsync<T>(string url)
    {
        try
        {
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<T>>(responseBody) ?? new List<T>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Veri çekilirken hata oluştu : {Url}", url);
            await _notificationService.NotifyErrorAsync("ForeksHttpClient", $"Veri çekilirken hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
            return new List<T>();
        }
    }
}