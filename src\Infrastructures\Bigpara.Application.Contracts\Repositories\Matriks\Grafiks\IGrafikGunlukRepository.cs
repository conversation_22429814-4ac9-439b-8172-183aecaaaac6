﻿using Bigpara.Domain;
using Bigpara.Domain.Matriks;

namespace Bigpara.Application.Contracts.Repositories.Matriks.Grafiks;

public interface IGrafikRepository
{
    Task<List<GrafikGunluk>> GetTopGrafikGunluks(int top);
    
    Task<List<GrafikGunluk>> GetGrafikGunluksBySembolId(int sembolId, int top);
    Task<List<Grafik5Dk>> GetGrafik5DkBySembolIds(string Ids, int perIdRecordCount);

    Task<List<GrafikGunluk>> GetGrafikGunlukBySembolIds(string Ids, int perIdRecordCount, int daysBack = 30);
}
