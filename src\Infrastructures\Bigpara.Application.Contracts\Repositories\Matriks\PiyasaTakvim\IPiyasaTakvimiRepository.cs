﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Application.Contracts.Repositories.Matriks.PiyasaTakvim;

public interface IPiyasaTakvimiRepository
{
    Task<List<PiyasaTakvimi>> GetPiyasaTakvimiTopData(int topCount);
    Task<List<PiyasaTakvimi>> GetPiyasaTakvimiList(DateTime baslang<PERSON><PERSON>hi, int gunSayisi, short tarihSayisi);
    Task<bool> InsertOrUpdatePiyasaTakvimi(PiyasaTakvimi piyasaTakvimi);
    Task<int> GetTotalPiyasaTakvimiCount();
    Task<List<PiyasaTakvimi>> GetPiyasaTakvimiByPaging(int intervalStart, int intervalEnd);
    Task<PiyasaTakvimi> GetPiyasaTakvimiById(int id);
    Task DeletePiyasaTakvimi(int Id);
}