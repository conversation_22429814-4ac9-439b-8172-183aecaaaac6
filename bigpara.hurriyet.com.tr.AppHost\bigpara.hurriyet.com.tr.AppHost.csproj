﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>519060f0-cfe1-42a7-80f4-568a0040e5f5</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="8.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\src\Bigpara.Service.Api\Bigpara.Service.Api.csproj" />
    <ProjectReference Include="..\src\Bigpara.Service.Hangfire\Bigpara.Service.Hangfire.csproj" />
    <ProjectReference Include="..\src\Bigpara.Service.Realtime\Bigpara.Service.Realtime.csproj" />
  </ItemGroup>

</Project>
