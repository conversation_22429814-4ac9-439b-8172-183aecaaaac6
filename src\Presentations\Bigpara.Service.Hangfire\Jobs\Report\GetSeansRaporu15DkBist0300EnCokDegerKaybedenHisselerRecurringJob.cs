﻿using Bigpara.Domain.Matriks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Report;

public class GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisselerRecurringJob : IRecurringJob
{
    private readonly ISeansRaporlariService _seansRaporlariService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisselerRecurringJob> _logger;

    public GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisselerRecurringJob
    (
        ISeansRaporlariService seansRaporlariService,
        INotificationService notificationService,
        ILogger<GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisselerRecurringJob> logger
    )
    {
        _seansRaporlariService = seansRaporlariService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisselerRecurringJob";
     public IEnumerable<string> Crons => ["*/15 9-17 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisselerRecurringJob - Başladı ");
            await ProcessGetSeansRaporu15DkBist0300EnCokDegerKaybedenHisseler();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    /// <summary>
    /// 15 dk gun ici top 3 En cok deger kazanan bist 30 hisseleri aliniyor
    /// </summary>
    public async Task ProcessGetSeansRaporu15DkBist0300EnCokDegerKaybedenHisseler()
    {
        if (!_seansRaporlariService.IsActive())
            return;

        try
        {
            const string pattern = "En çok değer kaybeden BIST 30 hisse senetleri {0}.";
            var data = await _seansRaporlariService.GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisseler();

            var semboller = string.Join(",", data.Select(r => r.ACIKLAMA).ToList());

            var result = string.Format(pattern, semboller);

            var seansRaporuYuzeysel = data.FirstOrDefault();
            if (seansRaporuYuzeysel == null) return;
            var seansRaporu = new SeansRaporu
            {
                HisseCumle = result,
                Yon = -1,
                HisseAdi = string.Empty,
                EklenmeTarihi = DateTime.Now
            };

            await _seansRaporlariService.CreateAndCacheSeansRaporu(seansRaporu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            //EmailHelper.SendEmail("GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisseler", ex.Message);
            await _notificationService.NotifyErrorAsync($"GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisseler", ex.Message);
        }
    }
}
