﻿namespace Bigpara.Domain.Matriks.StoredProcedureResults;

public class HisseYuzeysel
{
    public int SEMBOLID { get; set; }
    public string SEMBOL { get; set; }
    public Nullable<System.DateTime> TARIH { get; set; }
    public int SEKTORID { get; set; }
    public Nullable<double> ALIS { get; set; }
    public Nullable<double> SATIS { get; set; }
    public Nullable<double> ACILIS { get; set; }
    public Nullable<double> YUKSEK { get; set; }
    public Nullable<double> YUKSEK1 { get; set; }
    public Nullable<double> YUKSEK2 { get; set; }
    public Nullable<double> DUSUK { get; set; }
    public Nullable<double> DUSUK1 { get; set; }
    public Nullable<double> DUSUK2 { get; set; }
    public Nullable<double> KAPANIS { get; set; }
    public Nullable<double> KAPANIS1 { get; set; }
    public Nullable<double> KAPANIS2 { get; set; }
    public Nullable<decimal> HACIMLOT { get; set; }
    public Nullable<decimal> HACIMLOT1 { get; set; }
    public Nullable<decimal> HACIMLOT2 { get; set; }
    public Nullable<double> AORT { get; set; }
    public Nullable<double> AORT1 { get; set; }
    public Nullable<double> AORT2 { get; set; }
    public Nullable<decimal> HACIMTLDUN { get; set; }
    public Nullable<decimal> HACIMYUZDEDEGISIM { get; set; }
    public Nullable<decimal> HACIMTL { get; set; }
    public Nullable<decimal> HACIMTL1 { get; set; }
    public Nullable<decimal> HACIMTL2 { get; set; }
    public Nullable<double> DUNKUKAPANIS { get; set; }
    public Nullable<double> ONCEKIKAPANIS { get; set; }
    public Nullable<double> IZAFIKAPANIS { get; set; }
    public Nullable<double> TAVAN { get; set; }
    public Nullable<double> TABAN { get; set; }
    public Nullable<double> YILYUKSEK { get; set; }
    public Nullable<double> YILDUSUK { get; set; }
    public Nullable<double> AYYUKSEK { get; set; }
    public Nullable<double> AYDUSUK { get; set; }
    public Nullable<double> HAFTAYUKSEK { get; set; }
    public Nullable<double> HAFTADUSUK { get; set; }
    public Nullable<double> ONCEKIYILKAPANIS { get; set; }
    public Nullable<double> ONCEKIAYKAPANIS { get; set; }
    public Nullable<double> ONCEKIHAFTAKAPANIS { get; set; }
    public Nullable<double> YILORTALAMA { get; set; }
    public Nullable<double> AYORTALAMA { get; set; }
    public Nullable<double> HAFTAORTALAMA { get; set; }
    public Nullable<double> YUZDEDEGISIMS1 { get; set; }
    public Nullable<double> YUZDEDEGISIMS2 { get; set; }
    public Nullable<double> YUZDEDEGISIM { get; set; }
    public Nullable<double> FIYATADIMI { get; set; }
    //public Nullable<double> LOTADET { get; set; }
    public Nullable<double> KAYKAR { get; set; }
    public Nullable<double> SERMAYE { get; set; }
    public Nullable<double> SAKLAMAOR { get; set; }
   
    public Nullable<double> NETKAR { get; set; }
    public Nullable<double> NET { get; set; }
 
    public Nullable<double> FIYATKAZ { get; set; }
    public Nullable<double> PIYDEG { get; set; }
    public Nullable<double> KAPANISFARK { get; set; }
    public string DONEM { get; set; }
    public Nullable<double> OZSERMAYE { get; set; }
    public Nullable<double> BETA { get; set; }
    public Nullable<double> XU100AG { get; set; }
    public string ACIKLAMA { get; set; }
}
public class HisseYuzeyselIndikator 
{
    public HisseYuzeysel HisseYuzeysel { get; set; }
    public Nullable<double> Stc_5_3 { get; set; }
    public Nullable<double> RSI14 { get; set; }
    public Nullable<double> mov10 { get; set; }
    public Nullable<double> CCI14 { get; set; }

    public HisseYuzeyselIndikator()
    {
        HisseYuzeysel=new HisseYuzeysel();
    }

}


public class LiveStockHisse
{
    public int SEMBOLID { get; set; }
    public string SEMBOL { get; set; }
    public Nullable<System.DateTime> TARIH { get; set; }
    public Nullable<double> ALIS { get; set; }
    public Nullable<double> SATIS { get; set; }
    public Nullable<double> ACILIS { get; set; }
    public Nullable<double> YUKSEK { get; set; }
    public Nullable<double> YUKSEK1 { get; set; }
    public Nullable<double> YUKSEK2 { get; set; }
    public Nullable<double> DUSUK { get; set; }
    public Nullable<double> DUSUK1 { get; set; }
    public Nullable<double> DUSUK2 { get; set; }
    public Nullable<double> KAPANIS { get; set; }
    public Nullable<double> KAPANIS1 { get; set; }
    public Nullable<double> KAPANIS2 { get; set; }
    public Nullable<decimal> HACIMLOT { get; set; }
    public Nullable<decimal> HACIMLOT1 { get; set; }
    public Nullable<decimal> HACIMLOT2 { get; set; }
    public Nullable<double> AORT { get; set; }
    public Nullable<double> AORT1 { get; set; }
    public Nullable<double> AORT2 { get; set; }
    public Nullable<decimal> HACIMTLDUN { get; set; }
    public Nullable<decimal> HACIMYUZDEDEGISIM { get; set; }

    public Nullable<decimal> HACIMTL { get; set; }
    public Nullable<decimal> HACIMTL1 { get; set; }
    public Nullable<decimal> HACIMTL2 { get; set; }
    public Nullable<double> DUNKUKAPANIS { get; set; }
    public Nullable<double> ONCEKIKAPANIS { get; set; }
    public Nullable<double> IZAFIKAPANIS { get; set; }
    public Nullable<double> TAVAN { get; set; }
    public Nullable<double> TABAN { get; set; }
    public Nullable<double> YILYUKSEK { get; set; }
    public Nullable<double> YILDUSUK { get; set; }
    public Nullable<double> AYYUKSEK { get; set; }
    public Nullable<double> AYDUSUK { get; set; }
    public Nullable<double> HAFTAYUKSEK { get; set; }
    public Nullable<double> HAFTADUSUK { get; set; }
    public Nullable<double> ONCEKIYILKAPANIS { get; set; }
    public Nullable<double> ONCEKIAYKAPANIS { get; set; }
    public Nullable<double> ONCEKIHAFTAKAPANIS { get; set; }
    public Nullable<double> YILORTALAMA { get; set; }
    public Nullable<double> AYORTALAMA { get; set; }
    public Nullable<double> HAFTAORTALAMA { get; set; }
    public Nullable<double> FIYATADIMI { get; set; }
    public Nullable<double> LOTADET { get; set; }
    public Nullable<double> KAYKAR { get; set; }
    public Nullable<double> SERMAYE { get; set; }
    public Nullable<double> SAKLAMAOR { get; set; }

    public Nullable<double> XU100AG { get; set; }

    public Nullable<double> NETKAR { get; set; }
    public Nullable<decimal> NET { get; set; }
    public Nullable<double> YUZDEDEGISIM { get; set; }



}
