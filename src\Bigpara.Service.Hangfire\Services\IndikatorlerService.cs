﻿
using Bigpara.Application.Contracts.Repositories.Matriks.Indikators;
using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Matriks;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class IndikatorlerService : IIndikatorlerService
{
    private readonly IIndikatorRepository _indikatorRepository;
    private readonly ICacheService _cacheService;

    public IndikatorlerService
    (
        IIndikatorRepository indikatorRepository,
        ICacheService cacheService
    )
    {
        _indikatorRepository = indikatorRepository;
        _cacheService = cacheService;
    }

    public async Task<Indikatorler> GetIndikatorlers(string sembol, DateTime dateTime)
    {
        return await _indikatorRepository.GetIndikatorSembolbyTarih(sembol, dateTime);
    }

    public async Task<int> CreateOrUpdate(Indikatorler indikator)
    {
        return await _indikatorRepository.CreateOrUpdate(indikator);
    }

    public async Task<Indikatorler> GetIndikatorbySembol(string sembol)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_INDIKATOR_BY_SEMBOL, sembol);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_10DK_DURATION, () => _indikatorRepository.GetIndikatorbySembol(sembol)));
    }

    public async Task<List<Indikatorler>> GetIndikatorlers(DateTime dateTime)
    {
        return await _indikatorRepository.GetIndikatorbyTarih(dateTime);
    }

    public async Task<List<Indikatorler>> GetOtomatikTeknikYorumIndikatorSembolbyTarih(string sembol, DateTime dateTime)
    {
        return await _indikatorRepository.GetOtomatikTeknikYorumIndikatorSembolbyTarih(sembol, dateTime);
    }
}
