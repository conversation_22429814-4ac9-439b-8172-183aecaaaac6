﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Bigpara.Application.Dtos
{
    public class GrafikDto
    {
        [JsonPropertyName("Symbol")]
        public string SEMBOL { get; set; }
        public int SEMBOLID { get; set; }
        [JsonPropertyName("Date")]
        public System.DateTime TARIH { get; set; }
        [JsonPropertyName("Open")]
        public Nullable<double> ACILIS { get; set; }
        [JsonPropertyName("High")]
        public Nullable<double> YUKSEK { get; set; }
        [JsonPropertyName("Low")]
        public Nullable<double> DUSUK { get; set; }
        [Json<PERSON>ropertyName("Close")]
        public Nullable<double> KAPANIS { get; set; }
        [JsonPropertyName("TotalVolume")]
        public Nullable<decimal> HACIMLOT { get; set; }
        [Json<PERSON>ropertyName("VWap")]
        public Nullable<double> AORT { get; set; }

        [JsonPropertyName("TotalTurnover")]
        public Nullable<decimal> H<PERSON>IMTL { get; set; }

    }
}
