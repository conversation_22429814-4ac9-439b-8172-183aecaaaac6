﻿using Bigpara.Domain.Bigpara;
using Bigpara.Domain.HurPass;

namespace Bigpara.Service.Hangfire.Services.Interfaces;

public interface IUserService
{
    Task<User> GetUserByHurPassId(string hurrPassId, bool refreshCache = false);
    Task<User> GetUserByEmailAdress(string email);
    Task<UserPayment> GetLicenedUserByHurpassId(string hurrPassId);
    Task UpdateUserCacheByHurPassId(User user);
    Task<List<UserSembol>> RenewUserSembols(string hurrPassId);
    Task RemoveUserCache(string hurrpassId);
    Task UpdateUserLastLoginDate(User user);
    Task<int> UpdateUserLocation(User user);
    Task<User> UpdateUser(User user);
    Task<int> DeleteUser(User user);
    Task<User> RegisterUser(SsoUser hurpassUser);
    Task<int> RegisterUserPayment(UserPayment userPayment);
    List<User> GetUserList(int pageIndex, int pageSize, out int totalCount);
    Task<User> GetUserByUsernamePassword(string username, string password);
    Task<int> LoginLogger(UserActivityLogger userActivityLogger);
    Task<int> CanliBorsaViewLog(UserActivityLogger userActivityLogger);
    Task<int> CanliBorsaFormFillLog(UserActivityLogger userActivityLogger);
    Task<bool> IsEmptyUserAddress(string hurrPassId);
}
