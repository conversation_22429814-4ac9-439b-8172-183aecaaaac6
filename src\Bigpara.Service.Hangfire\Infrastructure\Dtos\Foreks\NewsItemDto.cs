﻿using Newtonsoft.Json;

namespace Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;

public class NewsItemDto
{
    [JsonProperty("_id")]
    public string _id { get; set; }

    [JsonProperty("createdBy")]
    public string CreatedBy { get; set; }

    [JsonProperty("createDate")]
    public long CreateDate { get; set; }

    [JsonProperty("updatedBy")]
    public string UpdatedBy { get; set; }

    [JsonProperty("updatedDate")]
    public long UpdateDate { get; set; }

    [JsonProperty("publishedBy")]
    public string PublishedBy { get; set; }

    [JsonProperty("publishDate")]
    public long PublishDate { get; set; }

    [JsonProperty("source")]
    public string Source { get; set; }

    [JsonProperty("locale")]
    public string Locale { get; set; }

    [JsonProperty("sourceRef")]
    public string SourceRef { get; set; }

    [JsonProperty("sourceId")]
    public string SourceId { get; set; }

    [JsonProperty("mimeType")]
    public string MimeType { get; set; }

    [JsonProperty("header")]
    public string Header { get; set; }

    [JsonProperty("summary")]
    public string Summary { get; set; }

    [JsonProperty("content")]
    public string Content { get; set; }

    [JsonProperty("sender")]
    public Sender Sender { get; set; }

    [JsonProperty("relatedStock")]
    public List<string>? RelatedStock { get; set; }

    [JsonProperty("tag")]
    public List<string> Tag { get; set; }

    public List<string> Sembol { get; set; }
}

public class Sender
{
    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("codes")]
    public List<string> Codes { get; set; }
}