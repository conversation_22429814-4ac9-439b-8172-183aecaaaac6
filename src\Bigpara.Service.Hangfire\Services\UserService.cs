﻿using Bigpara.Application.Contracts.Repositories.BigparaDB.Users;
using Bigpara.Application.Contracts.Repositories.Matriks.Borsa;
using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Bigpara;
using Bigpara.Domain.HurPass;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class UserService : IUserService
{
    private readonly IUserRepository _userRepository;
    private readonly IRedisCacheService _redisCacheService;
    private readonly IHisseRepository _hisseRepository;
    private readonly IMetaDataService _metaDataService;
    private readonly ILogger<UserService> _logger;

    public UserService
    (
        IUserRepository userRepository,
        IRedisCacheService redisCacheService,
        IHisseRepository hisseRepository,
        IMetaDataService metaDataService,
        ILogger<UserService> logger
    )
    {
        _userRepository = userRepository;
        _redisCacheService = redisCacheService;
        _hisseRepository = hisseRepository;
        _metaDataService = metaDataService;
        _logger = logger;
    }

    public async Task<User> GetUserByHurPassId(string hurrPassId, bool refreshCache = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(hurrPassId))
                return null;

            string key = string.Format(CacheKeys.BIGPARA_REDIS_USER_GET_BY_HURRPASS_ID, hurrPassId);

            if (refreshCache)
                _redisCacheService.Remove(key);

            var user = _redisCacheService.Get(key, CacheKeys.CACHE_DATA_1DAY_DURATION, () =>
            {
                return GetUserByHurpassIdLive(hurrPassId).GetAwaiter().GetResult();
            });

            if (user != null)
                return user;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
        }

        return await GetUserByHurpassIdLive(hurrPassId);
    }
    private async Task<User> GetUserByHurpassIdLive(string hurrPassId)
    {
        var userData = await _userRepository.GetUserByHurpassID(hurrPassId);
        if (userData != null)
        {
            userData.UserSembolList = await _hisseRepository.GetUserHisseList(userData.Id);
        }

        return userData;
    }

    public async Task<User> GetUserByEmailAdress(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return null;

        return await _userRepository.GetUserByEmail(email);
    }

    public async Task<UserPayment> GetLicenedUserByHurpassId(string hurrPassId)
    {
        if (string.IsNullOrWhiteSpace(hurrPassId))
            return null;

        string key = string.Format(CacheKeys.BIGPARA_REDIS_LICENCED_USER_GET_BY_HURRPASS_ID, hurrPassId);

        return _redisCacheService.Get(key, CacheKeys.CACHE_DATA_1DAY_DURATION, () =>
        {
            var userData = _userRepository.GetLicencedUserByHurpassID(hurrPassId).GetAwaiter().GetResult();

            return userData;
        });
    }

    public Task UpdateUserCacheByHurPassId(User user)
    {
        if (user == null || string.IsNullOrEmpty(user.HurPassId))
            return Task.CompletedTask;

        string key = string.Format(CacheKeys.BIGPARA_REDIS_USER_GET_BY_HURRPASS_ID, user.HurPassId);
        if (_redisCacheService.IsSet(key))
        {
            _redisCacheService.Add(key, user);
        }

        return Task.CompletedTask;
    }
    public async Task<List<UserSembol>> RenewUserSembols(string hurrPassId)
    {
        if (string.IsNullOrWhiteSpace(hurrPassId))
            return null;

        string key = string.Format(CacheKeys.BIGPARA_REDIS_USER_GET_BY_HURRPASS_ID, hurrPassId);
        User userData = null;

        if (_redisCacheService.IsSet(key))
        {
            userData = _redisCacheService.Get<User>(key);
        }
        else
            userData = await _userRepository.GetUserByHurpassID(hurrPassId);

        if (userData != null)
        {
            userData.UserSembolList = await _hisseRepository.GetUserHisseList(userData.Id);
            _redisCacheService.Add(key, userData);

            return userData.UserSembolList;
        }

        return null;
    }

    public async Task<User> RegisterUser(SsoUser hurpassUser)
    {
        string fullname = hurpassUser.FullName != null ? hurpassUser.FullName.Trim() : string.Empty;
        string firstname = fullname.LastIndexOf(' ') > 0
            ? fullname.Substring(0, fullname.LastIndexOf(' '))
            : fullname;
        string lastname = fullname.LastIndexOf(' ') > 0
            ? fullname.Substring(fullname.LastIndexOf(' ') + 1)
            : string.Empty;


        var alreadyUser = await _userRepository.GetUserByHurpassID(hurpassUser.UserId);

        var user = new User
        {
            HurPassId = hurpassUser.UserId,
            AddDate = DateTime.Now,
            UserType = 0,
            FirstName = firstname,
            LastName = lastname,
            Gender = hurpassUser.Gender,
            Email = hurpassUser.Email,
            FBProfile = hurpassUser.FBprofile,
            LIProfile = hurpassUser.LIprofile,
            TWProfile = hurpassUser.TWprofile,
            IsHurPassData = true
        };

        var city = (await _metaDataService.GetCities()).FirstOrDefault(p => p.Code == hurpassUser.CityId);

        if (city != null)
        {
            user.CityId = city.Id;
            user.CountryId = city.CountryId;
        }
        else if (alreadyUser != null)
        {
            user.CityId = alreadyUser.CityId;
            user.CountryId = alreadyUser.CountryId;
        }

        if (hurpassUser.BirthDate > DateTime.MinValue)
            user.BirthDate = hurpassUser.BirthDate;
        else
            user.BirthDate = null;


        //Create user on db
        user = await _userRepository.RegisterUser(user);
        if (user != null)
        {
            user = await _userRepository.RegisterOrUpdateUser(user);
            RemoveUserCache(user.HurPassId);
        }

        if (user == null || user.Id <= 0)
            return user;

        return user;
    }

    public async Task<int> RegisterUserPayment(UserPayment userPayment)
    {
        return await _userRepository.InsertUserPaymentDetails(userPayment);
    }

    public List<User> GetUserList(int pageIndex, int pageSize, out int totalCount)
    {
        return _userRepository.GetUserList(pageIndex, pageSize, out totalCount);
    }

    public async Task<int> UpdateUserLocation(User user)
    {
        string key = string.Format(CacheKeys.BIGPARA_REDIS_USER_GET_BY_HURRPASS_ID, user.HurPassId);
        _redisCacheService.Add(key, user);

        return await _userRepository.UpdateUserLocation(user);
    }

    public async Task<User> UpdateUser(User user)
    {
        string key = string.Format(CacheKeys.BIGPARA_REDIS_USER_GET_BY_HURRPASS_ID, user.HurPassId);
        _redisCacheService.Add(key, user);

        return await _userRepository.RegisterOrUpdateUser(user);
    }

    public async Task UpdateUserLastLoginDate(User user)
    {
        await _userRepository.UpdateUserLastLoginDate(user);
    }

    public async Task<User> GetUserByUsernamePassword(string username, string password)
    {
        var user = await _userRepository.GetUserByUsernamePassword(username, password.SHA512HashData());

        if (user == null)
            return null;

        return user;
    }

    public async Task<int> LoginLogger(UserActivityLogger userActivityLogger)
    {
        return await _userRepository.LoginLogger(userActivityLogger);
    }

    public async Task<int> CanliBorsaViewLog(UserActivityLogger userActivityLogger)
    {
        return await _userRepository.CanliBorsaViewLog(userActivityLogger);
    }

    public async Task<int> CanliBorsaFormFillLog(UserActivityLogger userActivityLogger)
    {
        return await _userRepository.CanliBorsaFormFillLog(userActivityLogger);
    }

    public Task RemoveUserCache(string hurrpassId)
    {
        if (string.IsNullOrWhiteSpace(hurrpassId))
            return Task.CompletedTask;

        string key = string.Format(CacheKeys.BIGPARA_REDIS_USER_GET_BY_HURRPASS_ID, hurrpassId);
        _redisCacheService.Remove(key);

        return Task.CompletedTask;
    }

    public async Task<int> DeleteUser(User user)
    {
        return await _userRepository.DeleteUser(user);
    }

    public async Task<bool> IsEmptyUserAddress(string hurrPassId)
    {
        return await _userRepository.IsEmptyUserAddress(hurrPassId);
    }
}
