﻿using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Data.Common;
using System.Reflection;

namespace Bigpara.Service.Hangfire.Infrastructure.Data;
public class StoredProcedureParameterService : IStoredProcedureParameterService
{
    private readonly ILogger<StoredProcedureParameterService> _logger;
    private readonly IConfiguration _configuration;
    private readonly INotificationService _notificationService;

    public StoredProcedureParameterService(
        ILogger<StoredProcedureParameterService> logger,
        IConfiguration configuration,
        INotificationService notificationService)
    {
        _logger = logger;
        _configuration = configuration;
        _notificationService = notificationService;
    }

    private static object GetDbValue(object? value) => value ?? DBNull.Value;

    public SqlParameter[] CreateSymbolStoredProcedureParameters(ForeksSembolDto sembollerDto)
    {
        var intSil = sembollerDto.Aktif ? 0 : 1;

        return
        [
            new SqlParameter("@strKod", GetDbValue(sembollerDto.Sembol)),
            new SqlParameter("@strAd", GetDbValue(sembollerDto.Aciklama)),
            new SqlParameter("@strPiyasa", GetDbValue(sembollerDto.StrPiyasa)),
            new SqlParameter("@strSektor", GetDbValue(sembollerDto.Sector)),
            new SqlParameter("@dblHane", GetDbValue(sembollerDto.OndalikBasamak)),
            new SqlParameter("@dblSermaye", DBNull.Value),
            new SqlParameter("@dblArz", DBNull.Value),
            new SqlParameter("@intSil", value:intSil),
            new SqlParameter("@strEndeks", GetDbValue(sembollerDto.StrEndeks)),
            new SqlParameter("@dblOzSermaye", DBNull.Value),
            new SqlParameter("@dblNetKar", DBNull.Value),
            new SqlParameter("@strSonDonem", DBNull.Value),
            new SqlParameter("@dblNominal", DBNull.Value),
            new SqlParameter("@strContStock", DBNull.Value),
            new SqlParameter("@strSubMarket", DBNull.Value),
            new SqlParameter("@strISINKod", DBNull.Value),
            new SqlParameter("@dblNetTemettu", DBNull.Value),
            new SqlParameter("@dblFiiliDolasimOrani", DBNull.Value),
            new SqlParameter("@strHisseSeri", GetDbValue(sembollerDto.ImkbHisseTip)),
            new SqlParameter("@strPazar", GetDbValue(sembollerDto.ImkbPazarKod)),
            new SqlParameter("@dblVobFiyatAdimi", DBNull.Value),
            new SqlParameter("@dblVobKontratMarji", DBNull.Value),
            new SqlParameter("@dblLastPeriodProfit", DBNull.Value),
            new SqlParameter("@strKita", DBNull.Value),
            new SqlParameter("@strUlke", DBNull.Value),
            new SqlParameter("@strCompanyGroup", DBNull.Value),
            new SqlParameter("@dblVOBBaslangicTeminati", DBNull.Value),
            new SqlParameter("@strUnderlyingSecurity", DBNull.Value),
            new SqlParameter("@strFirstTradeDate", DBNull.Value),
            new SqlParameter("@strLastTradeDate", DBNull.Value),
            new SqlParameter("@strMaturity", DBNull.Value),
            new SqlParameter("@strCurrency", DBNull.Value),
            new SqlParameter("@strSecurityType", DBNull.Value),
            new SqlParameter("@strLabel", DBNull.Value),
            new SqlParameter("@dblOrder", DBNull.Value),
            new SqlParameter("@dblISEIndexFFR", DBNull.Value),
            new SqlParameter("@strIssuer", DBNull.Value)
        ];
    }

    public SqlParameter[] CreateViopSymbolStoredProcedureParameters(ForeksSembolDto sembollerDto)
    {
        return
        [
            new SqlParameter("@strMarket", DBNull.Value),
            new SqlParameter("@strInstrument", DBNull.Value),
            new SqlParameter("@strBoard", sembollerDto.StrBoard),
            new SqlParameter("@strContract", GetDbValue(sembollerDto.Sembol)),
            new SqlParameter("@strUnderlyingSecurityCode", DBNull.Value),
            new SqlParameter("@strOptionType", DBNull.Value),
            new SqlParameter("@dblMaturityDate", DBNull.Value),
            new SqlParameter("@strOptionClass", DBNull.Value),
            new SqlParameter("@strStrikePrice", DBNull.Value),
            new SqlParameter("@strSecurityGroupName", DBNull.Value),
            new SqlParameter("@dblSecurityGroupSeqNo", DBNull.Value),
            new SqlParameter("@dblListingDate", DBNull.Value),
            new SqlParameter("@dblContractMultiplier", DBNull.Value),
            new SqlParameter("@strSettlementType", DBNull.Value),
            new SqlParameter("@strParentContractCode", DBNull.Value),
            new SqlParameter("@strActionType", DBNull.Value),
            new SqlParameter("@dblMarginPct", DBNull.Value),
            new SqlParameter("@dblPriceStep", DBNull.Value),
            new SqlParameter("@dblTradingUnitCollateral", DBNull.Value),
            new SqlParameter("@dblPrevSettlePrice", DBNull.Value),
            new SqlParameter("@dblDecimalCount", GetDbValue(sembollerDto.OndalikBasamak)),
            new SqlParameter("@strContStock", DBNull.Value),
            new SqlParameter("@strForeksUndSec", DBNull.Value),
            new SqlParameter("@dblSecurityCount", DBNull.Value),
            new SqlParameter("@dblLimitUp", DBNull.Value),
            new SqlParameter("@dblLimitDown", DBNull.Value),
            new SqlParameter("@strShortName", GetDbValue(sembollerDto.Aciklama)),
            new SqlParameter("@strShortNameEnglish", DBNull.Value)
        ];
    }

    public SqlParameter[] CreatePassiveSymbolStoredProcedureParameters(string strKod)
    {
        return
        [
            new SqlParameter("@strKod", strKod)
        ];
    }

    public SqlParameter[] CreateFidesPariteStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
         [
            new SqlParameter("@Owner",yuzeyselDto.Proccesor),
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            //Sp tarafında legacy Code kontrolü yapılıyor o yüzden legacy code gönderilmeli.
            new SqlParameter("@strSembol", GetDbValue(yuzeyselDto.LegacyCode)),
            new SqlParameter("@dblSon", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblAlis", yuzeyselDto.ALIS.HasValue ? yuzeyselDto.ALIS.Value : DBNull.Value),
            new SqlParameter("@dblSatis", yuzeyselDto.SATIS.HasValue ? yuzeyselDto.SATIS.Value : DBNull.Value),
            new SqlParameter("@dblEnDusuk", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@dblOncekiKapanis", yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblAcilis", yuzeyselDto.ACILIS.HasValue ? yuzeyselDto.ACILIS.Value : DBNull.Value),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateFidesPariteRealTimeStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
         [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            //Sp tarafında legacy Code kontrolü yapılıyor o yüzden legacy code gönderilmeli.
            new SqlParameter("@strSembol", GetDbValue(yuzeyselDto.LegacyCode)),
            new SqlParameter("@dblSon", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblAlis", yuzeyselDto.ALIS.HasValue ? yuzeyselDto.ALIS.Value : DBNull.Value),
            new SqlParameter("@dblSatis", yuzeyselDto.SATIS.HasValue ? yuzeyselDto.SATIS.Value : DBNull.Value),
            new SqlParameter("@dblEnDusuk", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@dblOncekiKapanis", yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblAcilis", yuzeyselDto.ACILIS.HasValue ? yuzeyselDto.ACILIS.Value : DBNull.Value),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateFidesPariteHistoricalStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
         [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
             //Sp tarafında legacy Code kontrolü yapılıyor o yüzden legacy code gönderilmeli.
            new SqlParameter("@strSembol", GetDbValue(yuzeyselDto.LegacyCode)),
            new SqlParameter("@strTip", value:"S"),
            new SqlParameter("@dblHaftaEnDusuk", yuzeyselDto.HAFTADUSUK.HasValue ? yuzeyselDto.HAFTADUSUK.Value : DBNull.Value),
            new SqlParameter("@dblHaftaEnYuksek", yuzeyselDto.HAFTAYUKSEK.HasValue ? yuzeyselDto.HAFTAYUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblHaftaOncekiKapanis", yuzeyselDto.ONCEKIHAFTAKAPANIS.HasValue ? yuzeyselDto.ONCEKIHAFTAKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblAyEnDusuk", yuzeyselDto.AYDUSUK.HasValue ? yuzeyselDto.AYDUSUK.Value : DBNull.Value),
            new SqlParameter("@dblAyEnYuksek", yuzeyselDto.AYYUKSEK.HasValue ? yuzeyselDto.AYYUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblAyOncekiKapanis", yuzeyselDto.ONCEKIAYKAPANIS.HasValue ? yuzeyselDto.ONCEKIAYKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblYilEnDusuk", yuzeyselDto.YILDUSUK.HasValue ? yuzeyselDto.YILDUSUK.Value : DBNull.Value),
            new SqlParameter("@dblYilEnYuksek", yuzeyselDto.YILYUKSEK.HasValue ? yuzeyselDto.YILYUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblYilOncekiKapanis", yuzeyselDto.ONCEKIYILKAPANIS.HasValue ? yuzeyselDto.ONCEKIYILKAPANIS.Value : DBNull.Value),
            new SqlParameter("@strHaftaEnDusukTarihi", DBNull.Value),
            new SqlParameter("@strHaftaEnYuksekTarihi", DBNull.Value),
            new SqlParameter("@strAyEnDusukTarihi", DBNull.Value),
            new SqlParameter("@strAyEnYuksekTarihi", DBNull.Value),
            new SqlParameter("@strYilEnDusukTarihi", DBNull.Value),
            new SqlParameter("@strYilEnYuksekTarihi", DBNull.Value)
        ];
    }

    public SqlParameter[] CreateHisseAcilisStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)) ,
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)),
            new SqlParameter("@strPiyasa", DBNull.Value),
            new SqlParameter("@dblAcilis1", yuzeyselDto.ACILIS.HasValue ? yuzeyselDto.ACILIS.Value : DBNull.Value),
            new SqlParameter("@dblAcilis2", yuzeyselDto.ACILIS.HasValue ? yuzeyselDto.ACILIS.Value : DBNull.Value),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateHisseKapanisStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)),
            new SqlParameter("@strPiyasa", DBNull.Value),
            new SqlParameter("@dblKapanis1", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblOncekiKapanis1", yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblKapanis2", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblOncekiKapanis2", yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value),
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateHisseSonStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)) ,
            new SqlParameter("@strPiyasa", DBNull.Value) ,
            new SqlParameter("@dblSon", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value) ,
            new SqlParameter("@dblOncekiSon", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value) ,
            new SqlParameter("@dblSonAdet", yuzeyselDto.HACIMLOT.HasValue ? yuzeyselDto.HACIMLOT.Value : DBNull.Value) ,
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")) ,
            new SqlParameter("@isRefresh", value: 0),
            new SqlParameter("@dblOzSermaye", yuzeyselDto.OZSERMAYE.HasValue ? yuzeyselDto.OZSERMAYE.Value : DBNull.Value),
            new SqlParameter("@dblSermaye", yuzeyselDto.SERMAYE.HasValue ? yuzeyselDto.SERMAYE.Value : DBNull.Value),
            new SqlParameter("@dblLastPeriodProfit", yuzeyselDto.NETKAR.HasValue ? yuzeyselDto.NETKAR.Value : DBNull.Value),
            new SqlParameter("@strSonDonem", GetDbValue(yuzeyselDto.DONEM)),
            new SqlParameter("@dblFK", yuzeyselDto.FIYATKAZ.HasValue ? yuzeyselDto.FIYATKAZ.Value : DBNull.Value),
            new SqlParameter("@dblPiyasaDegeri", yuzeyselDto.PIYDEG.HasValue ? yuzeyselDto.PIYDEG.Value : DBNull.Value),
            new SqlParameter("@dblSenetSayisi", yuzeyselDto.SENETSAY.HasValue ? yuzeyselDto.SENETSAY.Value : DBNull.Value),
            //kullanım dışı
            new SqlParameter("@dblFiiliDolasimOrani", DBNull.Value),
            new SqlParameter("@dblXU100AG", yuzeyselDto.XU100AG.HasValue ? yuzeyselDto.XU100AG.Value : DBNull.Value)
        ];
    }

    public SqlParameter[] CreateHisseToplamStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)),
            new SqlParameter("@strPiyasa", DBNull.Value),
            new SqlParameter("@dblToplamHacim1", yuzeyselDto.HACIMTL.HasValue ? yuzeyselDto.HACIMTL.Value : DBNull.Value),
            new SqlParameter("@dblToplamAdet1", yuzeyselDto.HACIMLOT.HasValue ? yuzeyselDto.HACIMLOT.Value : DBNull.Value) ,
            new SqlParameter("@dblToplamHacim2", yuzeyselDto.HACIMTL.HasValue ? yuzeyselDto.HACIMTL.Value : DBNull.Value),
            new SqlParameter("@dblToplamAdet2", yuzeyselDto.HACIMLOT.HasValue ? yuzeyselDto.HACIMLOT.Value : DBNull.Value) ,
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateHisseSonRealTimeStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)),
            new SqlParameter("@strPiyasa", DBNull.Value),
            new SqlParameter("@dblSon", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value) ,
            new SqlParameter("@dblOncekiSon", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblSonAdet", yuzeyselDto.HACIMLOT.HasValue ? yuzeyselDto.HACIMLOT.Value : DBNull.Value),
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")) ,
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateHisseIstatistikRealTimeStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)),
            new SqlParameter("@strPiyasa", DBNull.Value),
            new SqlParameter("@dblEnIyiAlis", yuzeyselDto.ALIS.HasValue ? yuzeyselDto.ALIS.Value : DBNull.Value),
            new SqlParameter("@dblEnIyiSatis", yuzeyselDto.SATIS.HasValue ? yuzeyselDto.SATIS.Value : DBNull.Value),
            new SqlParameter("@dblEnDusuk1", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek1", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblEnDusuk2", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek2", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateHisseIstatistikStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)),
            new SqlParameter("@strPiyasa", DBNull.Value),
            new SqlParameter("@dblEnIyiAlis", yuzeyselDto.ALIS.HasValue ? yuzeyselDto.ALIS.Value : DBNull.Value),
            new SqlParameter("@dblEnIyiSatis", yuzeyselDto.SATIS.HasValue ? yuzeyselDto.SATIS.Value : DBNull.Value),
            new SqlParameter("@dblEnDusuk1", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek1", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblEnDusuk2", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek2", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateHisseOrtalamaStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)),
            new SqlParameter("@strPiyasa", DBNull.Value),
            new SqlParameter("@dblOrtalama1", yuzeyselDto.AORT.HasValue ? yuzeyselDto.AORT.Value : DBNull.Value),
            new SqlParameter("@dblOrtalama2", yuzeyselDto.AORT.HasValue ? yuzeyselDto.AORT.Value : DBNull.Value),
            new SqlParameter("@dblOrtalama", yuzeyselDto.AORT.HasValue ? yuzeyselDto.AORT.Value : DBNull.Value),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateHisseOrtalamaRealTimeStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)),
            new SqlParameter("@strPiyasa", DBNull.Value),
            new SqlParameter("@dblOrtalama1", yuzeyselDto.AORT.HasValue ? yuzeyselDto.AORT.Value : DBNull.Value),
            new SqlParameter("@dblOrtalama2", yuzeyselDto.AORT.HasValue ? yuzeyselDto.AORT.Value : DBNull.Value),
            new SqlParameter("@dblOrtalama", yuzeyselDto.AORT.HasValue ? yuzeyselDto.AORT.Value : DBNull.Value),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateHisseHistoricalStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
         [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)),
            new SqlParameter("@dblHaftaEnDusuk", yuzeyselDto.HAFTADUSUK.HasValue ? yuzeyselDto.HAFTADUSUK.Value : DBNull.Value),
            new SqlParameter("@strHaftaEnDusukTarihi", DBNull.Value),
            new SqlParameter("@dblHaftaEnYuksek", yuzeyselDto.HAFTAYUKSEK.HasValue ? yuzeyselDto.HAFTAYUKSEK.Value : DBNull.Value),
            new SqlParameter("@strHaftaEnYuksekTarihi", DBNull.Value),
            new SqlParameter("@dblHaftaOncekiKapanis", yuzeyselDto.ONCEKIHAFTAKAPANIS.HasValue ? yuzeyselDto.ONCEKIHAFTAKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblAyEnDusuk", yuzeyselDto.AYDUSUK.HasValue ? yuzeyselDto.AYDUSUK.Value : DBNull.Value),
            new SqlParameter("@strAyEnDusukTarihi", DBNull.Value),
            new SqlParameter("@dblAyEnYuksek", yuzeyselDto.AYYUKSEK.HasValue ? yuzeyselDto.AYYUKSEK.Value : DBNull.Value),
            new SqlParameter("@strAyEnYuksekTarihi", DBNull.Value),
            new SqlParameter("@dblAyOncekiKapanis", yuzeyselDto.ONCEKIAYKAPANIS.HasValue ? yuzeyselDto.ONCEKIAYKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblYilEnDusuk", yuzeyselDto.YILDUSUK.HasValue ? yuzeyselDto.YILDUSUK.Value : DBNull.Value),
            new SqlParameter("@strYilEnDusukTarihi", DBNull.Value),
            new SqlParameter("@dblYilEnYuksek", yuzeyselDto.YILYUKSEK.HasValue ? yuzeyselDto.YILYUKSEK.Value : DBNull.Value),
            new SqlParameter("@strYilEnYuksekTarihi", DBNull.Value),
            new SqlParameter("@dblYilOncekiKapanis", yuzeyselDto.ONCEKIYILKAPANIS.HasValue ? yuzeyselDto.ONCEKIYILKAPANIS.Value : DBNull.Value)
        ];
    }

    public SqlParameter[] CreateHisseMarjStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
         [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strKod", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSeri", GetDbValue(yuzeyselDto.Security)),
            new SqlParameter("@strPiyasa", DBNull.Value),
            new SqlParameter("@dblAltMarj", yuzeyselDto.TABAN.HasValue ? yuzeyselDto.TABAN.Value : DBNull.Value),
            new SqlParameter("@dblUstMarj", yuzeyselDto.TAVAN.HasValue ? yuzeyselDto.TAVAN.Value : DBNull.Value),
            //Pttrow hesaplamasından dönen dönüş neyse buraya setlenmeli.
            new SqlParameter("@dblFiyatAdimi", yuzeyselDto.FIYATADIMI.HasValue ? yuzeyselDto.FIYATADIMI.Value : DBNull.Value),
            //Bunu sembol üstünden almalıyız.
            new SqlParameter("@strGrupKodu", GetDbValue(yuzeyselDto.HisseGrubu)),
            new SqlParameter("dblBazFiyat", DBNull.Value),
            //Sembollerdeki işlem türüne denk geliyor ama artık kullanılmıyor.
            new SqlParameter("@strTradeMethod", DBNull.Value),
            new SqlParameter("strHisseStatus", DBNull.Value),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateGrandBazaarStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
         [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strSembol", GetDbValue(yuzeyselDto.LegacyCode)),
            new SqlParameter("@dblAlis", yuzeyselDto.ALIS.HasValue ? yuzeyselDto.ALIS.Value : DBNull.Value),
            new SqlParameter("@dblSatis", yuzeyselDto.SATIS.HasValue ? yuzeyselDto.SATIS.Value : DBNull.Value),
            new SqlParameter("@dblEnDusuk", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@dblOncekiKapanis", yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value),
            new SqlParameter("@strGuncellemeTarihi", yuzeyselDto.Tarih.ToString("yyyyMMdd")),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateGrandBazaarHistoricalStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
         [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strSembol", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strTip",value:"S"),
            new SqlParameter("@dblHaftaEnDusuk", yuzeyselDto.HAFTADUSUK.HasValue ? yuzeyselDto.HAFTADUSUK.Value : DBNull.Value),
            new SqlParameter("@dblHaftaEnYuksek", yuzeyselDto.HAFTAYUKSEK.HasValue ? yuzeyselDto.HAFTAYUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblHaftaOncekiKapanis", yuzeyselDto.ONCEKIHAFTAKAPANIS.HasValue ? yuzeyselDto.ONCEKIHAFTAKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblAyEnDusuk", yuzeyselDto.AYDUSUK.HasValue ? yuzeyselDto.AYDUSUK.Value : DBNull.Value),
            new SqlParameter("@dblAyEnYuksek", yuzeyselDto.AYYUKSEK.HasValue ? yuzeyselDto.AYYUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblAyOncekiKapanis", yuzeyselDto.ONCEKIAYKAPANIS.HasValue ? yuzeyselDto.ONCEKIAYKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblYilEnDusuk", yuzeyselDto.YILDUSUK.HasValue ? yuzeyselDto.YILDUSUK.Value : DBNull.Value),
            new SqlParameter("@dblYilEnYuksek", yuzeyselDto.YILYUKSEK.HasValue ? yuzeyselDto.YILYUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblYilOncekiKapanis", yuzeyselDto.ONCEKIYILKAPANIS.HasValue ? yuzeyselDto.ONCEKIYILKAPANIS.Value : DBNull.Value),
            new SqlParameter("@strHaftaEnDusukTarihi", DBNull.Value),
            new SqlParameter("@strHaftaEnYuksekTarihi", DBNull.Value),
            new SqlParameter("@strAyEnDusukTarihi", DBNull.Value),
            new SqlParameter("@strAyEnYuksekTarihi", DBNull.Value),
            new SqlParameter("@strYilEnDusukTarihi", DBNull.Value),
            new SqlParameter("@strYilEnYuksekTarihi", DBNull.Value)
        ];
    }

    public SqlParameter[] CreateImkbVipSessionStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
         [
            new SqlParameter("@dtmDate", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strMarket", DBNull.Value),
            new SqlParameter("@strInstrument", DBNull.Value),
            new SqlParameter("@strBoard", GetDbValue(yuzeyselDto.StrBoard)),
            new SqlParameter("@strContract", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSession", DBNull.Value),
            new SqlParameter("@strContractStatus", DBNull.Value),
            new SqlParameter("@dblBestBidPrice", yuzeyselDto.ALIS.HasValue ? yuzeyselDto.ALIS.Value : DBNull.Value),
            new SqlParameter("@dblBestAskPrice", yuzeyselDto.SATIS.HasValue ? yuzeyselDto.SATIS.Value : DBNull.Value),
            new SqlParameter("@dblLastTradePrice", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblMarketMakerBestBidQuotation", DBNull.Value),
            new SqlParameter("@dblMarketMakerBestAskQuotation", DBNull.Value),
            new SqlParameter("@dblSessionLow", DBNull.Value),
            new SqlParameter("@dblSessionHigh", DBNull.Value),
            new SqlParameter("@dblDailyLowPrice", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblDailyHighPrice", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblOpeningValue", yuzeyselDto.ACILIS.HasValue ? yuzeyselDto.ACILIS.Value : DBNull.Value),
            new SqlParameter("@dblPreviousSettlementPrice", yuzeyselDto.VOB_ONCUZL.HasValue ? yuzeyselDto.VOB_ONCUZL.Value : DBNull.Value),
            new SqlParameter("@dblSettlementPrice", yuzeyselDto.VOB_UZL.HasValue ? yuzeyselDto.VOB_UZL.Value : DBNull.Value),
            new SqlParameter("@dblPreOpeningFixingSettlementPrice", DBNull.Value),
            new SqlParameter("@dblSessionWeightedAverage", DBNull.Value),
            new SqlParameter("@dblDailyWeightedAverage", yuzeyselDto.AORT.HasValue ? yuzeyselDto.AORT.Value : DBNull.Value),
            new SqlParameter("@strTimeStamp", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@dblPrevClose", yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value),
            new SqlParameter("@strPrevSettleDate", DBNull.Value),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateImkbVipSessionHD1PStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
         [
            new SqlParameter("@dtmDate", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strMarket", DBNull.Value),
            new SqlParameter("@strInstrument", DBNull.Value),
            new SqlParameter("@strBoard", GetDbValue(yuzeyselDto.StrBoard)),
            new SqlParameter("@strContract", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strSession", DBNull.Value),
            new SqlParameter("@strContractStatus", DBNull.Value),
            new SqlParameter("@dblBestBidVolume", DBNull.Value),
            new SqlParameter("@dblBestAskVolume", DBNull.Value),
            new SqlParameter("@dblSessionTotalVolume", DBNull.Value),
            new SqlParameter("@dblSessionTotalTurnover", DBNull.Value),
            new SqlParameter("@dblSessionTotalTransactionCount", DBNull.Value),
            new SqlParameter("@dblSessionTotalOptionPremiumValue", DBNull.Value),
            new SqlParameter("@dblDailyTotalVolume", yuzeyselDto.HACIMLOT.HasValue ? yuzeyselDto.HACIMLOT.Value : DBNull.Value),
            new SqlParameter("@dblDailyTotalTurnover", yuzeyselDto.HACIMTL.HasValue ? yuzeyselDto.HACIMTL.Value : DBNull.Value),
            //VOB_SAYI kaldırılmış
            new SqlParameter("@dblDailyTotalTransactionCount", DBNull.Value),
            new SqlParameter("@dblDailyTotalOptionPremiumValue", DBNull.Value),
            new SqlParameter("@dblNumberofShortPositions", DBNull.Value),
            new SqlParameter("@dblChangeInTheNumberofShortPositions", yuzeyselDto.VOB_OPINTDEG.HasValue ? yuzeyselDto.VOB_OPINTDEG.Value : DBNull.Value),
            new SqlParameter("@strTimeStamp", yuzeyselDto.Tarih.ToString("HHmmss")),
          new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[] CreateImkbViopHistoricalStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
         [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strSembol", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@strTip", value:"S"),
            new SqlParameter("@dblHaftaEnDusuk", yuzeyselDto.HAFTADUSUK.HasValue ? yuzeyselDto.HAFTADUSUK.Value : DBNull.Value),
            new SqlParameter("@dblHaftaEnYuksek", yuzeyselDto.HAFTAYUKSEK.HasValue ? yuzeyselDto.HAFTAYUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblHaftaOncekiKapanis", yuzeyselDto.ONCEKIHAFTAKAPANIS.HasValue ? yuzeyselDto.ONCEKIHAFTAKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblAyEnDusuk", yuzeyselDto.AYDUSUK.HasValue ? yuzeyselDto.AYDUSUK.Value : DBNull.Value),
            new SqlParameter("@dblAyEnYuksek", yuzeyselDto.AYYUKSEK.HasValue ? yuzeyselDto.AYYUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblAyOncekiKapanis", yuzeyselDto.ONCEKIAYKAPANIS.HasValue ? yuzeyselDto.ONCEKIAYKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblYilEnDusuk", yuzeyselDto.YILDUSUK.HasValue ? yuzeyselDto.YILDUSUK.Value : DBNull.Value),
            new SqlParameter("@dblYilEnYuksek", yuzeyselDto.YILYUKSEK.HasValue ? yuzeyselDto.YILYUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblYilOncekiKapanis", yuzeyselDto.ONCEKIYILKAPANIS.HasValue ? yuzeyselDto.ONCEKIYILKAPANIS.Value : DBNull.Value),
            new SqlParameter("@strHaftaEnDusukTarihi", DBNull.Value),
            new SqlParameter("@strHaftaEnYuksekTarihi", DBNull.Value),
            new SqlParameter("@strAyEnDusukTarihi", DBNull.Value),
            new SqlParameter("@strAyEnYuksekTarihi", DBNull.Value),
            new SqlParameter("@strYilEnDusukTarihi", DBNull.Value),
            new SqlParameter("@strYilEnYuksekTarihi", DBNull.Value)
        ];
    }

    public SqlParameter[] CreateTanimSektorlerStoredProcedureParameters(SektorlerDto sektorlerDto)
    {
        var intSil = sektorlerDto.Aktif ? 0 : 1;

        return
         [
             new SqlParameter("@intSil",value:intSil),
             new SqlParameter("@strKod", GetDbValue(sektorlerDto.StrKod)),
             new SqlParameter("@strAd", GetDbValue(sektorlerDto.StrAd))
        ];
    }

    public SqlParameter[] CreateImkbEndeksStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih ),
            new SqlParameter("@strKod", yuzeyselDto.SEMBOL),
            new SqlParameter("@dblSon", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblOncekiSon", yuzeyselDto.DUNKUKAPANIS.HasValue?yuzeyselDto.DUNKUKAPANIS.Value:DBNull.Value),
            new SqlParameter("@dblEnDusuk1", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek1", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblKapanis1", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblOncekiKapanis1", yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblEnDusuk2", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek2", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@dblKapanis2", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblOncekiKapanis2", yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value),
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@dblAcilis1", yuzeyselDto.ACILIS.HasValue ? yuzeyselDto.ACILIS.Value : DBNull.Value),
            new SqlParameter("@dblAcilis2", yuzeyselDto.ACILIS.HasValue ? yuzeyselDto.ACILIS.Value : DBNull.Value),
            new SqlParameter("@isRefresh",SqlDbType.Int){Value = 0}
        ];
    }

    public SqlParameter[] CreateImkbEndeksToplamStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return [
             new SqlParameter("@dtmTarih", SqlDbType.DateTime) { Value = yuzeyselDto.Tarih },
            new SqlParameter("@strKod", SqlDbType.VarChar, 32) { Value = yuzeyselDto.SEMBOL },
            new SqlParameter("@dblToplamHacim1", SqlDbType.Decimal) { Value = yuzeyselDto.HACIMTL.HasValue ?yuzeyselDto.HACIMTL.Value:DBNull.Value },
            new SqlParameter("@dblToplamAdet1", SqlDbType.Decimal) { Value = yuzeyselDto.HACIMLOT.HasValue?yuzeyselDto.HACIMLOT.Value:DBNull.Value},
            new SqlParameter("@dblToplamSozlesmeSayisi1", SqlDbType.Decimal) { Value = yuzeyselDto.VOB_OPINTDEG.HasValue ? yuzeyselDto.VOB_OPINTDEG.Value : DBNull.Value },
            new SqlParameter("@dblToplamHacim2", SqlDbType.Decimal) { Value = yuzeyselDto.HACIMTL.HasValue ? yuzeyselDto.HACIMTL.Value : DBNull.Value },
            new SqlParameter("@dblToplamAdet2", SqlDbType.Decimal) { Value = yuzeyselDto.HACIMLOT.HasValue ? yuzeyselDto.HACIMLOT.Value : DBNull.Value },
            new SqlParameter("@dblToplamSozlesmeSayisi2", SqlDbType.Decimal) { Value = yuzeyselDto.VOB_OPINTDEG.HasValue ? yuzeyselDto.VOB_OPINTDEG.Value : DBNull.Value },
            new SqlParameter("@isRefresh", SqlDbType.Int) { Value = 0 }

        ];
    }

    public SqlParameter[]? CreateImkbEndeksRealTimeStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return [
                new SqlParameter("@dtmTarih", SqlDbType.DateTime) { Value = yuzeyselDto.Tarih },
                new SqlParameter("@strKod", SqlDbType.VarChar, 32) { Value =yuzeyselDto.SEMBOL },
                new SqlParameter("@dblSon", SqlDbType.Decimal) { Value =yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value },
                new SqlParameter("@dblOncekiSon", SqlDbType.Decimal) { Value = yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value },
                new SqlParameter("@dblEnDusuk1", SqlDbType.Decimal) { Value = yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value },
                new SqlParameter("@dblEnYuksek1", SqlDbType.Decimal) { Value = yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value },
                new SqlParameter("@dblKapanis1", SqlDbType.Decimal) { Value = yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value },
                new SqlParameter("@dblOncekiKapanis1", SqlDbType.Decimal) { Value = yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value },
                new SqlParameter("@dblEnDusuk2", SqlDbType.Decimal) { Value = yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value },
                new SqlParameter("@dblEnYuksek2", SqlDbType.Decimal) { Value = yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value },
                new SqlParameter("@dblKapanis2", SqlDbType.Decimal) { Value = yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value },
                new SqlParameter("@dblOncekiKapanis2", SqlDbType.Decimal) { Value = yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value },
                new SqlParameter("@strSaat", SqlDbType.VarChar, 6) { Value = yuzeyselDto.Tarih.ToString("HHmmss") },
                new SqlParameter("@dblAcilis1", SqlDbType.Decimal) { Value = yuzeyselDto.ACILIS.HasValue ? yuzeyselDto.ACILIS.Value : DBNull.Value },
                new SqlParameter("@dblAcilis2", SqlDbType.Decimal) { Value =yuzeyselDto.ACILIS.HasValue ? yuzeyselDto.ACILIS.Value : DBNull.Value },
                new SqlParameter("@isRefresh", SqlDbType.Int) { Value = 0 }
        ];
    }

    public SqlParameter[]? CreateFidesGelecekStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strSembol", GetDbValue(yuzeyselDto.LegacyCode)),
            new SqlParameter("@dblSon", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblKapanis", yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblEnDusuk", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            new SqlParameter("@strKapanisTarihi", DBNull.Value),
            new SqlParameter("@strAy",  yuzeyselDto.Tarih.ToString("MM")),
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[]? CreateFidesEndeksStoredProcedureParameters(YuzeyselDto yuzeyselDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", yuzeyselDto.Tarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@strSembol", GetDbValue(yuzeyselDto.SEMBOL)),
            new SqlParameter("@dblSon", yuzeyselDto.KAPANIS.HasValue ? yuzeyselDto.KAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblKapanis", yuzeyselDto.DUNKUKAPANIS.HasValue ? yuzeyselDto.DUNKUKAPANIS.Value : DBNull.Value),
            new SqlParameter("@dblEnDusuk", yuzeyselDto.DUSUK.HasValue ? yuzeyselDto.DUSUK.Value : DBNull.Value),
            new SqlParameter("@dblEnYuksek", yuzeyselDto.YUKSEK.HasValue ? yuzeyselDto.YUKSEK.Value : DBNull.Value),
            //apide bu alan yok
            new SqlParameter("@strKapanisTarihi", yuzeyselDto.Tarih.ToString("ddMMyy")),
            new SqlParameter("@strSaat", yuzeyselDto.Tarih.ToString("HHmmss")),
            new SqlParameter("@dblAcilis", yuzeyselDto.ACILIS.HasValue ? yuzeyselDto.ACILIS.Value : DBNull.Value),
            new SqlParameter("@dblToplamHacim", yuzeyselDto.HACIMTL.HasValue ? yuzeyselDto.HACIMTL.Value : DBNull.Value),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[]? CreateTahvilKesinOzetStoredProcedureParameters(TahvilDto tahvilDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", DBNull.Value),
            new SqlParameter("@strVL", GetDbValue(tahvilDto.StrVl)),
            new SqlParameter("@strMAT", GetDbValue(tahvilDto.StrMat)),
            new SqlParameter("@strID", DBNull.Value),
            new SqlParameter("@dblAV", tahvilDto.DblAv.HasValue ? tahvilDto.DblAv : DBNull.Value),
            new SqlParameter("@dblSY", tahvilDto.DblSy.HasValue ? tahvilDto.DblSy : DBNull.Value),
            new SqlParameter("@dblCY", tahvilDto.DblCy.HasValue ? tahvilDto.DblCy : DBNull.Value),
            new SqlParameter("@dblTV", tahvilDto.DblTv.HasValue ? tahvilDto.DblTv : DBNull.Value),
            new SqlParameter("@strSaat", DBNull.Value),
            new SqlParameter("@dblAI", DBNull.Value),
            new SqlParameter("@dblAVCP", DBNull.Value),
            new SqlParameter("@isRefresh", value:0)
        ];
    }

    public SqlParameter[]? CreateTahvilRepoOzetStoredProcedureParameters(TahvilDto tahvilDto)
    {
        return
        [
            new SqlParameter("@dtmTarih", DBNull.Value),
            new SqlParameter("@strVL1", GetDbValue(tahvilDto.StrVl)),
            new SqlParameter("@strDAYS", GetDbValue(tahvilDto.StrDays)),
            new SqlParameter("@strPAKOD", DBNull.Value),
            new SqlParameter("@dblIR", tahvilDto.DblIr.HasValue ? tahvilDto.DblIr : DBNull.Value),
            new SqlParameter("@dblXR", tahvilDto.DblXr.HasValue ? tahvilDto.DblXr : DBNull.Value),
            new SqlParameter("@dblAR", tahvilDto.DblAr.HasValue ? tahvilDto.DblAr : DBNull.Value),
            new SqlParameter("@dblTV", tahvilDto.DblTv.HasValue ? tahvilDto.DblTv : DBNull.Value),
            new SqlParameter("@strSaat", tahvilDto.DtmTarih.ToString("HHmmss"))
        ];
    }

    public SqlParameter[]? CreateBolunmeStoredProcedureParameters(BolunmeDto bolunmeDto)
    {
        return
        [
            new SqlParameter("@strKod", bolunmeDto.StrKod),
            new SqlParameter("@strTarih", bolunmeDto.StrTarih.ToString("yyyy-MM-dd HH:mm:ss")),
            new SqlParameter("@dblBedelli", bolunmeDto.DblBedelli),
            new SqlParameter("@dblBedelsiz", bolunmeDto.DblBedelsiz),
            new SqlParameter("@dblTemettu", bolunmeDto.DblTemettu),
            new SqlParameter("@dblOran", bolunmeDto.DblOran)
        ];
    }

    public SqlParameter[] UpdateSembolIfEmptylStoredProcedureParameters(ForeksSembolDto sembollerDto)
    {
        return
        [
            new SqlParameter("@strKod", GetDbValue(sembollerDto.Sembol)),
            new SqlParameter("@strSocketId", GetDbValue(sembollerDto.SocketId)),
            new SqlParameter("@strMarketSector", GetDbValue(sembollerDto.MarketSector)),
            new SqlParameter("@strApiSecurityType", GetDbValue(sembollerDto.SecurityType)),
            new SqlParameter("@strExchange", GetDbValue(sembollerDto.Exchange)),
        ];
    }

    public async Task ExecuteStoredProcedureAsync(string storedProcedureName, SqlParameter[] parameters, string symbolName)
    {
        await using var connection = new SqlConnection(_configuration[$"ConnectionStrings:Foreks"]);

        if (connection.State != ConnectionState.Open)
        {
            await connection.OpenAsync();
        }

        await using var command = new SqlCommand(storedProcedureName, connection);
        command.CommandType = CommandType.StoredProcedure;

        command.Parameters.AddRange(parameters);

        try
        {
            await command.ExecuteNonQueryAsync();
            _logger.LogInformation($"{storedProcedureName} başarıyla çağrıldı.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Stored procedure hatası: {storedProcedureName}");
            await _notificationService.NotifyErrorAsync(storedProcedureName, $"Stored procedure hatası: Sembol : {symbolName} : {ex} , Hata Mesajı: {ex.Message}");
        }
        finally
        {
            await connection.CloseAsync();
        }
    }

    /// <summary>
    /// Stored procedure'ü çalıştırır ve sonuçları generic tip olarak döner
    /// </summary>
    public async Task<List<T>> ExecuteStoredProcedureAsync<T>(string procedureName, params DbParameter[] parameters) where T : class, new()
    {
        await using var connection = new SqlConnection(_configuration[$"ConnectionStrings:Foreks"]);
        var wasClosed = connection.State == ConnectionState.Closed;

        try
        {
            if (wasClosed)
                await connection.OpenAsync();

            await using var command = connection.CreateCommand();
            command.CommandText = procedureName;
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null)
                command.Parameters.AddRange(parameters);

            await using var reader = await command.ExecuteReaderAsync();

            return await MapDataReaderToList<T>(reader);
        }
        finally
        {
            if (wasClosed && connection.State != ConnectionState.Closed)
                await connection.CloseAsync();
        }
    }

    /// <summary>
    /// DataReader'dan generic liste oluşturur
    /// </summary>
    private async Task<List<T>> MapDataReaderToList<T>(DbDataReader reader) where T : class, new()
    {
        var list = new List<T>();
        var properties = typeof(T).GetProperties().Where(p => p.CanWrite).ToArray();

        while (await reader.ReadAsync())
        {
            var item = new T();

            foreach (var property in properties)
            {
                try
                {
                    // Column name'i ColumnAttribute varsa onunla, yoksa property name ile eşleştir
                    var columnName = property.GetCustomAttribute<ColumnAttribute>()?.Name ?? property.Name;

                    if (HasColumn(reader, columnName))
                    {
                        var value = reader[columnName];
                        if (value != null && value != DBNull.Value)
                        {
                            var propertyType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;

                            object convertedValue;

                            if (propertyType.IsEnum)
                            {
                                var intValue = Convert.ToInt32(value);
                                convertedValue = Enum.ToObject(propertyType, intValue);
                            }
                            else
                            {
                                convertedValue = Convert.ChangeType(value, propertyType);
                            }

                            property.SetValue(item, convertedValue);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log exception if needed
                    Console.WriteLine($"Error mapping property {property.Name}: {ex.Message}");
                }
            }

            list.Add(item);
        }

        return list;
    }


    /// <summary>
    /// DataReader'da belirtilen column'un var olup olmadığını kontrol eder
    /// </summary>
    private static bool HasColumn(DbDataReader reader, string columnName)
    {
        for (int i = 0; i < reader.FieldCount; i++)
        {
            if (reader.GetName(i).Equals(columnName, StringComparison.OrdinalIgnoreCase))
                return true;
        }
        return false;
    }
}