﻿using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.Definitions;
using Bigpara.Domain.Redis;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;


namespace Bigpara.Service.Hangfire.Jobs;

public class InsertSymbolClickCountRecurringJob : IRecurringJob
{
    private readonly IRedisCacheService _redisCacheService;
    private readonly ISembollerService _sembolService;
    private readonly IBorsaService _borsaService;
    private readonly ILogger<InsertSymbolClickCountRecurringJob> _logger;
    private readonly INotificationService _notificationService;


    public InsertSymbolClickCountRecurringJob
    (
        IRedisCacheService redisCacheService,
        ISembollerService sembolService,
        IBorsaService borsaService,
        ILogger<InsertSymbolClickCountRecurringJob> logger,
        INotificationService notificationService
    )
    {
        _redisCacheService = redisCacheService;
        _sembolService = sembolService;
        _borsaService = borsaService;
        _logger = logger;
        _notificationService = notificationService;
    }

    public string Name => "InsertSymbolClickCountRecurringJob";
    public IEnumerable<string> Crons => ["30 */5 * * *"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("InsertSymbolClickCountRecurringJob - Başladı ");
            await ProcessInsertSymbolClickCount();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
            await _notificationService.NotifyErrorAsync("InsertSymbolClickCountRecurringJob", exception.Message);
        }
    }

    public async Task ProcessInsertSymbolClickCount()
    {
        try
        {
            var redisDataList = _redisCacheService.GetTypedList<RedisReadCount>(CacheKeys.GetMostClickedStockKey);

            if (redisDataList != null && redisDataList.Any())
            {
                foreach (RedisReadCount redisReadCount in redisDataList)
                {
                    await _sembolService.InsertSymbolReadCount(new SymbolReadCount()
                    {
                        SEMBOLID = redisReadCount.Id,
                        CountDate = redisReadCount.CreatedDate,
                        ReadCount = redisReadCount.Daily,
                        CreateTime = redisReadCount.CreatedDate,
                        UpdateTime = redisReadCount.UpdatedDate
                    });
                }
            }

            _redisCacheService.Remove(CacheKeys.GetMostClickedStockKey);

            await GetMostClickedSymbolFromDb();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            await _notificationService.NotifyErrorAsync("En Çok Tıklanan Sembol Redis Persist", ex.Message);

            //TODO : Check
            //EmailHelper.SendEmail("En Çok Tıklanan Sembol Redis Persist", ex.Message);
        }
    }

    private async Task GetMostClickedSymbolFromDb()
    {
        try
        {
            var hisseList = await _borsaService.GetHisseList();

            foreach (Hisse hisse in hisseList)
            {
                var readCount = await _sembolService.GetSymbolReadCountsAllPeriods(hisse.SEMBOLID);
                _redisCacheService.AddTypedList(CacheKeys.GetMostClickedStockKey, new List<RedisReadCount>() { readCount });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            await _notificationService.NotifyErrorAsync("En Çok Tıklanan Sembol Db 'den Redis'e Persist", ex.Message);

            //TODO : Check
            //EmailHelper.SendEmail("En Çok Tıklanan Sembol Db 'den Redis'e Persist", ex.Message);
        }
    }
}
