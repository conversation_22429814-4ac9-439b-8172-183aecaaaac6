﻿using Bigpara.Domain;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.Definitions;
using Bigpara.Domain.Redis;
using System.Linq.Expressions;

namespace Bigpara.Service.Hangfire.Services.Interfaces
{
    public interface ISembollerService
    {
        Task<List<Semboller>> GetSembollersAsync();
        Task<List<T>> GetSembollersAsync<T>(Expression<Func<Semboller, bool>> predicate, Expression<Func<Semboller, T>> selector);
        Task<List<Semboller>> GetSembollersByPiyasaIdAsync(int id);
        Task<List<Semboller>> GrafikGunlukSembols(DateTime dateTime);
        Task<List<Endeks>> GetSembolByEndeks(string endeks);
        Task<List<Endeks>> GetEndeksList();
        Task<List<SembolEndeks>> GetSembolEndekses(string sembol);
        Task<Semboller> GetSembollerBySembolId(int sembolId);
        Task<List<Endeks>> GetSembolEndekslerListesi();
        Task<List<Semboller>> GetHisseSembollerByPiyasaIdList(int piyasaId);
        Task<RedisReadCount> GetSymbolReadCountsAllPeriods(int symbolId);
        Task<int> InsertSymbolReadCount(SymbolReadCount symbolReadCount);
        Task<int> UpdateSymbolReadCount(int sembolId, DateTime countDate);
        Task<SembollerEk> GetSembollerEkData(int sembolId);
        Task<List<SymbolCountSummary>> GetSembolReadCountSummaries();
        Task<KisaTanimSemboller> GetSymbolSummaryBySymbol(string sembol);
    }
}
