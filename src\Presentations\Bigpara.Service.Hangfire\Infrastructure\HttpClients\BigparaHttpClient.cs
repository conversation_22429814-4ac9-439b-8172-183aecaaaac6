﻿using Bigpara.Notifications;
using Newtonsoft.Json;
using System.Text;

namespace Bigpara.Service.Hangfire.Infrastructure.HttpClients;

public class BigparaHttpClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<BigparaHttpClient> _logger;
    private readonly string _username;
    private readonly string _password;
    private readonly INotificationService _notificationService;

    public BigparaHttpClient(
        IHttpClientFactory httpClientFactory,
        ILogger<BigparaHttpClient> logger,
        IConfiguration configuration,
        INotificationService notificationService)
    {
        _httpClient = httpClientFactory.CreateClient("ForeksCloudService");
        _logger = logger;

        _username = configuration["Foreks:Auth:WebUsername"];
        _password = configuration["Foreks:Auth:WebPassword"];

        SetBasicAuthentication();
        _notificationService = notificationService;
    }

    private void SetBasicAuthentication()
    {
        var credentials = $"{_username}:{_password}";
        var encodedCredentials = Convert.ToBase64String(Encoding.UTF8.GetBytes(credentials));
        _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", encodedCredentials);
    }

    public async Task<List<T>> FetchDataAsync<T>(string url)
    {
        try
        {
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<T>>(responseBody) ?? new List<T>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Veri çekilirken hata oluştu : {Url}", url);
            await _notificationService.NotifyErrorAsync("BigparaHttpClient", $"Veri çekilirken hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
            return new List<T>();
        }
    }

    public async Task<T?> FetchObjectAsync<T>(string url)
    {
        try
        {
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<T>(responseBody);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Veri çekilirken hata oluştu : {Url}", url);
            await _notificationService.NotifyErrorAsync("BigparaHttpClient", $"Veri çekilirken hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
            return default;
        }
    }

    public async Task<string?> PostAsync(string url, string body, string contentType = "application/x-www-form-urlencoded")
    {
        try
        {
            var content = new StringContent(body, Encoding.UTF8, contentType);

            var response = await _httpClient.PostAsync(url, content);
            response.EnsureSuccessStatusCode();

            return await response.Content.ReadAsStringAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "POST isteğinde hata oluştu: {Url}", url);
            await _notificationService.NotifyErrorAsync("BigparaHttpClient", $"POST isteğinde hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
            return null;
        }
    }
}