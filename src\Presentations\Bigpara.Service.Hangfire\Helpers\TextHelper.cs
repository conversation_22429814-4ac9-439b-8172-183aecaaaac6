﻿using Newtonsoft.Json.Linq;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

namespace Bigpara.Service.Hangfire.Helpers;

public static class TextHelper
{
    public static string RemoveSpecialCharacters(string text, params string[] chars)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            return string.Empty;
        }

        return chars.Aggregate(text, (current, c) => current.Replace(c, string.Empty));
    }

    public static string RemoveEscapeCharacters(string input)
    {
        string result = input;

        result = result.Replace("\\", "");
        result = result.Replace("\"", "");
        result = result.Replace("\a", "");
        result = result.Replace("\b", "");
        result = result.Replace("\f", "");
        result = result.Replace("\n", "");
        result = result.Replace("\r", "");
        result = result.Replace("\t", "");
        result = result.Replace("\v", "");
        result = result.Replace("\0", "");

        return result;
    }

    public static string CleanIllegalFileNameChars(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName))
        {
            return string.Empty;
        }

        var r = new Regex(string.Format("[{0}]", Regex.Escape(new string(Path.GetInvalidFileNameChars()))));
        return r.Replace(fileName, string.Empty);
    }

    public static string ConvertTextForReWrite(this string txt)
    {
        txt = txt ?? string.Empty;
        txt = txt.Trim();

        txt = txt.Replace("ş", "s");
        txt = txt.Replace("Ş", "s");
        txt = txt.Replace("İ", "i");
        txt = txt.Replace("I", "i");
        txt = txt.Replace("ı", "i");
        txt = txt.Replace("ö", "o");
        txt = txt.Replace("Ö", "o");
        txt = txt.Replace("ü", "u");
        txt = txt.Replace("Ü", "u");
        txt = txt.Replace("Ç", "c");
        txt = txt.Replace("ç", "c");
        txt = txt.Replace("ğ", "g");
        txt = txt.Replace("Ğ", "g");
        txt = txt.Replace(" ", "-");
        txt = txt.Replace("---", "-");
        txt = txt.Replace("?", "");
        txt = txt.Replace("/", "");
        txt = txt.Replace(".", "");
        txt = txt.Replace("'", "");
        txt = txt.Replace("#", "");
        txt = txt.Replace("%", "");
        txt = txt.Replace("&", "");
        txt = txt.Replace("*", "");
        txt = txt.Replace("!", "");
        txt = txt.Replace("@", "");
        txt = txt.Replace("+", "");

        // tüm harfleri küçült
        string encodedUrl = txt.ToLower();

        // & ile " " yer değiştirme
        encodedUrl = Regex.Replace(encodedUrl, @"\&+", "and");

        // " " karakterlerini silme
        encodedUrl = encodedUrl.Replace("'", "");

        // geçersiz karakterleri sil
        encodedUrl = Regex.Replace(encodedUrl, @"[^a-z0-9]", "-");

        // tekrar edenleri sil
        encodedUrl = Regex.Replace(encodedUrl, @"-+", "-");

        // karakterlerin arasına tire koy
        encodedUrl = encodedUrl.Trim('-');

        return encodedUrl;
    }

    public static string ToMoneyString(this object obj)
    {
        if (obj == null) return string.Empty;

        return string.Format("{0:N2}", obj);
    }

    public static string ToMoneyString(this object obj, int righDigit)
    {
        if (obj == null) return string.Empty;
        if (righDigit <= 0 || righDigit > 6) righDigit = 2;

        string format = "{0:N" + righDigit + "}";
        return string.Format(format, obj);
    }

    public static string ToMoneyString(this object obj, string format)
    {
        if (obj == null) return string.Empty;

        return string.Format(format, obj);
        //return string.Format("{0:#.00}", obj);
    }

    public static string FormatNumber(IFormattable sVal, Int32 nPrecision)
    {
        return string.Format("{0:N" + nPrecision + "}", sVal);
    }

    public static string CleanForUrl(this string text, bool toLowercase = true)
    {
        text = text.RemoveDiacritics();
        text = text.ToLowerInvariant();
        text = Regex.Replace(text, "[^a-z0-9]", " ", RegexOptions.IgnoreCase);
        text = Regex.Replace(text, @"\s+", " ");
        text = text.Trim();
        text = text.Replace(" ", "-");

        return text;
    }

    public static string RemoveDiacritics(this string text)
    {
        var srcEncoding = Encoding.UTF8;
        var destEncoding = Encoding.GetEncoding(1252); // Latin alphabet
        text = destEncoding.GetString(Encoding.Convert(srcEncoding, destEncoding, srcEncoding.GetBytes(text)));
        var normalizedString = text.Normalize(NormalizationForm.FormD);
        var result = new StringBuilder();
        foreach (var str in normalizedString)
        {
            if (!CharUnicodeInfo.GetUnicodeCategory(str).Equals(UnicodeCategory.NonSpacingMark))
            {
                result.Append(str);
            }
        }

        return result.ToString();
    }

    public static Dictionary<string, string> ToDictionary(this JObject jObject)
    {
        if (jObject == null)
        {
            return new Dictionary<string, string>();
        }

        var dict = new Dictionary<string, string>();

        foreach (var property in jObject.Properties())
        {
            // Use property.Value.ToString() instead of jObject[property]
            dict[property.Name] = property.Value?.ToString() ?? string.Empty;
        }

        return dict;
    }
}

