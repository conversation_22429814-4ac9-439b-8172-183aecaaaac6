﻿using Bigpara.Application.Contracts.Repositories.Matriks.Sembol;
using Bigpara.Application.Dtos.Matriks;
using Bigpara.Domain;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.Definitions;
using Bigpara.Domain.Redis;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Linq.Expressions;

namespace Bigpara.Persistence.Matriks.Sembol;

public class SembolRepository : ISembolRepository
{
    private readonly IMatriksDbContext _matriksDbContext;

    public SembolRepository(MatriksDbContext matriksDbContext)
    {
        _matriksDbContext = matriksDbContext;
    }

    private static List<SqlParameter> Parameters
    {
        get
        {
            return new List<SqlParameter>();
        }
    }

    public IQueryable<Semboller> Query(Expression<Func<Semboller, bool>> filter = null, Func<IQueryable<Semboller>, IOrderedQueryable<Semboller>> orderBy = null)
    {
        IQueryable<Semboller> query = _matriksDbContext.Semboller;

        if (filter != null)
        {
            query = query.Where(filter);
        }

        if (orderBy != null)
        {
            query = orderBy(query);
        }

        return query;
    }

    public async Task<List<Semboller>> GetSembolsGrafikGunluks(DateTime dateTime)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("tarih", dateTime.Date)
        };
        
        var sembol = await _matriksDbContext.ExecuteStoredProcedureAsync<Semboller>("bp.pGetGrafikGunlukSembols", parameters.ToArray());

        return sembol;

    }

    public async Task<Semboller> GetSembollerById(int sembolId)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembolId", sembolId));

        return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<Semboller>("pGetYuzeyselBySembolId", parameters.ToArray());
    }

    public async Task<KisaTanimSemboller> GetSymbolSummaryBySymbol(string sembol)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembol", sembol));

        return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<KisaTanimSemboller>("bp.pGetSembolDetailBySembol", parameters.ToArray());
    }

    public async Task<List<Endeks>> GetEndeksList()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<Endeks>("bp.pGetEndeksListesi", Parameters.ToArray()) ??
               new List<Endeks>();
    }

    public async Task<List<Endeks>> GetSembolByEndeks(string endeks)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("endeks", endeks));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<Endeks>("bp.pGetSembolByEndeks", parameters.ToArray()) ??
               new List<Endeks>();
    }

    public async Task<List<Endeks>> GetSembolEndekslerListesi()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<Endeks>("bp.pGetSembolEndekslerListesi", Parameters.ToArray()) ??
               new List<Endeks>();
    }

    public async Task<List<SembolEndeks>> GetSembolEndekses(string sembol)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembol", sembol));


        return await _matriksDbContext.ExecuteStoredProcedureAsync<SembolEndeks>("bp.pGetSembolEndeksler", parameters.ToArray()) ??
               new List<SembolEndeks>();
    }

    public async Task<Semboller> GetSembollerBySembolId(int sembolId)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembolId", sembolId));

        return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<Semboller>("bp.pGetSembolById", parameters.ToArray());
    }

    public async Task<List<Semboller>> GetHisseSembollerByPiyasaIdList(int piyasaId)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("piyasaId", piyasaId));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<Semboller>("bp.pGetHisseSembolListListesi", parameters.ToArray()) ??
               new List<Semboller>();
    }

    public async Task<int> InsertSymbolReadCount(SymbolReadCount symbolReadCount)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("symbolId",symbolReadCount.SEMBOLID),
            new SqlParameter("readCount",symbolReadCount.ReadCount),
            new SqlParameter("countDate",symbolReadCount.CountDate),
            new SqlParameter("createTime",symbolReadCount.CreateTime),
            new SqlParameter("updateTime",symbolReadCount.UpdateTime)
        };

        return await _matriksDbContext.ExecuteNonQueryAsync("bp.spInsertSymbolReadCount", parameters.ToArray());
    }

    public async Task<RedisReadCount> GetSymbolReadCountsAllPeriods(int symbolId)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("symbolId", symbolId)
        };

        return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<RedisReadCount>("bp.pGetSymbolReadCountsAllPeriods", parameters.ToArray());
    }

    public async Task<int> UpdateSymbolReadCount(int sembolId, DateTime countDate)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("symbolId", sembolId),
            new SqlParameter("countDate", countDate),
            new SqlParameter("updateTime", DateTime.Now),
        };

        return await _matriksDbContext.ExecuteNonQueryAsync("bp.spUpdateSymbolReadCount", parameters.ToArray());
    }

    public async Task<List<YatirimAraclari>> GetYatirimAraclariList()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<YatirimAraclari>("bp.pYatirimAraclarininGetirileri", Parameters.ToArray()) ??
               new List<YatirimAraclari>();
    }

    public async Task<SembollerEk> GetSembollerEkData(int sembolId)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("sembolId", sembolId)
        };

        return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<SembollerEk>("bp.pGetSembollerEkDataBySembol", parameters.ToArray()) ?? new SembollerEk();
    }

    public async Task<List<SymbolCountSummary>> GetSembolReadCountSummaries()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SymbolCountSummary>("bp.pGetSymbolReadCounts", Parameters.ToArray()) ??
               new List<SymbolCountSummary>();
    }

    public  Task<List<Semboller>> GetSembolHisseListesi()
    {
        var data= _matriksDbContext.FromSql<Semboller>("bp.pGetHisseListesi");
        return Task.FromResult(data.ToList());
    }

    public async Task<List<Semboller>> GetSembollerList()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<Semboller>("bp.pGetSembolListesi", Parameters.ToArray()) ??
           new List<Semboller>();
        
    }

    public async Task<Semboller> GetSembollerByCode(string code)
    {
       return await  _matriksDbContext.Semboller.FirstOrDefaultAsync(s => s.Sembol == code);
    }
}
