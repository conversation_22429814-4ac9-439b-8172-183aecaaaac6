﻿using Bigpara.Service.Realtime.Hubs;
using Bigpara.Service.Realtime.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Hosting;
using StackExchange.Redis;

var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();

builder.Services.AddMemoryCache();
// Add services to the container.
// Redis bağlantısı
builder.Services.AddSingleton<IConnectionMultiplexer>(sp =>
{
    return ConnectionMultiplexer.Connect(builder.Configuration["ConnectionStrings:Redis"]);
});

builder.Services.AddSignalR()
.AddStackExchangeRedis(builder.Configuration["ConnectionStrings:Redis"]);


builder.Services.AddSingleton<IRedisSubscriptionService, RedisSubscriptionService>();
builder.Services.AddHostedService<RedisDataListenerService>();

// CORS politikası ekle
builder.Services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy",
        builder => builder
            .WithOrigins("http://localhost:5035","http://master-hangfire-test.bigpara.com", "https://master-hangfire-test.bigpara.com", "http://hangfire-test.bigpara.com", "https://hangfire-test.bigpara.com")
            .AllowAnyMethod()
            .SetIsOriginAllowed((host) => true)
            .AllowAnyHeader()
            .AllowCredentials()
    );
});

builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();
var app = builder.Build();

app.MapDefaultEndpoints();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();
app.UseCors("CorsPolicy");
app.UseAuthorization();


app.MapControllers();
app.MapHub<TradeHub>("/tradehub");

await app.RunAsync();
