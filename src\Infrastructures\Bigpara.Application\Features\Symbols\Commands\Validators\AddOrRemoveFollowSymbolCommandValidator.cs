﻿using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Features.Symbols.Commands.Validators
{
    public class AddOrRemoveFollowSymbolCommandValidator:AbstractValidator<AddOrRemoveFollowSymbolCommand>
    {
        public AddOrRemoveFollowSymbolCommandValidator()
        {
            RuleFor(x => x.Symbol)
                .NotEmpty().WithMessage("Symbol is required.");

            RuleFor(x => x.Operation)
                .GreaterThan(1).WithMessage("Operation is not valid.");
        }
    }
}
