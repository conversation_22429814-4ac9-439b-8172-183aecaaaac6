using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Bigpara.Cache.Memory.Services;
using Microsoft.Extensions.Caching.Memory;
using Moq;
using Xunit;

namespace Bigpara.Cache.Memory.Tests.Services
{
    public class MemoryCacheServiceTests
    {
        private readonly MemoryCacheService _service;
        private readonly IMemoryCache _memoryCache;

        public MemoryCacheServiceTests()
        {
            _memoryCache = new MemoryCache(new MemoryCacheOptions());
            _service = new MemoryCacheService(_memoryCache);
        }

        [Fact]
        public void Set_And_Get_Should_Return_Same_Value()
        {
            var key = "test-key";
            var value = "test-value";

            _service.Set(key, value);
            var result = _service.Get<string>(key);

            Assert.Equal(value, result);
        }

        [Fact]
        public void Get_With_Acquire_Should_Cache_And_Return_Value()
        {
            var key = "acquire-key";
            var value = "acquire-value";
            int acquireCallCount = 0;

            string Acquire() { acquireCallCount++; return value; }

            var result1 = _service.Get(key, 1, Acquire);
            var result2 = _service.Get<string>(key, 1, () => throw new Exception("Should not be called"));

            Assert.Equal(value, result1);
            Assert.Equal(value, result2);
            Assert.Equal(1, acquireCallCount);
        }

        [Fact]
        public async Task GetAsync_With_Acquire_Should_Cache_And_Return_Value()
        {
            var key = "async-key";
            var value = "async-value";
            int acquireCallCount = 0;

            async Task<string> AcquireAsync() { acquireCallCount++; await Task.Delay(1); return value; }

            var result1 = await _service.GetAsync(key, 1, AcquireAsync);
            var result2 = await _service.GetAsync<string>(key, 1, () => throw new Exception("Should not be called"));

            Assert.Equal(value, result1);
            Assert.Equal(value, result2);
            Assert.Equal(1, acquireCallCount);
        }

        [Fact]
        public void IsSet_Should_Return_True_If_Key_Exists()
        {
            var key = "isset-key";
            _service.Set(key, 123);
            Assert.True(_service.IsSet(key));
        }

        [Fact]
        public void IsSet_Should_Return_False_If_Key_Does_Not_Exist()
        {
            Assert.False(_service.IsSet("nonexistent-key"));
        }

        [Fact]
        public void Remove_Should_Delete_Key()
        {
            var key = "remove-key";
            _service.Set(key, 42);
            _service.Remove(key);
            Assert.False(_service.IsSet(key));
        }

        [Fact]
        public void Get_With_Out_Should_Return_Success_Status()
        {
            var key = "out-key";
            _service.Set(key, "out-value");
            var result = _service.Get<string>(key, out bool isSucceeded);
            Assert.True(isSucceeded);
            Assert.Equal("out-value", result);
        }

        [Fact]
        public void Get_Multiple_Keys_Should_Return_Existing_Values()
        {
            _service.Set("k1", 1);
            _service.Set("k2", 2);
            var keys = new List<string> { "k1", "k2", "k3" };
            var dict = _service.Get<int>(keys);
            Assert.Equal(2, dict.Count);
            Assert.Equal(1, dict["k1"]);
            Assert.Equal(2, dict["k2"]);
        }

        [Fact]
        public void Update_Should_Overwrite_Existing_Value()
        {
            var key = "update-key";
            _service.Set(key, 10);
            _service.Update(key, 20);
            Assert.Equal(20, _service.Get<int>(key));
        }

        [Fact]
        public void Add_Should_Set_Value()
        {
            var key = "add-key";
            _service.Add(key, "added");
            Assert.Equal("added", _service.Get<string>(key));
        }

        [Fact]
        public void Add_With_Timeout_Should_Set_Value()
        {
            var key = "add-timeout-key";
            _service.Add(key, "timeout", TimeSpan.FromSeconds(1));
            Assert.Equal("timeout", _service.Get<string>(key));
        }

        [Fact]
        public void Add_With_CacheTime_Should_Set_Value()
        {
            var key = "add-cachetime-key";
            _service.Add(key, "cachetime", 1);
            Assert.Equal("cachetime", _service.Get<string>(key));
        }

        [Fact]
        public void RemoveByPattern_Should_Throw_NotSupportedException()
        {
            Assert.Throws<NotSupportedException>(() => _service.RemoveByPattern("pattern"));
        }

        [Fact]
        public void Clear_Should_Throw_NotSupportedException()
        {
            Assert.Throws<NotSupportedException>(() => _service.Clear());
        }

        [Fact]
        public void Clear_By_Key_Should_Remove_Key()
        {
            var key = "clear-key";
            _service.Set(key, 99);
            _service.Clear(key);
            Assert.False(_service.IsSet(key));
        }
    }
}
