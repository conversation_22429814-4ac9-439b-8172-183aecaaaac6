﻿using Bigpara.Domain.Matriks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Report;

public class GetSeansRaporu15DkBist0100EndeksRecurringJob : IRecurringJob
{
    private readonly ISeansRaporlariService _seansRaporlariService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<GetSeansRaporu15DkBist0100EndeksRecurringJob> _logger;

    public GetSeansRaporu15DkBist0100EndeksRecurringJob
    (
        ISeansRaporlariService seansRaporlariService,
        INotificationService notificationService,
        ILogger<GetSeansRaporu15DkBist0100EndeksRecurringJob> logger
    )
    {
        _seansRaporlariService = seansRaporlariService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "GetSeansRaporu15DkBist0100EndeksRecurringJob";
     public IEnumerable<string> Crons => ["*/15 9-17 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("GetSeansRaporu15DkBist0100EndeksRecurringJob - Başladı ");
            await ProcessGetSeansRaporu15DkBist0100Endeks();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    /// <summary>
    /// 10 dk gun ici bist 30 endeks göstergesi bilgisi aliniyor
    /// </summary>
    public async Task ProcessGetSeansRaporu15DkBist0100Endeks()
    {
        if (!_seansRaporlariService.IsActive())
            return;

        try
        {
            const string pattern = "Endeks {0} ile dünkü kapanışının %{1} {2}. İşlem hacmi {3} milyon TL.";
            var seansRaporuYuzeysel = await _seansRaporlariService.GetSeansRaporuBistAcilisKapanis();
            if (seansRaporuYuzeysel == null) return;
            var now = new DateTime(seansRaporuYuzeysel.TARIH.Year, seansRaporuYuzeysel.TARIH.Month, seansRaporuYuzeysel.TARIH.Day, 12, 30, 0);
            var result = string.Format(pattern, seansRaporuYuzeysel.KAPANIS, seansRaporuYuzeysel.YUZDEDEGISIM, seansRaporuYuzeysel.YUZDEDEGISIM > 0 ? "altında" : "üstünde", seansRaporuYuzeysel.TARIH < now ? (seansRaporuYuzeysel.HACIM1 / 1000000).ToMoneyString("{0:N0}") : (seansRaporuYuzeysel.HACIM / 1000000).ToMoneyString("{0:N0}"));

            var yon = 0;
            if (seansRaporuYuzeysel.YUZDEDEGISIM > 0)
            {
                yon = 1;
            }
            else if (seansRaporuYuzeysel.YUZDEDEGISIM < 0)
            {
                yon = -1;
            }
            var seansRaporu = new SeansRaporu
            {
                HisseCumle = result,
                Yon = yon,
                HisseAdi = seansRaporuYuzeysel.SEMBOL,
                EklenmeTarihi = DateTime.Now
            };

            await _seansRaporlariService.CreateAndCacheSeansRaporu(seansRaporu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            //EmailHelper.SendEmail("GetSeansRaporu15DkBist0100Endeks", ex.Message);
            await _notificationService.NotifyErrorAsync($"GetSeansRaporu15DkBist0100Endeks", ex.Message);
        }
    }
}
