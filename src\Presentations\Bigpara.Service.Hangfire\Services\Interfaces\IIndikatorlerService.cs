﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Service.Hangfire.Services.Interfaces;

public interface IIndikatorlerService
{
    Task<Indikatorler> GetIndikatorlers(string sembol, DateTime dateTime);
    Task<List<Indikatorler>> GetIndikatorlers(DateTime dateTime);
    Task<int> CreateOrUpdate(Indikatorler indikator);
    // List<Indikatorler> GetIndikatorlersByTarih(DateTime dateTime);
    Task<Indikatorler> GetIndikatorbySembol(string sembol);
    //List<Indikatorler> GetIndikatorbyTarihSembol(string sembol, DateTime dateTime);
    Task<List<Indikatorler>> GetOtomatikTeknikYorumIndikatorSembolbyTarih(string sembol, DateTime dateTime);
}
