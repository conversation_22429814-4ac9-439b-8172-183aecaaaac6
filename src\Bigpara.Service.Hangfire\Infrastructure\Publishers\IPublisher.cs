﻿namespace Bigpara.Service.Hangfire.Infrastructure.Publishers
{
    public interface IPublisher
    {
        Task PublishSymbolDataAsync(string symbol, Dictionary<string, string> data);
        Task PublishSymbolDetailAsync(string symbol, Dictionary<string, string> detail);
        Task PublishAllSymbolsAsync(List<Dictionary<string, string>> data);
        Task PublishSymbolsByTypeAsync(string type, List<Dictionary<string, string>> symbols);
    }
}
