﻿using Bigpara.Application.Contracts.Repositories.Matriks.Piyasalar;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Matriks;
using Bigpara.Persistence;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Services.Matriks.Piyasalar
{
    public class PiyasaRepository : IPiyasaRepository
    {
        private const string CachekeyAllPiyasalar = "bigpara.piyasalar.all";
        private readonly IMatriksDbContext _matriksDbContext;
        private readonly ICacheService _cacheService;
        public PiyasaRepository(IMatriksDbContext matriksDbContext, ICacheService cacheService)
        {
            _matriksDbContext = matriksDbContext;
            _cacheService = cacheService;
        }
        public async Task<List<Piyasa>> GetAll()
        {
            var cachedData=await _cacheService.GetAsync<List<Piyasa>>(CachekeyAllPiyasalar,int.MaxValue, async () => await _matriksDbContext.Piyasalar.ToListAsync());

            return cachedData;
        }

        public async Task<Piyasa> GetById(byte piyasaId)
        {
            if (piyasaId <= 0)
            {
                return null;
            }

            var cachedData = await GetAll();

            if(cachedData == null || !cachedData.Any())
            {
                return null;
            }

            return cachedData.FirstOrDefault(p => p.PIYASAID == piyasaId);
        }
    }
}
