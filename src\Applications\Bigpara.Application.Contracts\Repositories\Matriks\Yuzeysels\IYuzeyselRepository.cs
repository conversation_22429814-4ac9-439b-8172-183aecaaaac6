﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Application.Contracts.Repositories.Matriks.Yuzeysels;

public interface IYuzeyselRepository
{
    Task<Yuzeysel> GetById(int sembolId);
    Task<Yuzeysel> GetBySembol(string sembol);
    Task<Yuzeysel> gunlukOzet();
    Task<Yuzeysel> GetYuzeyselBySembolTarih(string sembolId, DateTime tarih);
    Task<List<Yuzeysel>> GetBySembols(string sembols);
    Task<List<Yuzeysel>> GetPerformanceBySembols(string sembols);
}
