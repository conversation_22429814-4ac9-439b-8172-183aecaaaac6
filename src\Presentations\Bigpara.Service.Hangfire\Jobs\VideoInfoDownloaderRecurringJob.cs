﻿using Bigpara.Domain.Bigpara;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;


namespace Bigpara.Service.Hangfire.Jobs;

public class VideoInfoDownloaderRecurringJob : IRecurringJob
{
    //private readonly IVideoService _videoService;
    private readonly BigparaHttpClient _bigparaHttpClient;
    private readonly ISystemSettingsService _systemSettingsService;
    private readonly ILogger<VideoInfoDownloaderRecurringJob> _logger;
    private readonly INotificationService _notificationService;

    public VideoInfoDownloaderRecurringJob
    (
        //IVideoService videoService,
        ILogger<VideoInfoDownloaderRecurringJob> logger,
        BigparaHttpClient bigparaHttpClient,
        ISystemSettingsService systemSettingsService,
        INotificationService notificationService)
    {
        //_videoService = videoService;
        _logger = logger;
        _bigparaHttpClient = bigparaHttpClient;
        _systemSettingsService = systemSettingsService;
        _logger = logger;
        _notificationService = notificationService;
    }

    public string Name => "VideoInfoDownloaderRecurringJob";
     public IEnumerable<string> Crons => ["0 0 8 * * ?"];
    public async Task ExecuteAsync()
    {
        var streamDictionary = new Dictionary<int, string>()
            {
                { 0,  (await _systemSettingsService.GetSystemSettingsValueFromCacheAsync("FileConfiguration", "VideoMetaDataDownloadURL")).Value },
                { 1, (await _systemSettingsService.GetSystemSettingsValueFromCacheAsync("FileConfiguration", "VideoMetaDataDownloadVarantURL")).Value }
            };

        foreach (KeyValuePair<int, string> stream in streamDictionary)
        {
            await ProcessGetVideos(stream.Key, stream.Value);
        }
    }

    public async Task ProcessGetVideos(int categoryId, string streamUrl)
    {
        if (string.IsNullOrWhiteSpace(streamUrl))
        {
            return;
        }

        int index = 0;
        var list = _bigparaHttpClient.FetchDataAsync<VideoJson>(streamUrl).GetAwaiter().GetResult();

        if (list != null && list.Count > 0)
        {
            foreach (var item in list)
            {
                index++;
                try
                {
                    var _categoryName = "";
                    if (item.Ancestors.Count > 0)
                    {
                        _categoryName = item.Ancestors[item.Ancestors.Count - 1].IxName;
                    }
                    var videoItem = new Video()
                    {
                        CategoryColor = string.Empty,
                        CategoryId = categoryId,
                        CategoryName = _categoryName,
                        CreateDate = item.CreatedDate,
                        CustomListId = 0,
                        DocumentTypes = DocumentTypes.Video,
                        DomainID = 0,
                        Duration = item.Duration,
                        DurationText = (((float)item.Duration) / ((float)60)).ToString("00.00", System.Globalization.CultureInfo.InvariantCulture),
                        EmbedUrl = (await _systemSettingsService.GetSystemSettingsValueFromCacheAsync("BigparaJobs", "VideoEmbedUrl")).Value.ToString().Replace("{0}", item.IId.ToString()),
                        Explanation = item.Text,
                        IFrameCode = string.Empty,
                        Id = item.IId,
                        IsOutVideo = false,
                        IsSeeding = false,
                        MetaInfo = new MetaInfo() { Title = item.Title, Keywords = String.Join(",", item.Tags), Description = item.Text },
                        PlaylistWebUrl = string.Empty,
                        PrerollAd = string.Empty,
                        RowIdx = index,
                        SeedingUrl = string.Empty,
                        SeoUrl = string.Empty,
                        ShortDescription = item.Description,
                        //Status = (byte)(item.Status.Trim().Equals("Active") ? 1 : 0),
                        Status = (byte)(item.Status.Trim().Equals("0") ? 1 : 0),
                        Tags = string.Join(",", item.Tags),
                        ThumbnailUrl = await ImageHelper.GetImageUrl(item.Files, _systemSettingsService),
                        Title = item.Title,
                        TotalCommentCount = 0,
                        TotalImpression = 0,
                        TotalLikeCount = 0,
                        VideoID = item.IId,
                        VideoUrl = string.Empty,
                        WebUrl = (await _systemSettingsService.GetSystemSettingsValueFromCacheAsync("BigparaJobs", "VideoWebUrl")).Value.ToString().Replace("{0}", item.Url)
                    };

                    videoItem.SeoUrl = item.Title.ConvertTextForReWrite();
                   // await _videoService.CreateOrUpdate(videoItem);

                    if (item.IId == 0)
                        continue;
                }
                catch (Exception exp)
                {
                    _logger.LogError(exp.Message);
                    // TODO : Check
                    //EmailHelper.SendEmail("Video", exp.ToString());
                    await _notificationService.NotifyErrorAsync("Video", exp.ToString());
                }
            }
        }
    }
}
