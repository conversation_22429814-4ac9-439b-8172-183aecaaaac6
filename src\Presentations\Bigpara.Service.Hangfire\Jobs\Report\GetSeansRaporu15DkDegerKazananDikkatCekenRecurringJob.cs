﻿using Bigpara.Domain.Matriks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Report;

public class GetSeansRaporu15DkDegerKazananDikkatCekenRecurringJob : IRecurringJob
{
    private readonly ISeansRaporlariService _seansRaporlariService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<GetSeansRaporu15DkDegerKazananDikkatCekenRecurringJob> _logger;

    public GetSeansRaporu15DkDegerKazananDikkatCekenRecurringJob
    (
        ISeansRaporlariService seansRaporlariService,
        INotificationService notificationService,
        ILogger<GetSeansRaporu15DkDegerKazananDikkatCekenRecurringJob> logger
    )
    {
        _seansRaporlariService = seansRaporlariService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "GetSeansRaporu15DkDegerKazananDikkatCekenRecurringJob";
     public IEnumerable<string> Crons => ["*/15 9-17 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("GetSeansRaporu15DkDegerKazananDikkatCekenRecurringJob - Başladı ");
            await ProcessGetSeansRaporu15DkDegerKazananDikkatCeken();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    /// <summary>
    /// 15 dk gun ici top 3  değer kaybeden hisseler aliniyor
    /// </summary>
    public async Task ProcessGetSeansRaporu15DkDegerKazananDikkatCeken()
    {
        if (!_seansRaporlariService.IsActive())
            return;

        try
        {
            const string pattern = "{0} değer artışı ile dikkat çeken hisseler arasında.";
            var data = await _seansRaporlariService.GetSeansRaporu15DkDegerKazananDikkatCeken();

            var semboller = string.Join(",", data.Select(r => r.ACIKLAMA).ToList());

            var result = string.Format(pattern, semboller);

            var seansRaporuYuzeysel = data.FirstOrDefault();
            if (seansRaporuYuzeysel == null) return;
            var seansRaporu = new SeansRaporu
            {
                HisseCumle = result,
                Yon = 1,
                HisseAdi = string.Empty,
                EklenmeTarihi = DateTime.Now
            };

            await _seansRaporlariService.CreateAndCacheSeansRaporu(seansRaporu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            //EmailHelper.SendEmail("GetSeansRaporu10DkIslemHacmi", ex.Message);
            await _notificationService.NotifyErrorAsync($"GetSeansRaporu15DkDegerKazananDikkatCeken", ex.Message);
        }
    }
}
