﻿using Bigpara.Application.Contracts.Repositories.Quark.News;
using Bigpara.Application.Dtos;
using Bigpara.Application.Dtos.Quark;
using Bigpara.Cache.Interfaces;
using Bigpara.External.Quark.Extensions;
using Bigpara.External.Quark.Filters;
using Bigpara.External.Quark.Infrastructure;
using Quark.Models.Entities;

namespace Bigpara.External.Quark
{
    public class NewsSearchRepository : INewsSearchRepository
    {
        private const int DefaultCacheTime = 10;
        private const string SearchCacheKeyTemplate = "search.newssearch.query-{0}.fields-{1}.operators-{2}.types-{3}.sortField-{4}.sortOrder-{5}.skip-{6}.top-{7}.rangeFilter-{8}.writerId-{9}.year-{10}.category-{11}.returnFields-{12}.withAggrigation-{13}.withDefaultData-{14}.exactResult-{15}.prefixSearch-{16}-link-{17}-notInTags-{18}";

        private readonly ICacheService _cacheService;
        private readonly IQuarkRepository<SuperContent, SearchResult> _quarkRepository;

        public NewsSearchRepository(ICacheService cacheService, IQuarkRepository<SuperContent, SearchResult> quarkRepository)
        {
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _quarkRepository = quarkRepository ?? throw new ArgumentNullException(nameof(quarkRepository));
            _quarkRepository.BaseApiUrl = "/search";
        }

        public async Task<PaginatedItems<NewsDto>> Search(string keyword, string? sortField = null, int sortOrder = -1,int pageIndex=0, int pageSize = 20)
        {
            var filter = new SearchFilter() { Keyword = keyword, Types = "Article,NewsVideo,NewsPhotoGallery,Column", PageIndex = pageIndex, PageSize = pageSize, WithDefaultData = string.IsNullOrEmpty(keyword) };
            if (filter == null) throw new ArgumentNullException(nameof(filter));

            var queryString = BuildSearchQueryString(filter);
            var oDataFilter = new ODataFilter
            {
                QueryString = queryString,
            };

            var cacheKey = string.Format(
                SearchCacheKeyTemplate,
                filter.Keyword,
                filter.Fields,
                filter.Operators,
                filter.Types,
                filter.SortField,
                filter.SortOrder,
                filter.PageIndex,
                filter.PageSize,
                null,
                filter.WriterId,
                filter.Year,
                filter.Category,
                filter.ReturnFields ?? string.Empty,
                filter.WithAggrigation,
                filter.WithDefaultData,
                filter.ExactResult,
                filter.PrefixSearch,
                filter.Links,
                filter.NotInTags
            );

            return await _cacheService.GetAsync(cacheKey, DefaultCacheTime, async () =>
            {
                var data = await Task.Run(() => _quarkRepository.GetDtoByFilter(oDataFilter));
                var newsDtos = data.List.Select(x => x.ToNewsDto()).ToList();
                return new PaginatedItems<NewsDto>(filter.PageIndex, filter.PageSize, data.Count, newsDtos);
            });
        }

    
        /// <summary>
        /// Builds the search query string based on the provided request.
        /// </summary>
        private static string BuildSearchQueryString(SearchFilter request)
        {
            var skip = request.PageIndex > 0 ? (request.PageIndex - 1) * request.PageSize : 0;
            var top = request.PageSize > 0 ? request.PageSize : 10;

            var queryParts = new List<string>
                {
                    $"q={Uri.EscapeDataString(request.Keyword ?? string.Empty)}",
                    $"so={request.SortOrder}",
                    $"s={skip}",
                    $"l={top}",
                    $"withAgg={request.WithAggrigation.ToString().ToLowerInvariant()}",
                    $"withDefaultData={request.WithDefaultData.ToString().ToLowerInvariant()}",
                    $"exactResult={request.ExactResult.ToString().ToLowerInvariant()}",
                    $"prefixSearch={request.PrefixSearch.ToString().ToLowerInvariant()}"
                };

            if (!string.IsNullOrWhiteSpace(request.Fields))
                queryParts.Add($"f={Uri.EscapeDataString(request.Fields)}");

            if (!string.IsNullOrWhiteSpace(request.Operators))
                queryParts.Add($"o={Uri.EscapeDataString(request.Operators)}");

            if (!string.IsNullOrWhiteSpace(request.Types))
                queryParts.Add($"t={Uri.EscapeDataString(request.Types)}");

            if (!string.IsNullOrWhiteSpace(request.SortField))
                queryParts.Add($"sf={Uri.EscapeDataString(request.SortField)}");

            if (!string.IsNullOrWhiteSpace(request.WriterId))
                queryParts.Add($"wr={Uri.EscapeDataString(request.WriterId)}");

            if (!string.IsNullOrWhiteSpace(request.Year))
                queryParts.Add($"yr={Uri.EscapeDataString(request.Year)}");

            if (!string.IsNullOrWhiteSpace(request.Category))
                queryParts.Add($"path={Uri.EscapeDataString(request.Category)}");

            if (!string.IsNullOrWhiteSpace(request.ReturnFields))
                queryParts.Add($"finc={Uri.EscapeDataString(request.ReturnFields)}");

            if (!string.IsNullOrWhiteSpace(request.ContentTag))
                queryParts.Add($"ct={Uri.EscapeDataString(request.ContentTag)}");

            if (!string.IsNullOrWhiteSpace(request.NotInTags))
                queryParts.Add($"notInTags={Uri.EscapeDataString(request.NotInTags)}");

            if (!string.IsNullOrWhiteSpace(request.Links))
                queryParts.Add($"links={Uri.EscapeDataString(request.Links)}");

            return string.Join("&", queryParts);
        }
    }
}
