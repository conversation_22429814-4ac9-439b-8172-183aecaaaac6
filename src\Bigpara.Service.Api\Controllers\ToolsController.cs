﻿using Bigpara.Application.Features.Tools.Queries;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Bigpara.Service.Api.Controllers
{
    [Route("api/tools")]
    [ApiController]
    public class ToolsController : ControllerBase
    {
        private readonly IMediator _mediator;
        public ToolsController(IMediator mediator)
        {
            _mediator = mediator;
        }
        [HttpGet("exchange")]
        public async Task<IActionResult> Exchange(GetExchangeRatesQuery query)
        {
            var result= await _mediator.Send(query);

            return Ok(result);
        }
    }
}
