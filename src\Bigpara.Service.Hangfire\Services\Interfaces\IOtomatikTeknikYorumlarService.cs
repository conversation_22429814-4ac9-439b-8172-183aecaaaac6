﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Service.Hangfire.Services.Interfaces;

public interface IOtomatikTeknikYorumlarService
{
    Task<List<OtomatikTeknikYorumlar>> GetOtomatikTeknikYorumlars(string sembol, DateTime dateTime);
    Task<List<OtomatikTeknikYorumlar>> GetOtomatikTeknikYorumlars(string sembol);
    Task<int> CreateOrUpdateOtomatikTeknikYorumlar(OtomatikTeknikYorumlar otomatikTeknikYorumlar);
}
