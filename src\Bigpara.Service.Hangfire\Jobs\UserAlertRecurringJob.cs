﻿using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs;

public class UserAlertRecurringJob : IRecurringJob
{
    private readonly IBorsaService _borsaService;
    private readonly IUserService _userService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<UserAlertRecurringJob> _logger;

    public UserAlertRecurringJob
    (
        IBorsaService borsaService,
        IUserService userService,
        ILogger<UserAlertRecurringJob> logger,
        INotificationService notificationService
    )
    {
        _borsaService = borsaService;
        _userService = userService;
        _logger = logger;
        _notificationService = notificationService;
    }

    public string Name => "UserAlertRecurringJob";
    public IEnumerable<string> Crons => ["* * * * *"];

    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("UserAlertRecurringJob - Başladı ");
            await ProcessUserAlert();

        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    public async Task ProcessUserAlert()
    {
        try
        {
            int totalCount;
            int pageSize = 5000;
            int pageIndex = 1;
            _userService.GetUserList(pageIndex, pageSize, out totalCount);
            while (pageIndex * pageSize < totalCount)
            {
                var userList = _userService.GetUserList(pageIndex, pageSize, out totalCount);

                foreach (var user in userList)
                {
                    int id = await _borsaService.UserAlertInstanceAlert(user.Id);
                }
                pageIndex++;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: Hata: {ex} , Hata Mesajı: {ex.Message}");
        }
    }
}
