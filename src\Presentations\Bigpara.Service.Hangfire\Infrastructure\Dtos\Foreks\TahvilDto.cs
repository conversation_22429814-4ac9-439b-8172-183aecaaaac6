﻿using Newtonsoft.Json;

namespace Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;

public class TahvilDto
{
    [JsonProperty("Code")]
    public string? StrMat { get; set; }

    [JsonProperty("LegacyCode")]
    public string? LegacyCode { get; set; }

    public DateTime DtmTarih { get; set; }

    [JsonProperty("DateTime")]
    public long UnixDate { get; set; }

    [JsonProperty("Valor")]
    public string? StrVl { get; set; }

    [JsonProperty("Last")]
    public decimal? DblAv { get; set; }

    [JsonProperty("LastRate")]
    public decimal? DblSy { get; set; }

    [JsonProperty("CompoundRate")]
    public decimal? DblCy { get; set; }

    [JsonProperty("TotalTurnover")]
    public decimal? DblTv { get; set; }

    [JsonProperty("DTE")]
    public string? StrDays { get; set; }

    [JsonProperty("Low")]
    public decimal? DblIr { get; set; }

    [JsonProperty("High")]
    public decimal? DblAr { get; set; }

    [JsonProperty("VWAP")]
    public decimal? DblXr { get; set; }

    [JsonProperty("LiveStatus")]
    public int? LiveStatus { get; set; }
}