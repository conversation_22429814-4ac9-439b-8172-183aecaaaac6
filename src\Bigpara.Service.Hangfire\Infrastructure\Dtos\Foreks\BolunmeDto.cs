﻿using Newtonsoft.Json;

namespace Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;

public class BolunmeDto
{
    public DateTime StrTarih { get; set; }

    [JsonProperty("d")]
    public long UnixDate { get; set; }

    [JsonProperty("co")]
    public string? StrLegacyKod { get; set; }
    public string? StrKod { get; set; }

    [JsonProperty("ri")]
    public decimal? DblBedelli { get; set; }

    [JsonProperty("bi")]
    public decimal? DblBedelsiz { get; set; }

    [JsonProperty("di")]
    public decimal? DblTemettu { get; set; }

    [JsonProperty("r")]
    public decimal? DblOran { get; set; }

    [JsonProperty("exc")]
    public string? Exchange { get; set; }
}