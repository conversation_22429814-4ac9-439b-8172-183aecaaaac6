﻿using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;

public class YuzeyselDto : YuzeyselBaseDto
{
    [JsonProperty("MarketSector")]
    public string Type { get; set; }

    [JsonProperty("Open")]
    public decimal? ACILIS { get; set; }

    [JsonProperty("UpperLimit")]
    public decimal? TAVAN { get; set; }

    [JsonProperty("LowerLimit")]
    public decimal? TABAN { get; set; }

    [JsonProperty("YTDHigh")]
    public decimal? YILYUKSEK { get; set; }

    [JsonProperty("YTDLow")]
    public decimal? YILDUSUK { get; set; }

    [JsonProperty("MTDHigh")]
    public decimal? AYYUKSEK { get; set; }

    [JsonProperty("MTDLow")]
    public decimal? AYDUSUK { get; set; }

    [JsonProperty("WTDHigh")]
    public decimal? HAFTAYUKSEK { get; set; }

    [JsonProperty("WTDLow")]
    public decimal? HAFTADUSUK { get; set; }

    [JsonProperty("PreviousYearClose")]
    public decimal? ONCEKIYILKAPANIS { get; set; }

    [JsonProperty("PreviousMonthClose")]
    public decimal? ONCEKIAYKAPANIS { get; set; }

    [JsonProperty("PreviousWeekClose")]
    public decimal? ONCEKIHAFTAKAPANIS { get; set; }

    [JsonIgnore]
    public decimal? FIYATADIMI { get; set; }

    [JsonProperty("Volume")]
    public decimal? LOTADET { get; set; }

    [JsonProperty("Capital")]
    public decimal? SERMAYE { get; set; }

    [JsonProperty("NetProfit")]
    public decimal? NETKAR { get; set; }

    [JsonProperty("LastPeriod")]
    [MaxLength(10)]
    public string? DONEM { get; set; }

    [JsonProperty("PriceEarning")]
    public decimal? FIYATKAZ { get; set; }

    [JsonProperty("MarketValue")]
    public decimal? PIYDEG { get; set; }

    [JsonProperty("FreeFloatStockSize")]
    public decimal? SENETSAY { get; set; }

    [JsonProperty("NetCapital")]
    public decimal? OZSERMAYE { get; set; }

    [JsonProperty("Beta200")]
    public decimal? BETA { get; set; }

    [JsonProperty("TotalVolume")]
    public decimal? HACIMLOT { get; set; }

    [JsonProperty("VWAP")]
    public decimal? AORT { get; set; }

    [JsonProperty("TotalTurnover")]
    public decimal? HACIMTL { get; set; }

    [JsonProperty("SettlementPrice")]
    public decimal? VOB_UZL { get; set; }

    [JsonProperty("PreviousSettlementPrice")]
    public decimal? VOB_ONCUZL { get; set; }

    [JsonProperty("OpenInterest")]
    public decimal? VOB_OPINTDEG { get; set; }

    [JsonIgnore]
    public string? Security { get; set; }

    [JsonIgnore]
    public string? StrPiyasa { get; set; }

    [JsonIgnore]
    public char? HisseGrubu { get; set; }

    [JsonIgnore]
    public string? SecurityType { get; set; }

    [JsonIgnore]
    public string? StrBoard { get; set; }

    [JsonIgnore]
    public decimal? XU100AG { get; set; }

    public string Proccesor { get; set; }

    [JsonProperty("Last")]
    public decimal? Son { get; set; }
}