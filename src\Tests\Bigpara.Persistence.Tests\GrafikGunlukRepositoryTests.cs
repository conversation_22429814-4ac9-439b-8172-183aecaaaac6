using Bigpara.Domain;
using Bigpara.Domain.Matriks;
using Bigpara.Persistence.Matriks.Grafiks;
using Microsoft.Data.SqlClient;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Bigpara.Persistence.Tests.Matriks.Grafiks
{
    public class GrafikGunlukRepositoryTests
    {
        private readonly Mock<IMatriksDbContext> _mockDbContext;
        private readonly GrafikRepository _repository;
        

        public GrafikGunlukRepositoryTests()
        {
            _mockDbContext = new Mock<IMatriksDbContext>();
            _repository = new GrafikRepository(_mockDbContext.Object);
        }



        [Fact]
        public async Task GetGrafikGunluksBySembolId_ReturnsGrafikGunlukList()
        {
            // Arrange
            int sembolId = 5;
            int top = 10;
            var expected = new List<GrafikGunluk>
            {
                new GrafikGunluk { SEMBOLID = sembolId, TARIH = DateTime.Today, ACILIS = 100 },
                new GrafikGunluk { SEMBOLID = sembolId, TARIH = DateTime.Today.AddDays(-1), ACILIS = 110 }
            };
            _mockDbContext
                .Setup(x => x.ExecuteStoredProcedureAsync<GrafikGunluk>(
                    "bp.pGetGrafikGunlukBySembolId",
                    It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetGrafikGunluksBySembolId(sembolId, top);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetTopGrafikGunluks_ThrowsNotImplementedException()
        {
            // Arrange
            int top = 5;

            // Act & Assert
            await Assert.ThrowsAsync<NotImplementedException>(() => _repository.GetTopGrafikGunluks(top));
        }
    }
}
