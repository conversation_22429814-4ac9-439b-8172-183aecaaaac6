﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs;

public class BolunmeRecurringJob : IRecurringJob
{
    private readonly BigparaHttpClient _bigparaHttpClient;
    private readonly ILogger<BolunmeRecurringJob> _logger;
    private readonly IConfiguration _configuration;
    private readonly IBolunmeService _bolunmeService;
    private readonly INotificationService _notificationService;

    private readonly int _maxConcurrentTasks;
    private string LastWeeks => DateTime.Now.AddDays(-7).ToString("yyyyMMdd");
    private string Today => DateTime.Now.ToString("yyyyMMdd");

    public BolunmeRecurringJob(
        BigparaHttpClient bigparaHttpClient,
        ILogger<BolunmeRecurringJob> logger,
        IConfiguration configuration,
        IBolunmeService bolunmeService,
        INotificationService notificationService)
    {
        _bigparaHttpClient = bigparaHttpClient;
        _logger = logger;
        _configuration = configuration;
        _bolunmeService = bolunmeService;
        _notificationService = notificationService;
        _maxConcurrentTasks = _configuration.GetValue("BolunmeProcessing:MaxConcurrentTasks", 10);
    }

    public string Name => "BolunmeRecurringJob";
    public IEnumerable<string> Crons => ["0 0 9 * * ?"];

    public async Task ExecuteAsync()
    {
        try
        {
            await ProcessDataTypeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError($"{Name} sırasında kritik hata oluştu.");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    public async Task ProcessDataTypeAsync()
    {
        try
        {
            var url = $"{_configuration["Foreks:CloudApi"]}/{_configuration["Foreks:Cloud:Bolunme:Url"]}/{LastWeeks}/to/{Today}";
            if (string.IsNullOrEmpty(url))
            {
                _logger.LogError("Foreks:Cloud:Bolunme:Url değeri bulunamadı.");
                await _notificationService.NotifyErrorAsync(Name, "Foreks:Cloud:Bolunme:Url değeri bulunamadı.");
                return;
            }

            var result = await _bigparaHttpClient.FetchDataAsync<BolunmeDto>(url);

            if (result == null || result.Count == 0)
            {
                _logger.LogWarning("Çekilen veri boş veya null.");
                return;
            }

            var bistResults = result.Where(x => !string.IsNullOrWhiteSpace(x.Exchange) && x.Exchange == "BIST").ToList();
            if (bistResults == null || bistResults.Count == 0)
            {
                _logger.LogWarning("BIST verisi bulunamadı.");
                return;
            }

            using var semaphore = new SemaphoreSlim(_maxConcurrentTasks);

            foreach (var item in bistResults)
            {
                await semaphore.WaitAsync();

                try
                {
                    await ProcessSingleItemAsync(item);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"İşlem sırasında hata oluştu: {item}");
                    await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.StrKod}, Hata: {ex} , Hata Mesajı: {ex.Message}");
                }
                finally
                {
                    semaphore.Release();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    private async Task ProcessSingleItemAsync(BolunmeDto bolunmeDto)
    {
        try
        {
            await _bolunmeService.Change(bolunmeDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {bolunmeDto.StrKod}");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {bolunmeDto.StrKod}, Hata: {ex} , Hata Mesajı: {ex.Message}");
        }
    }
}
