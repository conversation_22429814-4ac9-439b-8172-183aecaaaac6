using Microsoft.EntityFrameworkCore.Storage;

namespace Bigpara.Application.Contracts.Common;

public interface IUnitOfWork : IDisposable
{
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
    void ClearChangeTracker();
}

public interface IUnitOfWork<TContext> : IUnitOfWork where TContext : class
{
    TContext Context { get; }
}
