﻿using System.Text;
using System.Text.Json;
using Bigpara.Jobs.Hangfire.Models;
using Bigpara.Service.Hangfire.Services.Interfaces;
using System.Globalization;
using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Cache.Interfaces;
using System.Threading.Tasks;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients.Interfaces;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Services;

namespace Bigpara.Jobs.Hangfire.Workers
{
    public class ForeksRealTimeBackgroundService : BackgroundService
    {
        
        
        private readonly IServiceProvider _serviceProvider;
        private HttpClient _client;
        private List<string> fields;
        private readonly IConfiguration _configuration;
        private static readonly TimeZoneInfo TurkeyTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");
        private readonly ILogger<ForeksRealTimeBackgroundService> _logger;

        public ForeksRealTimeBackgroundService(IHttpClientFactory httpClientFactory, IServiceProvider serviceProvider, IConfiguration configuration, ILogger<ForeksRealTimeBackgroundService> logger)
        {
            _client = httpClientFactory.CreateClient("BigparaSignalrService");
            fields = new List<string>() {
                 Fields.Ask,
                 Fields.Bid,
                 Fields.Direction,
                 Fields.LegacyCode,
                 Fields.Name,
                 Fields.Code,
                 Fields.Low,
                 Fields.High,
                 Fields.DailyChangePercent,
                 Fields.DateTime,
                 Fields.SecurityType,
                 Fields.MarketSector,
                 Fields.ShortMarketCode,
                 Fields.Type,
                 Fields.SecurityGroup,
                 Fields.Group,
                 Fields.GrossSettlement,
                 Fields.VWAP,
                 Fields.LowerLimit,
                 Fields.UpperLimit,
                 Fields.MTDHigh,
                 Fields.MTDLow,
                 Fields.PreviousClose,
                 Fields.PreviousMonthClose,
                 Fields.PreviousWeekClose,
                 Fields.PreviousYearClose,
                 Fields.WTDHigh,
                 Fields.WTDLow,
                 Fields.YTDHigh,
                 Fields.YTDLow,
                 Fields.Close,
                 Fields.TotalTurnover,
                 Fields.TotalVolume
            };
            _serviceProvider = serviceProvider;
            _configuration = configuration;
            _logger = logger;
        }




        private async Task OnData(object? sender, ResponseArgs args)
        {
            try
            {
                CultureInfo.DefaultThreadCurrentCulture = new CultureInfo("en-US");
                CultureInfo.DefaultThreadCurrentUICulture = new CultureInfo("en-US");

                var results = (Dictionary<string,string>) args.Result;
                if (results.ContainsKey(Fields.DateTime) && results.ContainsKey("_i"))
                {
                    Console.WriteLine($"{results["_i"]} :{results.GetValueOrDefault(Fields.Open)},{results.GetValueOrDefault(Fields.Ask)},{results.GetValueOrDefault(Fields.Bid)},{results.GetValueOrDefault(Fields.Close)}, {results.GetValueOrDefault(Fields.Last)}");
                    await SaveStorage(results);
                   
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "OnData method error");
            }

        }

        private void OnError(object? sender, ErrorEventArgs e)
        {
            _logger.LogError(e.GetException(), "OnData method error");
        }

        private async Task OnLogin(object sender, ResponseArgs e)
        {
            if (!e.Success)
            {
                _logger.LogError("Giriş başarısız: {ErrorMessage}", e.Result);
                return;
            }


            var _foreksSocketClient = (ForeksSocketClient)sender;


            var stocks = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:Parite:Url"]}");
            await _foreksSocketClient.Subscribe(stocks, fields);

            var serbest = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:SerbestPiyasa:Url"]}");
            await _foreksSocketClient.Subscribe(serbest, fields);


            var tcmb = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:TCMB:Url"]}");
            await _foreksSocketClient.Subscribe(tcmb, fields);

            //var hisseler = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:Hisse:Url"]}");
            //await _foreksSocketClient.Subscribe(hisseler, fields);

        }


        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {

            //while (true)
            //{
            //    await SaveStorage(new Dictionary<string, string>
            //    {
            //        {"_i","o10" },
            //        { Fields.Code, "USDTRY" },
            //        { Fields.Ask, "39.9111" },
            //        { Fields.Bid, "39.813" },
            //        {Fields.Last,"39.862" },
            //        {Fields.Open,"39.8353" },
            //        {Fields.Close,"39.862" },
            //        {Fields.DateTime, "1751662776915" },
            //        {Fields.MarketSector, "Currency" },
            //    });

            //    await Task.Delay(3000);
            //}

            using (var scope = _serviceProvider.CreateScope())
            {
                var _foreksSocketClient = scope.ServiceProvider.GetRequiredService<IForeksSocketClient>();
               
                _foreksSocketClient.OnError += OnError;
                _foreksSocketClient.OnData  += OnData;
                _foreksSocketClient.OnLogin += OnLogin;

                await _foreksSocketClient.ConnectAsync("wss://websocket.foreks.com/websocket");
                if (_foreksSocketClient.IsConnected)
                {
                   
                    await _foreksSocketClient.LoginAsync(_configuration["Foreks:Auth:RealTimeUsername"], _configuration["Foreks:Auth:RealTimePassword"]);
                    await _foreksSocketClient.Listen();
                }
            }

          

            await Task.CompletedTask;
        }

     

       
        private async Task SaveStorage(Dictionary<string, string> results)
        {


            using (var scope = _serviceProvider.CreateScope())
            {
                var redisCache = scope.ServiceProvider.GetRequiredService<IRedisCacheService>();
                var pariteService = scope.ServiceProvider.GetRequiredService<IPariteService>();
                var hisseService = scope.ServiceProvider.GetRequiredService<IHisseService>();
                var endeksService = scope.ServiceProvider.GetRequiredService<IEndeksService>();
                var serbestPiyasaService = scope.ServiceProvider.GetRequiredService<ISerbestPiyasaService>();
                var _symbolService = scope.ServiceProvider.GetRequiredService<ISymbolService>();
                var _configuration = scope.ServiceProvider.GetRequiredService<IConfiguration>();
                var symbol = await _symbolService.GetSymbolByIdAsync(results["_i"]);

                if (symbol == null)
                    return;

                results["legacyCode"] = symbol.Sembol.ForeksKodYeni;
                results["SEMBOL"] = symbol.Sembol.Sembol;

                if (!results.ContainsKey("MS"))
                {
                    results.Add("MS", symbol.Sembol.MarketSector);
                }

                if (!results.ContainsKey("SY"))
                {
                    results.Add("SY", symbol.Sembol.SecurityType);
                }

                if (!results.ContainsKey("E"))
                {
                    results.Add("E", symbol.Sembol.Sembol);
                }

                results.Add("subMarket", symbol.Sembol.ImkbPazarKodString);


                switch (symbol.Sembol.MarketSector)
                {
                    case "Equity":

                        if (symbol.Sembol.SecurityType == "Warrant")
                        {
                            await SaveStorageWarrant(results, hisseService);
                            break;
                        }
                        else if (symbol.Sembol.SecurityType == "Fund")
                        {
                            await SaveStorageFund(results, hisseService);
                            break;
                        }
                        else
                        {
                            await SaveStorageImkb(results, hisseService);
                        }
                        break;
                    case "Index":
                        await SaveStorageIndex(results, endeksService);
                        break;
                    case "Currency":

                        

                        if (symbol.Sembol.Exchange.Equals("GrandBazaar"))
                            await SaveStorageSerbest(results, serbestPiyasaService);
                        else
                            await SaveStorageCurrency(results, pariteService);

                        break;

                    default:
                        break;
                }

                await SaveSembolDefinition(results, hisseService);
                await Task.CompletedTask;
            }
        }

        private async Task SaveSembolDefinition(Dictionary<string, string> results, IHisseService hisseService)
        {
            await Task.CompletedTask;
        }

        private async Task SaveStorageCurrency(Dictionary<string, string> results, IPariteService pariteService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await pariteService.Change(yuzeyselDto);
            await pariteService.ChangeRealTime(yuzeyselDto);
            await pariteService.SendRealTimeApi(results);
            await Task.CompletedTask;
        }

        private async Task SaveStorageSerbest(Dictionary<string, string> results, ISerbestPiyasaService serbestPiyasaService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await serbestPiyasaService.Change(yuzeyselDto);
            await serbestPiyasaService.SendRealTimeApi(results);
            await Task.CompletedTask;
        }

        private async Task SaveStorageIndex(Dictionary<string, string> results, IEndeksService hisseService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await hisseService.ChangeRealTime(yuzeyselDto);
            await Task.CompletedTask;

        }

        private async Task SaveStorageImkb(Dictionary<string, string> results, IHisseService hisseService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await hisseService.Change(yuzeyselDto);
            await hisseService.SendRealTimeApi(results);
            await Task.CompletedTask;
        }

        private static YuzeyselDto BuildYuzeyselDto(Dictionary<string, string> results)
        {
            var yuzeyselDto = new YuzeyselDto();
            yuzeyselDto.SEMBOL = results[Fields.Code];
            yuzeyselDto.UnixTime = (long)Convert.ToDouble(results[Fields.DateTime]);
            yuzeyselDto.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                                DateTimeOffset.FromUnixTimeMilliseconds((long)Convert.ToDouble(results[Fields.DateTime])).UtcDateTime,
                                TurkeyTimeZone);


            if (results.ContainsKey("MS"))
                yuzeyselDto.Type = results["MS"];


            if (results.ContainsKey("subMarket"))
                yuzeyselDto.HisseGrubu = !string.IsNullOrEmpty(results["subMarket"]) ? results["subMarket"][0] : default(char);


            if (results.ContainsKey("legacyCode"))
            {
                yuzeyselDto.LegacyCode = results["legacyCode"];
            }

            if (results.ContainsKey(Fields.Ask) && decimal.TryParse(results[Fields.Ask], out decimal a))
            {
                yuzeyselDto.SATIS = a;
            }

            if (results.ContainsKey(Fields.TotalTurnover) && decimal.TryParse(results[Fields.TotalTurnover], out decimal tT))
            {
                yuzeyselDto.HACIMTL = tT;
            }

            if (results.ContainsKey(Fields.TotalVolume) && decimal.TryParse(results[Fields.TotalVolume], out decimal tV))
            {
                yuzeyselDto.HACIMLOT = tV;
            }

            if (results.ContainsKey(Fields.Bid) && decimal.TryParse(results[Fields.Bid], out decimal b))
            {
                yuzeyselDto.ALIS = b;
            }

            if (results.ContainsKey(Fields.Low) && decimal.TryParse(results[Fields.Low], out decimal l))
            {
                yuzeyselDto.DUSUK = l;
            }

            if (results.ContainsKey(Fields.High) && decimal.TryParse(results[Fields.High], out decimal h))
            {
                yuzeyselDto.YUKSEK = h;
            }

            if (results.ContainsKey(Fields.VWAP) && decimal.TryParse(results[Fields.VWAP], out decimal d))
            {
                yuzeyselDto.AORT = d;
            }

            if (results.ContainsKey(Fields.Open) && decimal.TryParse(results[Fields.Open], out decimal pa))
            {
                yuzeyselDto.ACILIS = pa;
            }

            if (results.ContainsKey(Fields.Close) && decimal.TryParse(results[Fields.Close], out decimal p))
            {
                yuzeyselDto.KAPANIS = p;
            }

            if (results.ContainsKey(Fields.LowerLimit) && decimal.TryParse(results[Fields.LowerLimit], out decimal c))
            {
                yuzeyselDto.TABAN = c;
            }
            if (results.ContainsKey(Fields.UpperLimit) && decimal.TryParse(results[Fields.UpperLimit], out decimal u))
            {
                yuzeyselDto.TAVAN = u;
            }

            if (results.ContainsKey(Fields.WTDLow) && decimal.TryParse(results[Fields.WTDLow], out decimal dc))
            {
                yuzeyselDto.HAFTADUSUK = dc;
            }

            if (results.ContainsKey(Fields.WTDHigh) && decimal.TryParse(results[Fields.WTDHigh], out decimal dh))
            {
                yuzeyselDto.HAFTAYUKSEK = dh;
            }

            if (results.ContainsKey(Fields.MTDLow) && decimal.TryParse(results[Fields.MTDLow], out decimal dcp))
            {
                yuzeyselDto.AYDUSUK = dcp;
            }

            if (results.ContainsKey(Fields.MTDHigh) && decimal.TryParse(results[Fields.MTDHigh], out decimal dhp))
            {
                yuzeyselDto.AYYUKSEK = dhp;
            }

            if (results.ContainsKey(Fields.YTDLow) && decimal.TryParse(results[Fields.YTDLow], out decimal dcy))
            {
                yuzeyselDto.YILDUSUK = dcy;
            }

            if (results.ContainsKey(Fields.YTDHigh) && decimal.TryParse(results[Fields.YTDHigh], out decimal dhy))
            {
                yuzeyselDto.YILYUKSEK = dhy;
            }

            if (results.ContainsKey(Fields.PreviousMonthClose) && decimal.TryParse(results[Fields.PreviousMonthClose], out decimal dmcp))
            {
                yuzeyselDto.ONCEKIAYKAPANIS = dmcp;
            }
            if (results.ContainsKey(Fields.PreviousYearClose) && decimal.TryParse(results[Fields.PreviousYearClose], out decimal dycp))
            {
                yuzeyselDto.ONCEKIYILKAPANIS = dycp;
            }

            if (results.ContainsKey(Fields.PreviousWeekClose) && decimal.TryParse(results[Fields.PreviousWeekClose], out decimal dwcp))
            {
                yuzeyselDto.ONCEKIHAFTAKAPANIS = dwcp;
            }

            if (results.ContainsKey(Fields.PreviousClose) && decimal.TryParse(results[Fields.PreviousClose], out decimal pc))
            {
                yuzeyselDto.DUNKUKAPANIS = pc;
            }


            yuzeyselDto.Proccesor = "Soket";
            
            return yuzeyselDto;
        }

        private async Task SaveStorageFund(Dictionary<string, string> results, IHisseService hisseService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await hisseService.Change(yuzeyselDto);
            await Task.CompletedTask;
        }

        private async Task SaveStorageWarrant(Dictionary<string, string> results, IHisseService hisseService)
        {
            await Task.CompletedTask;
        }

        public Dictionary<string,string> GetCodes(string filterUrl)
        {
            using (HttpClient client = new HttpClient())
            {
                var response = client.GetAsync(filterUrl).Result;
                if (response.IsSuccessStatusCode)
                {
                    var content = response.Content.ReadAsStringAsync().Result;
                    var stocks = JsonSerializer.Deserialize<List<ForeksSymbol>>(content);
                    if (stocks == null)
                    {
                        return new Dictionary<string, string>();
                    }
                    return stocks.ToDictionary(k => k._Id, v => v.Code);
                }
                else
                {
                    _logger.LogError(filterUrl + " istek hatası: " + response.StatusCode);
                    return new Dictionary<string, string>();
                }
            }
        }


    }
}


