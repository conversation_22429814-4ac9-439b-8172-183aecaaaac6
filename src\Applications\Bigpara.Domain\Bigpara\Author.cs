namespace Bigpara.Domain.Bigpara;

public partial class Author
{
    public int Id { get; set; }
    public string AuthorFirstName { get; set; }
    public string AuthorLastName { get; set; }
    public string AuthorTitle { get; set; }
    public string AuthorProfession { get; set; }
    public string Picture { get; set; }
    public string FbAccountLink { get; set; }
    public string Email { get; set; }
    public string TwitterAccountLink { get; set; }
    public bool IsActive { get; set; }
    public byte AuthorType { get; set; }
    public string AuthorTypeName { get; set; }
    public bool IsLive { get; set; }
    public string Resume { get; set; }
    public bool AskQuestion { get; set; }
    public string ExpertiseAreas { get; set; }

    public string AuthorFullName
    {
        get
        {
            return AuthorFirstName + " " + AuthorLastName;
        }
    }

}
