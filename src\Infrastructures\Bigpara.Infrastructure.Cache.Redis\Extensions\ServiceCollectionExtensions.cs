﻿using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;
using Microsoft.Extensions.Configuration;
using Bigpara.Cache.Redis.Services;
using Bigpara.Cache.Interfaces;

namespace Bigpara.Cache.Redis.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddCustomRedisCache(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IConnectionMultiplexer>(sp =>
        {
            var options = ConfigurationOptions.Parse(configuration["Redis:ClientHost"]);
            options.Password = configuration["Redis:AuthKey"];
            options.ConnectTimeout = int.Parse(configuration["Redis:ConnectTimeout"] ?? "10000");
            options.ResponseTimeout = int.Parse(configuration["Redis:ResponseTimeout"] ?? "5000");
            options.AbortOnConnectFail = false;
            options.SyncTimeout = options.ConnectTimeout;

            return ConnectionMultiplexer.Connect(options);
        });

        services.AddSingleton<IRedisCacheService, RedisCacheService>();

        return services;
    }
}
