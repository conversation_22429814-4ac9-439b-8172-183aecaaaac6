﻿using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients.Interfaces;
using Bigpara.Service.Hangfire.Models;
using Bigpara.Service.Hangfire.Services.Interfaces;
using MongoDB.Driver;
using Quark.Core.Helpers;
using Quark.Models.Entities.Contents;
using System.Text.RegularExpressions;

namespace Bigpara.Service.Hangfire.Jobs.KapNews;

public class KapHaberleriInsertMongoRecurringJob : IRecurringJob
{
    private readonly ICmsService _cmsService;
    private readonly ISembollerService _sembollerService;
    private readonly BigparaHttpClient _bigparaHttpClient;
    private readonly IConfiguration _configuration;
    private readonly IWebPageContentFetcher _webPageContentFetcher;
    private readonly INotificationService _notificationService;
    private readonly ILogger<KapHaberleriInsertMongoRecurringJob> _logger;

    public KapHaberleriInsertMongoRecurringJob
    (
        ICmsService cmsService,
        ISembollerService sembollerService,
        BigparaHttpClient bigparaHttpClient,
        IConfiguration configuration,
        IWebPageContentFetcher webPageContentFetcher,
        INotificationService notificationService,
        ILogger<KapHaberleriInsertMongoRecurringJob> logger
    )
    {
        _cmsService = cmsService;
        _sembollerService = sembollerService;
        _bigparaHttpClient = bigparaHttpClient;
        _configuration = configuration;
        _webPageContentFetcher = webPageContentFetcher;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "KapHaberleriInsertMongoRecurringJob";
    public IEnumerable<string> Crons => ["*/3 * * * *"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("KapHaberleriInsertMongoRecurringJob - Başladı ");
            await InsertNewsToMongoAsync();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
            await _notificationService.NotifyErrorAsync("KapHaberleriInsertMongoRecurringJob - Hata", exception.Message);
        }
    }

    public async Task InsertNewsToMongoAsync()
    {
        var newsList = await GetNewsListAsync();

        if (newsList == null || !newsList.Any())
        {
            _logger.LogInformation("KapHaberleriInsertMongoRecurringJob - InsertNewsToMongoAsync - No news to insert.");
            await _notificationService.NotifyInfoAsync("KapHaberleriInsertMongoRecurringJob - InsertNewsToMongoAsync", "No news to insert.");

            return;
        }

        try
        {
            var semboller = await _sembollerService.GetSembollersAsync(x => x.Aktif, x => new SembolModel { Id = x.SembolId, Name = x.Sembol });

            foreach (var item in newsList)
            {
                var existingContent = await _cmsService.ContentExistsAsync("Contents", $"kap-{item.SourceId}");
                if (existingContent)
                {
                    _logger.LogInformation($"KapHaberleriInsertMongoRecurringJob - News with SourceId {item.SourceId} already exists in MongoDB. Skipping insertion.");
                    continue;
                }

                var matchingSymbols = SembolHelper.GetMatchingSembollerString(item.Sembol, semboller);

                DateTime lastNewsDate = item.PublishDate;
                var info = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");
                DateTimeOffset localTime = TimeZoneInfo.ConvertTime(lastNewsDate.AddSeconds(1), info).ToLocalTime();

                var IId = await _cmsService.GetNextIncrementIdAsync("Contents", "com.bigpara");
                string title = item.Header.Length <= 150 ? item.Header : item.Header.Substring(0, item.Header.Substring(0, 150).LastIndexOf(" "));
                var slug = ContentHelper.CreateContentSlug(title, IId);
                var ancestors = await _cmsService.GetAncestorsAsync("Contents.Prod", "/kap-haberleri/");
                var permissions = await _cmsService.GetPermissionsAsync("Contents.Prod", "/kap-haberleri/");
                var contentTags = ContentHelper.CreateContentTag(item.Tags);
                var path = "/kap-haberleri/";

                var properties = await _cmsService.GetContentPropertiesAsync("Article");
                var property = properties.FirstOrDefault(x => x.IxName == "Symbols");

                if (property != null && !string.IsNullOrWhiteSpace(matchingSymbols))
                {
                    property.Value = matchingSymbols;
                }

                var content = new Article
                {
                    Application = "com.bigpara",
                    CommentEnabled = false,
                    ContentType = "Article",
                    CreatedBy = "sistem",
                    CreatedDate = lastNewsDate.ToUniversalTime(),
                    IxName = title.Slugify(),
                    Title = title,
                    IId = IId,
                    ModifiedDate = lastNewsDate.ToUniversalTime(),
                    ModifiedBy = "sistem",
                    Order = 0,
                    Path = path,
                    Url = $"/haberler{path}{slug}",
                    ReferenceId = $"kap-{item.SourceId}",
                    StartDate = lastNewsDate.ToUniversalTime(),
                    Status = 0,
                    Text = item.Content,
                    Description = "",
                    Ancestors = ancestors,
                    Permissions = permissions,
                    Properties = properties,
                    Links = [path],
                    ContentTags = contentTags,
                    Tags = item.Tags
                };

                await _cmsService.InsertContentAsync("Contents", content);
            }

            _logger.LogInformation($"KapHaberleriInsertMongoRecurringJob - Inserted {newsList.Count()} news items into MongoDB.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "KapHaberleriInsertMongoRecurringJob - Error inserting news into MongoDB.");
            await _notificationService.NotifyErrorAsync("KapHaberleriInsertMongoRecurringJob - InsertNewsToMongoAsync Hata", ex.Message);
        }
    }

    public async Task<IEnumerable<News>> GetNewsListAsync()
    {
        try
        {
            long lastNewsTimestampDate = await _cmsService.GetLastKapNewsTimestampAsync("Contents", "com.bigpara", "/kap-haberleri/");

            var url = $"{_configuration["Foreks:CloudApi"]}/{_configuration["Foreks:Cloud:Contents:Url"]}?source={_configuration["Foreks:Cloud:Contents:Source"]}&locale={_configuration["Foreks:Cloud:Contents:Locale"]}&last={_configuration["Foreks:Cloud:Contents:TakeCount"]}&gt={lastNewsTimestampDate}";

            var newsItems = await _bigparaHttpClient.FetchDataAsync<NewsItemDto>(url);

            var excludeTestNewsItems= newsItems.Where(x => !x.Summary.ToLower().Contains("test bildirimi") && !x.Header.ToLower().Contains("test bildirimi")).ToList();

            var newsList = new List<News>();
            int haberNo = 0;

            foreach (var item in excludeTestNewsItems)
            {
                haberNo++;
                string htmlContent = await _webPageContentFetcher.GetWebPageBodyContent(item.Content);

                Console.WriteLine($"*******Icerik Parse Ediliyor {newsItems.Count} / {haberNo} Content {item.Content}");
                htmlContent = Regex.Replace(htmlContent, @"(\s*\n\s*){1,}", match =>
                {
                    // Eğer birden fazla \n varsa maksimum 1 <br/> döndür
                    int lineBreakCount = match.Value.Length;
                    return lineBreakCount >= 1 ? "<br/>" : "<br/>";
                });

                var sembolCodes = item?.Sender?.Codes ?? item?.RelatedStock;
                string sembol = sembolCodes != null
                                        ? ResourceHelper.GetSembolString(sembolCodes)
                                        : "";

                var header = ResourceHelper.HeaderFormatString(sembolCodes, item?.Sender?.Title, item?.Header);
                newsList.Add(new News
                {
                    PublishDate = DateTimeOffset.FromUnixTimeMilliseconds(item.PublishDate).DateTime,
                    Header = header,
                    Content = $"<br>{header}<br><br>{htmlContent} <br>{item.SourceRef?.Replace("https", "http")}<br>", // foreks HTML içeriğini ata
                    Sembol = sembol,
                    HaberNo = haberNo,
                    SourceId = item.SourceId,
                    Tags = item.Tag
                });
            }

            return newsList;
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
            await _notificationService.NotifyErrorAsync("KapHaberleriInsertMongoRecurringJob - GetNewsListAsync Hata", exception.Message);

            return new List<News>();
        }
    }
}