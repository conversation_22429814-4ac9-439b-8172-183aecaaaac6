﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Common
{
    public class BaseResponse
    {
        public BaseResponse()
        {
            Errors = new List<string>();
        }
        public List<string> Errors { get; set; }
        public bool Success => Errors.Count == 0;
        public dynamic Data { get; set; }
    }
}
