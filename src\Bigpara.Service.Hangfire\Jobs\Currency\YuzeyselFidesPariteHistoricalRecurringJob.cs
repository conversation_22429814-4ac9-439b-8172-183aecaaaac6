﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Curreny;

public class YuzeyselFidesPariteHistoricalRecurringJob : IRecurringJob
{
    private readonly ForeksHttpClient _foreksHttpClient;
    private readonly ILogger<YuzeyselFidesPariteHistoricalRecurringJob> _logger;
    private readonly IConfiguration _configuration;
    private readonly IPariteService _pariteService;
    private readonly INotificationService _notificationService;
    private readonly int _maxConcurrentTasks;

    public YuzeyselFidesPariteHistoricalRecurringJob(
        ForeksHttpClient foreksHttpClient,
        ILogger<YuzeyselFidesPariteHistoricalRecurringJob> logger,
        IConfiguration configuration,
        IPariteService pariteService,
        INotificationService notificationService)
    {
        _foreksHttpClient = foreksHttpClient;
        _logger = logger;
        _configuration = configuration;
        _pariteService = pariteService;
        _notificationService = notificationService;
        _maxConcurrentTasks = _configuration.GetValue("YuzeyselFidesPariteHistoricalProcessing:MaxConcurrentTasks", 10);
    }

    public string Name => "YuzeyselFidesPariteHistoricalRecurringJob";
    public IEnumerable<string> Crons => ["0 0/55 * * * ?"];

    public async Task ExecuteAsync()
    {
        try
        {
            await ProcessDataTypeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{Name} sırasında kritik hata oluştu.");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    public async Task ProcessDataTypeAsync()
    {
        try
        {
            var url = $"{_configuration["Foreks:SnapShotApi"]}/?{_configuration["Foreks:SnapShot:Parite:Url"]}";
            if (string.IsNullOrEmpty(url))
            {
                _logger.LogError("Foreks:SnapShot:Parite:Url değeri bulunamadı.");
                await _notificationService.NotifyErrorAsync(Name, "Foreks:SnapShot:Parite:Url değeri bulunamadı.");
                return;
            }

            var result = await _foreksHttpClient.FetchDataAsync<YuzeyselDto>(url);

            if (result == null || result.Count == 0)
            {
                _logger.LogWarning("Çekilen veri boş veya null.");
                return;
            }

            using var semaphore = new SemaphoreSlim(_maxConcurrentTasks);

            foreach (var item in result)
            {
                if (!item.ALIS.HasValue || !item.SATIS.HasValue)
                {
                    _logger.LogWarning("Boş veri atlandı: {Item}", item);
                    continue;
                }

                await semaphore.WaitAsync();

                try
                {
                    await ProcessSingleItemAsync(item);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"İşlem sırasında hata oluştu: {item.SEMBOL}");
                    await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.SEMBOL} , Hata: {ex} , Hata Mesajı: {ex.Message}");
                }
                finally
                {
                    semaphore.Release();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    private async Task ProcessSingleItemAsync(YuzeyselDto item)
    {
        try
        {
            await _pariteService.ChangeHistorical(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {item.SEMBOL}");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.SEMBOL} , Hata: {ex} , Hata Mesajı: {ex.Message}");
        }
    }
}
