﻿using Bigpara.Domain.Matriks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Report;

public class GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleriRecurringJob : IRecurringJob
{
    private readonly ISeansRaporlariService _seansRaporlariService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleriRecurringJob> _logger;

    public GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleriRecurringJob
    (
        ISeansRaporlariService seansRaporlariService,
        INotificationService notificationService,
        ILogger<GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleriRecurringJob> logger
    )
    {
        _seansRaporlariService = seansRaporlariService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleriRecurringJob";
     public IEnumerable<string> Crons => ["*/15 9-17 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleriRecurringJob - Başladı ");
            await ProcessGetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleri();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    /// <summary>
    /// 15 Dk Bist'deki artan azalan degismeyen hisse adet bilgileri
    /// </summary>
    public async Task ProcessGetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleri()
    {
        if (!_seansRaporlariService.IsActive())
            return;

        try
        {
            const string pattern = "İşlem gören {0} hisse senedinin {1} tanesi dünkü kapanış fiyatının üstünde, {2} tanesi dünkü kapanış fiyatının altında  {3} tanesi ise dünkü kapanışı ile aynı seviyeden işlem görüyor.";
            var data = await _seansRaporlariService.GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleri();
            var seansRaporuBistHisselerDurumIstatistik = data.FirstOrDefault(r => r.Durum == 1);
            if (seansRaporuBistHisselerDurumIstatistik == null) return;
            var artanAdet = seansRaporuBistHisselerDurumIstatistik.Adet;
            var raporuBistHisselerDurumIstatistik = data.FirstOrDefault(r => r.Durum == -1);
            if (raporuBistHisselerDurumIstatistik == null) return;
            var azalanAdet = raporuBistHisselerDurumIstatistik.Adet;
            var bistHisselerDurumIstatistik = data.FirstOrDefault(r => r.Durum == 0);
            if (bistHisselerDurumIstatistik == null) return;
            var degismeyenAdet = bistHisselerDurumIstatistik.Adet;

            var result = string.Format(pattern, artanAdet + azalanAdet + degismeyenAdet, artanAdet, azalanAdet, degismeyenAdet);

            var seansRaporu = new SeansRaporu
            {
                HisseCumle = result,
                Yon = 0,
                HisseAdi = string.Empty,
                EklenmeTarihi = DateTime.Now
            };

            await _seansRaporlariService.CreateAndCacheSeansRaporu(seansRaporu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            //EmailHelper.SendEmail("GetSeansRaporu10DkEnCokDegerKazanan", ex.Message);
            await _notificationService.NotifyErrorAsync($"GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleri", ex.Message);
        }
    }
}
