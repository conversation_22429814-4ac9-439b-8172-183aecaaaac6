﻿using Bigpara.Application.Common;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Features.News.Commands
{
    public class AddNewsReadCommand:IRequest<BaseResponse>
    {
        public string id { get; set; }
    }

    public class AddNewsReadCommandHandler : IRequestHandler<AddNewsReadCommand, BaseResponse>
    {
        public async Task<BaseResponse> Handle(AddNewsReadCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse();

            Console.WriteLine($"News with ID {request.id} marked as read.");

            return await Task.FromResult(response);
        }

       
    }


}
