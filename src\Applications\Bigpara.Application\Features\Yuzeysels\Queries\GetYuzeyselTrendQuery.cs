﻿using Bigpara.Application.Contracts.Repositories.Matriks.Yuzeysels;
using Bigpara.Application.Features.Yuzeysels.ViewModels;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Features.Yuzeysels.Queries
{
    #region Query
    public class GetYuzeyselTrendQuery : IRequest<GetYuzeyselTrendQueryResponse>
    {
        public string Symbols { get; set; }
    }
    #endregion

    #region Handler
    public class GetYuzeyselTrendQueryHandler : IRequestHandler<GetYuzeyselTrendQuery, GetYuzeyselTrendQueryResponse>
    {
        private readonly IYuzeyselRepository _yuzeyselRepository;
        public GetYuzeyselTrendQueryHandler(IYuzeyselRepository yuzeyselRepository)
        {
            _yuzeyselRepository = yuzeyselRepository;
        }
        public async Task<GetYuzeyselTrendQueryResponse> Handle(GetYuzeyselTrendQuery request, CancellationToken cancellationToken)
        {
            var yuzeysels = await _yuzeyselRepository.GetPerformanceBySembols(request.Symbols);
            var data = yuzeysels.Select(s => new YuzeyselTrendDto()
            {
               //TODO 
            }).ToList();

            var response = new GetYuzeyselTrendQueryResponse
            {
                Data = data
            };

            return response;
        }
    }
    #endregion

}
