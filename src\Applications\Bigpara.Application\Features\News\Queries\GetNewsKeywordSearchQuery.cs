﻿
using Bigpara.Application.Contracts.Repositories.Quark.News;
using Bigpara.Application.Features.News.Queries.Models;
using MediatR;

namespace Bigpara.Application.Features.News.Queries;

#region Query
public class GetNewsKeywordSearchQuery : IRequest<GetNewsKeywordSearchQueryResponse>
{
    public string Keyword { get; set; }
    public int Size { get; set; }
}

#endregion

#region Handler
public class GetNewsKeywordSearchQueryHandler : IRequestHandler<GetNewsKeywordSearchQuery, GetNewsKeywordSearchQueryResponse>
{
    private readonly INewsSearchRepository _searchRepository;

    public GetNewsKeywordSearchQueryHandler(INewsSearchRepository searchRepository)
    {
        _searchRepository = searchRepository;
    }

    public async Task<GetNewsKeywordSearchQueryResponse> Handle(GetNewsKeywordSearchQuery request, CancellationToken cancellationToken)
    {

        var news = await _searchRepository.Search(request.Keyword, pageSize: request.Size);

        GetNewsKeywordSearchQueryResponse response = new GetNewsKeywordSearchQueryResponse();
        response.Data = new
        {
            news = news.Data,
            pageIndex = news.PageIndex,
            pageSize = news.PageSize
        };

        return response;

    }
}

#endregion

