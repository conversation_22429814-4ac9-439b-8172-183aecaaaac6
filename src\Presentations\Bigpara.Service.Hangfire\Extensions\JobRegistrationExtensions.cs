﻿using Bigpara.Service.Hangfire.Filters;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients.Interfaces;
using Bigpara.Service.Hangfire.Jobs;
using Bigpara.Service.Hangfire.Jobs.Index;
using Bigpara.Service.Hangfire.Jobs.Stocks;

using Bigpara.Service.Hangfire.Jobs.Report;

using Bigpara.Service.Hangfire.Jobs.Bonds;
using Bigpara.Service.Hangfire.Jobs.Viop;
using Hangfire;
using Hangfire.MemoryStorage;
using Bigpara.Service.Hangfire.Jobs.Curreny;
using Bigpara.Service.Hangfire.Jobs.Symbol;
using Bigpara.Service.Hangfire.Jobs.GrandBazaar;
using Bigpara.Service.Hangfire.Jobs.KapNews;

namespace Bigpara.Service.Hangfire.Extensions;

public static class JobRegistrationExtensions
{
    public static IServiceCollection AddBigparaHangfireJobs(this IServiceCollection services, IConfiguration configuration)
    {
        var isGlobalEnabled = configuration.GetValue("RecurringJobs:IsEnabled", true);

        if (!isGlobalEnabled)
        {
            return services;
        }

        services.AddSingleton<LogEverythingAttribute>();

        services.AddHangfire((provider, options) =>
        {
            var loggerFilter = provider.GetRequiredService<LogEverythingAttribute>();

            if (configuration.GetValue("Production", false))
            {
                options.UseSqlServerStorage(configuration["ConnectionStrings:Hangfire"]);
            }
            else
            {
                options.UseMemoryStorage();
            }

            options.UseFilter(loggerFilter);
        });

        services.AddHangfireServer();

        if (configuration.GetValue("RecurringJobs:Currency:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, YuzeyselFidesGelecekAndEndeksRecurringJob>();
            services.AddScoped<IRecurringJob, YuzeyselFidesPariteHistoricalRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:CurrencyRealTime:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, YuzeyselFidesPariteRecurringJob>();
            services.AddScoped<IRecurringJob, YuzeyselFidesPariteRealTimeRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Stocks:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, YuzeyselImkbHisseRecurringJob>();
            services.AddScoped<IRecurringJob, YuzeyselImkbHisseRealTimeRecurringJob>();
            services.AddScoped<IRecurringJob, YuzeyselImkbHisseHistoricalRecurringJob>();
            services.AddScoped<IRecurringJob, YuzeyselImkbHisseWarrantRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Index:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, YuzeyselImkbEndeksRecurringJob>();
            services.AddScoped<IRecurringJob, YuzeyselImkbEndeksRealTimeRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:GrandBazaar:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, YuzeyselTcmbRecurringJob>();
            services.AddScoped<IRecurringJob, YuzeyselSerbestRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Symbol:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, DailyMarketSymbolSyncJob>();
        }

        if (configuration.GetValue("RecurringJobs:Sector:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, SektorRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Split:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, BolunmeRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Viop:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, YuzeyselViopRecurringJob>();
            services.AddScoped<IRecurringJob, YuzeyselViopHistoricalRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Bonds:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, TahvilKesinOzetRecurringJob>();
            services.AddScoped<IRecurringJob, TahvilRepoOzetRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Doviz:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, GetDovizRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Crypto:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, BitCoinDailyDataBitFinexRecurringJob>();
            //services.AddScoped<IRecurringJob, BitCoinDailyDataRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Indikators:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, GetIndikatorlerRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:KapNews:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, KapHaberleriRecurringJob>();
            //services.AddScoped<IRecurringJob, KapHaberleriInsertMongoRecurringJob>();
            services.AddSingleton<IWebPageContentFetcher, WebPageContentFetcher>();
        }

        if (configuration.GetValue("RecurringJobs:PiyasaTakvimi:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, PiyasaTakvimiRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Report:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, GetSeansRaporu10DkBist030EndeksRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu10DkEnCokDegerKaybedenRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu10DkEnCokDegerKazananRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu10DkEnCokIslemAdediSahipHisselerRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu10DkIslemHacmiRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleriRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu15DkBist0100EndeksRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisselerRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu15DkBist0300EnCokDegerKazanaHisselerRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu15DkBist050EndeksRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu15DkDegerKaybiDikkatCekenRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu15DkDegerKazananDikkatCekenRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu15DkTabandaIslemGorenHisselerRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu15DkTavandaIslemGorenHisselerRecurringJob>();
            services.AddScoped<IRecurringJob, GetSeansRaporu1DkHisseAktivitiBilgisiRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:BistEnd:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, GetBistSeansSonaErdiRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:UserMetrics:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, InsertSymbolClickCountRecurringJob>();
            services.AddScoped<IRecurringJob, UpdateMostClickedSymbolOrderRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:UserServices:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, UserAlertRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:MarketAnalytics:IsEnabled", false))
        {
            //services.AddScoped<IRecurringJob, MaliOranImportRecurringJob>();
            services.AddScoped<IRecurringJob, OtomatikTeknikYorumRecurringJob>();
        }

        if (configuration.GetValue("RecurringJobs:Media:IsEnabled", false))
        {
            services.AddScoped<IRecurringJob, VideoInfoDownloaderRecurringJob>();
        }

        return services;
    }
}
