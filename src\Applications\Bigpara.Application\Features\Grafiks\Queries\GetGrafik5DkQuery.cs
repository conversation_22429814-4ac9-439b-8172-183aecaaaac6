﻿using AutoMapper;
using Bigpara.Application.Contracts.Repositories.Matriks.Grafiks;
using Bigpara.Application.Dtos;
using Bigpara.Application.Features.Grafiks.Queries.Models;
using MediatR;

namespace Bigpara.Application.Features.Grafiks.Queries;


#region Query
public class GetGrafik5DkQuery : IRequest<GetGrafikQueryResponse>
{
    public string Symbols { get; set; }
    public int PerCount { get; set; }
}
#endregion

#region Handler
public class GetGrafik5DkQueryHandler : IRequestHandler<GetGrafik5DkQuery, GetGrafikQueryResponse>
{
    private readonly IGrafikRepository _grafikGunlukRepository;
    private readonly IMapper _mapper;
    public GetGrafik5DkQueryHandler(IGrafikRepository grafikGunlukRepository, IMapper mapper)
    {
        _grafikGunlukRepository = grafikGunlukRepository;
        _mapper = mapper;
    }
    public async Task<GetGrafikQueryResponse> Handle(GetGrafik5DkQuery request, CancellationToken cancellationToken)
    {
        var response = new GetGrafikQueryResponse();
        var data = await _grafikGunlukRepository.GetGrafik5DkBySembolIds(request.Symbols, request.PerCount);

        if (data == null) { 
            response.Errors.Add("No data found for the provided symbols.");
            return response;
        }

        foreach (var item in request.Symbols.Split(','))
        {
            var last = data.OrderByDescending(p => p.TARIH).FirstOrDefault(p => p.SEMBOL == item);
            var grafikItem = new GrafikDataListDto();
            grafikItem.Current = _mapper.Map<GrafikDto>(last);
            grafikItem.Chart = data.Where(p => p.SEMBOL == item).Select(s => _mapper.Map<GrafikDto>(s)).ToList();

            response.Data.Add(grafikItem);
        }

        return response;
    }
}

#endregion


