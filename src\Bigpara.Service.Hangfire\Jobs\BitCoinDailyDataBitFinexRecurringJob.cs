﻿using Bigpara.Domain.Bitcoin;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;
using Newtonsoft.Json;

namespace Bigpara.Service.Hangfire.Jobs;

public class BitCoinDailyDataBitFinexRecurringJob : IRecurringJob
{
    private readonly IBitcoinService _bitcoinService;
    private readonly ILogger<BitCoinDailyDataBitFinexRecurringJob> _logger;
    private readonly BigparaHttpClient _bigparaHttpClient;
    private readonly INotificationService _notificationService;
    private readonly ISystemSettingsService _systemSettingsService;

    private readonly Dictionary<string, BitfinexNameOrder> _nameOrderDic = new Dictionary<string, BitfinexNameOrder>()
        {
            {"tBTCUSD", new BitfinexNameOrder {Name = "BTC/USD", Order = 1}}, //"Bitcoin (BTC/USD)"
            {"tXRPUSD", new BitfinexNameOrder {Name = "XRP/USD", Order = 2}}, //"Ripple (XRP/USD)"
            {"tETHUSD", new BitfinexNameOrder {Name = "ETH/USD", Order = 3}}, //"Ethereum (ETH/USD)"
            {"tBABUSD", new BitfinexNameOrder {Name = "BCH/USD", Order = 4}}, //"Bitcoin Cash (BCH/USD)"
            {"tXLMUSD", new BitfinexNameOrder {Name = "XLM/USD", Order = 5}}, //"Stellar (XLM/USD)"
            {"tEOSUSD", new BitfinexNameOrder {Name = "EOS/USD", Order = 6}}, //"EOS (EOS/USD)"
            {"tLTCUSD", new BitfinexNameOrder {Name = "LTC/USD", Order = 7}}, //"Litecoin (LTC/USD)"
            {"tXMRUSD", new BitfinexNameOrder {Name = "XMR/USD", Order = 8}}, //"Monero (XMR/USD)"
            {"tTRXUSD", new BitfinexNameOrder {Name = "TRX/USD", Order = 9}}, //"TRON (TRX/USD)"
            {"tIOTUSD", new BitfinexNameOrder {Name = "IOTA/USD", Order = 10}} //"IOTA (IOTA /USD)"
        };

    public BitCoinDailyDataBitFinexRecurringJob
    (
        IBitcoinService bitcoinService,
        ILogger<BitCoinDailyDataBitFinexRecurringJob> logger,
        BigparaHttpClient bigparaHttpClient,
        INotificationService notificationService,
        ISystemSettingsService systemSettingsService
    )
    {
        _bitcoinService = bitcoinService;
        _logger = logger;
        _bigparaHttpClient = bigparaHttpClient;
        _notificationService = notificationService;
        _systemSettingsService = systemSettingsService;
    }

    public string Name => "BitCoinDailyDataBitFinexRecurringJob";
    public IEnumerable<string> Crons => ["* * * * *"];

    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("BitCoinDailyDataBitFinexRecurringJob - Başladı ");
            await ProcessDailyDataBitFinex();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    private async Task ProcessDailyDataBitFinex()
    {
        try
        {
            var sUrl = (await _systemSettingsService.GetSystemSettingsValueFromCacheAsync("FileConfiguration", "BitCoinAPIUrlBitfinex")).Value;
            var bitfinexResult = await _bigparaHttpClient.FetchObjectAsync<object[]>(sUrl);

            if (bitfinexResult != null && bitfinexResult.Any())
            {
                var inserDate = DateTime.Now;
                var ticket = inserDate.Ticks;
                foreach (var bitfinexItem in bitfinexResult)
                {
                    var item = JsonConvert.DeserializeObject<object[]>(bitfinexItem.ToString());
                    var nameOrder = _nameOrderDic[item[0].ToString()];
                    var bitfinexOnline = new BitfinexOnline
                    {
                        Symbol = item[0].ToString(),
                        Name = nameOrder != null ? nameOrder.Name : string.Empty,
                        BId = Convert.ToDouble(item[1]),
                        DailyChangePerc = Convert.ToDouble(item[6]),
                        InsertDate = inserDate,
                        Ticket = ticket,
                        SymbolOrder = nameOrder != null ? nameOrder.Order : 9999
                    };

                    await _bitcoinService.AddBitfinexOnline(bitfinexOnline);
                }
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.StackTrace);
            //TODO : Check
            //EmailHelper.SendEmail(SystemSettings.MailServerConfiguration.Sysmon, "BitFinex servis hatası", exception.Message);
            await _notificationService.NotifyErrorAsync("BitFinex servis hatası", exception.Message);
            return;
        }
    }
}
