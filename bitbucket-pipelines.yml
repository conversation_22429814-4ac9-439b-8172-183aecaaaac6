image: mcr.microsoft.com/dotnet/sdk:9.0

definitions:
  steps:
    - step: &build-step
        name: SonarQube analysis
        runs-on:
          - self.hosted  # <-- Bu satır çok önemli
          - linux
        caches:
          - dotnetcore
          - sonar
        script:
          # Gerekli sistem paketlerini yükle
          - apt-get update && apt-get install -y --no-install-recommends openssl ca-certificates

          # TLS güvenlik seviyesini düşür: openssl.conf dosyasını düzenle
          - sed -i 's/\[openssl_init\]/# [openssl_init]/' /etc/ssl/openssl.cnf
          - printf "\n\n[openssl_init]\nssl_conf = ssl_sect" >> /etc/ssl/openssl.cnf
          - printf "\n\n[ssl_sect]\nsystem_default = ssl_default_sect" >> /etc/ssl/openssl.cnf
          - printf "\n\n[ssl_default_sect]\nMinProtocol = TLSv1\nCipherString = DEFAULT@SECLEVEL=0\n" >> /etc/ssl/openssl.cnf

          # .NET TLS zorlamaları
          - export DOTNET_SYSTEM_NET_SECURITY_PROTOCOLS=Tls12
          - export DOTNET_SYSTEM_NET_SECURITY_ENCRYPTIONPOLICY=RequireEncryption


          - dotnet tool install --global dotnet-coverage
          - dotnet tool install --global dotnet-sonarscanner
          - export PATH="$PATH:/root/.dotnet/tools"
          - dotnet nuget add source "${AZURE_FEED_URL}" --name AzureArtifacts --username "${AZURE_FEED_USERNAME}"  --password "${AZURE_FEED_PAT}" --store-password-in-clear-text
          - dotnet sonarscanner begin /k:"hurriyet-software_bigpara.hurriyet.com.tr_b06bb3a6-6782-4a75-8f9f-d0dd155d35aa" /d:sonar.token="${SONAR_TOKEN}"  /d:sonar.host.url="${SONAR_HOST_URL}" /d:sonar.cs.vscoveragexml.reportsPaths="coverage.xml"
          - dotnet sln bigpara.hurriyet.com.tr.sln remove bigpara.hurriyet.com.tr.AppHost/bigpara.hurriyet.com.tr.AppHost.csproj
          - dotnet build 
          - dotnet-coverage collect "dotnet test" -f xml -o "coverage.xml"
          - dotnet sonarscanner end /d:sonar.token="${SONAR_TOKEN}"
  caches:
    sonar: ~/.sonar

pipelines:
  branches:
    '{master}':
      - step: *build-step

  pull-requests:
    '**':
      - step: *build-step