﻿
using Bigpara.Application.Contracts.Repositories.Matriks.PiyasaTakvim;
using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Matriks;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class PiyasaTakvimiService : IPiyasaTakvimiService
{
    private readonly IRedisCacheService _redisCacheService;
    private readonly IPiyasaTakvimiRepository _piyasaTakvimiRepository;

    public PiyasaTakvimiService
    (
        IPiyasaTakvimiRepository piyasaTakvimiRepository,
        IRedisCacheService redisCacheService
    )
    {
        _piyasaTakvimiRepository = piyasaTakvimiRepository;
        _redisCacheService = redisCacheService;
    }

    public async Task<List<PiyasaTakvimi>> GetPiyasaTakvimiTopCount(int topCount)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_PIYASA_TAKVIMI_TOP_COUNT);

        return _redisCacheService.GetRedisList(key, () => _piyasaTakvimiRepository.GetPiyasaTakvimiTopData(topCount).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_10DK_DURATION).ToList();
    }

    public async Task<List<PiyasaTakvimi>> GetPiyasaTakvimBySelectedDateRange(DateTime baslangictarihi, int gunSayisi, short tarihSayisi)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_PIYASA_TAKVIMI_TARIH_ARALIGI_BY_TARIH_GUN_TARIHSAYISI, baslangictarihi.ToString("yyyyMMdd"), gunSayisi, tarihSayisi);

        return _redisCacheService.GetRedisList(key, () => _piyasaTakvimiRepository.GetPiyasaTakvimiList(baslangictarihi, gunSayisi, tarihSayisi).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_DURATION).ToList();
    }

    public async Task<bool> InsertOrUpdatePiyasaTakvimi(PiyasaTakvimi piyasaTakvimi)
    {
        return await _piyasaTakvimiRepository.InsertOrUpdatePiyasaTakvimi(piyasaTakvimi);
    }

    public async Task<int> GetTotalPiyasaTakvimiCount()
    {
        return await _piyasaTakvimiRepository.GetTotalPiyasaTakvimiCount();
    }

    public async Task<List<PiyasaTakvimi>> GetPiyasaTakvimiByPaging(int intervalStart, int intervalEnd)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_PIYASA_TAKVIMI_SAYFALI_BY_START_END, intervalStart, intervalEnd);

        return _redisCacheService.GetRedisList(key, () => _piyasaTakvimiRepository.GetPiyasaTakvimiByPaging(intervalStart, intervalEnd).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_DURATION).ToList();
    }

    public async Task<PiyasaTakvimi> GetPiyasaTakvimiById(int id)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_PIYASA_TAKVIMI_DETAY_BY_ID, id);

        return _redisCacheService.Get(key, () => _piyasaTakvimiRepository.GetPiyasaTakvimiById(id).GetAwaiter().GetResult());
    }

    public async Task DeletePiyasaTakvimi(int id)
    {
        await _piyasaTakvimiRepository.DeletePiyasaTakvimi(id);
    }
}
