{"SeriLog": {"Url": "http://***************:9200", "Platform": "Test-Bigpara.Service.Api", "Env": "Test"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"Matriks": "Server=***************;Database=DBMATRIKSv2;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;", "Bigpara": "Server=***************;Database=TestBigpara;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;", "Foreks": "Server=***************;Database=TestForeks;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;", "Redis": "***************:6379,password=BIGpara!com!tr!2014!"}, "Quark": {"AppId": "65e47493cd67ea01d2e233c9", "AppSecret": "Ecv+IIXPJrguc9Wtu7ZHVd3ra+8Np0gRLO5clI1zpqpFNlHHiBxh53pxsxFkQj8RorM+VPrD7HgTgVjXTwCGPA==", "BaseApplication": "com.bigpara", "DataService": {"Url": "http://cmsapi.hurriyet.com.tr/api"}, "StaticImage": {"BaseUrl": "//static.hurriyet.com.tr"}, "ImageService": {"Url": "//image.hurimg.com/i/hurriyet/75", "BaseUrl": "//image.hurimg.com"}}, "Auth": {"Voltran": {"Url": "https://uyelik-sso1.demirorenmedya.com", "Issuer": "https://uyelik-sso1.demirorenmedya.com"}}}