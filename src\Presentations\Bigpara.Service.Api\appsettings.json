{"SeriLog": {"Url": "http://***************:9200", "Platform": "Bigpara.Service.Api", "Env": "Prod"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"Matriks": "", "Bigpara": "", "Foreks": "", "Redis": ""}, "Quark": {"AppId": "65e47493cd67ea01d2e233c9", "AppSecret": "", "BaseApplication": "com.bigpara", "DataService": {"Url": "http://cmsapi.hurriyet.com.tr/api"}, "StaticImage": {"BaseUrl": "//static.hurriyet.com.tr"}, "ImageService": {"Url": "//image.hurimg.com/i/hurriyet/75", "BaseUrl": "//image.hurimg.com"}}, "Redis": {"ClientHost": "***************", "AuthKey": "BIGpara!com!tr!2014", "ConnectTimeout": "10000", "ResponseTimeout": "5000"}, "RedisDefaultDbIndex": "0"}