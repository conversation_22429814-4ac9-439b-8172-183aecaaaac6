var builder = DistributedApplication.CreateBuilder(args);


//builder.AddProject<Projects.Bigpara_Worker_WebSocket>("bigpara-worker-websocket");
var realtime=builder.AddProject<Projects.Bigpara_Service_Realtime>("bigpara-realtime-signalr");
builder.AddProject<Projects.Bigpara_Service_Hangfire>("bigpara-job-hangfire").WithReference(realtime);
builder.AddProject<Projects.Bigpara_Service_Api>("bigpara-service-api");
await builder.Build().RunAsync();
