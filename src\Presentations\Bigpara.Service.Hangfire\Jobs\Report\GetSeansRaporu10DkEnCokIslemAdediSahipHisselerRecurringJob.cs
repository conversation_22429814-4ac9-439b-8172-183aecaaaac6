﻿using Bigpara.Domain.Matriks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Report;

public class GetSeansRaporu10DkEnCokIslemAdediSahipHisselerRecurringJob : IRecurringJob
{
    private readonly ISeansRaporlariService _seansRaporlariService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<GetSeansRaporu10DkEnCokIslemAdediSahipHisselerRecurringJob> _logger;

    public GetSeansRaporu10DkEnCokIslemAdediSahipHisselerRecurringJob
    (
        ISeansRaporlariService seansRaporlariService,
        INotificationService notificationService,
        ILogger<GetSeansRaporu10DkEnCokIslemAdediSahipHisselerRecurringJob> logger
    )
    {
        _seansRaporlariService = seansRaporlariService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public string Name => "GetSeansRaporu10DkEnCokIslemAdediSahipHisselerRecurringJob";
     public IEnumerable<string> Crons => ["*/10 9-17 * * 1-5"];
    public async Task ExecuteAsync()
    {
        try
        {
            _logger.LogInformation("GetSeansRaporu10DkEnCokIslemAdediSahipHisselerRecurringJob - Başladı ");
            await ProcessGetSeansRaporu10DkEnCokIslemAdediSahipHisseler();
        }
        catch (Exception exception)
        {
            _logger.LogError(exception.Message);
        }
    }

    /// <summary>
    /// 15 dk gun ici top 3 En cok deger kazanan bist 30 hisseleri aliniyor
    /// </summary>
    public async Task ProcessGetSeansRaporu10DkEnCokIslemAdediSahipHisseler()
    {
        if (!_seansRaporlariService.IsActive())
            return;

        try
        {
            const string pattern = "En çok işlem adedine sahip ilk 3 hisse senedi {0}.";
            var data = await _seansRaporlariService.GetSeansRaporu10DkEnCokIslemAdediSahipHisseler();

            var semboller = string.Join(",", data.Select(r => r.ACIKLAMA).ToList());

            var result = string.Format(pattern, semboller);

            var seansRaporuYuzeysel = data.FirstOrDefault();
            if (seansRaporuYuzeysel == null) return;
            var seansRaporu = new SeansRaporu
            {
                HisseCumle = result,
                Yon = 0,
                HisseAdi = string.Empty,
                EklenmeTarihi = DateTime.Now
            };

            await _seansRaporlariService.CreateAndCacheSeansRaporu(seansRaporu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            // TODO : Check
            //EmailHelper.SendEmail("GetSeansRaporu10DkEnCokIslemAdediSahipHisseler", ex.Message);
            await _notificationService.NotifyErrorAsync($"GetSeansRaporu10DkEnCokIslemAdediSahipHisseler", ex.Message);
        }
    }
}
