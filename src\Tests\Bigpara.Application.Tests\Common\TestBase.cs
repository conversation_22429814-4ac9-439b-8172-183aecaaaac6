using AutoMapper;
using Bigpara.Application.Mappings;
using Microsoft.Extensions.Logging;
using Moq;

namespace Bigpara.Application.Tests.Common;

public abstract class TestBase
{
    protected readonly IMapper Mapper;
    protected readonly Mock<ILogger> MockLogger;

    protected TestBase()
    {
        var configuration = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<UserMappingProfile>();
            cfg.AddProfile<NewsMappingProfile>();
        });

        Mapper = configuration.CreateMapper();
        MockLogger = new Mock<ILogger>();
    }

    protected Mock<ILogger<T>> CreateMockLogger<T>()
    {
        return new Mock<ILogger<T>>();
    }
}

public class TestDbContextFactory
{
    public static TContext CreateInMemoryContext<TContext>() where TContext : DbContext
    {
        var options = new DbContextOptionsBuilder<TContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        return (TContext)Activator.CreateInstance(typeof(TContext), options);
    }
}
