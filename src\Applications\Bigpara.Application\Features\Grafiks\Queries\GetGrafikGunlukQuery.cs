﻿using AutoMapper;
using Bigpara.Application.Contracts.Repositories.Matriks.Grafiks;
using Bigpara.Application.Dtos;
using Bigpara.Application.Features.Grafiks.Queries.Models;
using MediatR;

namespace Bigpara.Application.Features.Grafiks.Queries;




#region Query
public class GetGrafikGunlukQuery : IRequest<GetGrafikQueryResponse>
{
    public List<string> Symbols { get; set; }
    public int PerCount { get; set; }
    public int BackDay { get; set; }
}
#endregion

#region Handlers

public class GetGrafikGunlukQueryHandler : IRequestHandler<GetGrafikGunlukQuery, GetGrafikQueryResponse>
{
    private readonly IMapper _mapper;
    private readonly IGrafikRepository _grafikGunlukRepository;
    public GetGrafikGunlukQueryHandler(IGrafikRepository grafikGunlukRepository,IMapper mapper)
    {
        _grafikGunlukRepository = grafikGunlukRepository;
        _mapper = mapper;
    }
    public async Task<GetGrafikQueryResponse> Handle(GetGrafikGunlukQuery request, CancellationToken cancellationToken)
    {
        var response = new GetGrafikQueryResponse();
        var data = await _grafikGunlukRepository.GetGrafikGunlukBySembolIds(string.Join(",", request.Symbols), request.PerCount, request.BackDay);

        foreach (var item in request.Symbols)
        {
            var last = data.OrderByDescending(p => p.TARIH).FirstOrDefault(p => p.SEMBOL == item);
            var grafikItem = new GrafikDataListDto();
            grafikItem.Current = _mapper.Map<GrafikDto>(last);
            grafikItem.Chart = data.Where(p => p.SEMBOL == item).Select(s => _mapper.Map<GrafikDto>(s)).ToList();
            response.Data.Add(grafikItem);
        }

        return response;
    }
}

#endregion



