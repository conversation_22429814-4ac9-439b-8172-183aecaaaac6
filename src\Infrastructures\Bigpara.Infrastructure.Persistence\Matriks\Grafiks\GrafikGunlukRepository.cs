﻿using Bigpara.Application.Contracts.Repositories.Matriks.Grafiks;
using Bigpara.Domain;
using Bigpara.Domain.Matriks;
using Microsoft.Data.SqlClient;
using System;
using System.Data.Common;
using System.Threading.Tasks;

namespace Bigpara.Persistence.Matriks.Grafiks;

public class GrafikRepository : IGrafikRepository
{
    private readonly IMatriksDbContext _matriksDbContext;
    public GrafikRepository(IMatriksDbContext matriksDbContext)
    {
        _matriksDbContext = matriksDbContext;
    }




    public Task<List<GrafikGunluk>> GetTopGrafikGunluks(int top)
    {
        throw new NotImplementedException();
    }

    public async Task<List<GrafikGunluk>> GetGrafikGunluksBySembolId(int sembolId, int top)
    {
        var parameters = new List<SqlParameter>();
        parameters.Add(new SqlParameter("sembolId", sembolId));
        parameters.Add(new SqlParameter("top", top));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<GrafikGunluk>("bp.pGetGrafikGunlukBySembolId", parameters.ToArray());

    }

    public async Task<List<Grafik5Dk>> GetGrafik5DkBySembolIds(string Ids, int perIdRecordCount)
    {
        var parameters = new List<SqlParameter>();
        parameters.Add(new SqlParameter("IdList", Ids));
        parameters.Add(new SqlParameter("RecordCountPerID", perIdRecordCount));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<Grafik5Dk>("[bp].[pGetGrafik5DkBySembolIds]", parameters.ToArray());
    }

    public async Task<List<GrafikGunluk>> GetGrafikGunlukBySembolIds(string Ids, int perIdRecordCount, int daysBack = 30)
    {
        var parameters = new List<SqlParameter>();
        parameters.Add(new SqlParameter("IdList", Ids));
        parameters.Add(new SqlParameter("DaysBack", daysBack));
        parameters.Add(new SqlParameter("RecordCountPerID", perIdRecordCount));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<GrafikGunluk>("[bp].[pGetGrafikGunlukBySembolIds]", parameters.ToArray());
    }
}
