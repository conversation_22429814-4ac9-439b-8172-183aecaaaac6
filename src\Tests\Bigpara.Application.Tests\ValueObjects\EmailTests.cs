using Bigpara.Domain.ValueObjects;
using Xunit;

namespace Bigpara.Application.Tests.ValueObjects;

public class EmailTests
{
    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    public void Create_With_Valid_Email_Should_Succeed(string validEmail)
    {
        // Act
        var email = Email.Create(validEmail);

        // Assert
        Assert.Equal(validEmail.ToLowerInvariant(), email.Value);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("   ")]
    public void Create_With_Empty_Email_Should_Throw_ArgumentException(string emptyEmail)
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => Email.Create(emptyEmail));
    }

    [Theory]
    [InlineData("invalid-email")]
    [InlineData("@domain.com")]
    [InlineData("user@")]
    [InlineData("user@domain")]
    [InlineData("user <EMAIL>")]
    public void Create_With_Invalid_Email_Should_Throw_ArgumentException(string invalidEmail)
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => Email.Create(invalidEmail));
    }

    [Fact]
    public void Two_Emails_With_Same_Value_Should_Be_Equal()
    {
        // Arrange
        var email1 = Email.Create("<EMAIL>");
        var email2 = Email.Create("<EMAIL>");

        // Act & Assert
        Assert.Equal(email1, email2);
        Assert.True(email1 == email2);
        Assert.False(email1 != email2);
    }

    [Fact]
    public void Two_Emails_With_Different_Values_Should_Not_Be_Equal()
    {
        // Arrange
        var email1 = Email.Create("<EMAIL>");
        var email2 = Email.Create("<EMAIL>");

        // Act & Assert
        Assert.NotEqual(email1, email2);
        Assert.False(email1 == email2);
        Assert.True(email1 != email2);
    }

    [Fact]
    public void ToString_Should_Return_Email_Value()
    {
        // Arrange
        var emailValue = "<EMAIL>";
        var email = Email.Create(emailValue);

        // Act
        var result = email.ToString();

        // Assert
        Assert.Equal(emailValue.ToLowerInvariant(), result);
    }

    [Fact]
    public void Implicit_Conversion_To_String_Should_Work()
    {
        // Arrange
        var emailValue = "<EMAIL>";
        var email = Email.Create(emailValue);

        // Act
        string result = email;

        // Assert
        Assert.Equal(emailValue.ToLowerInvariant(), result);
    }
}
