﻿using Bigpara.Application.Common;
using Bigpara.Application.Contracts.Repositories.Matriks.Sektor;
using Bigpara.Domain;
using System.Threading.Tasks;

namespace Bigpara.Persistence.Matriks.Sektor
{
    public class SektorRepository : ISektorRepository
    {
        private readonly IGenericRepository<Sektor<PERSON>,MatriksDbContext> _sectorRepository;

        public SektorRepository(IGenericRepository<Sektor<PERSON>, MatriksDbContext> sectorRepository)
        {
            _sectorRepository = sectorRepository;
        }

        public virtual async Task<List<Sektorler>> GetSektorList()
        {
            var sektorler = (await _sectorRepository.GetAllAsync())
                .Where(s => !string.IsNullOrEmpty(s.StrKod))
                .ToList();

            return sektorler;
        }
    }
}
