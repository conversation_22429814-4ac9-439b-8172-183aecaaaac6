﻿using Bigpara.Application.Contracts.Repositories.Matriks.Yuzeysels;
using Bigpara.Application.Features.Yuzeysels.ViewModels;
using MediatR;

namespace Bigpara.Application.Features.Yuzeysels.Queries
{
    #region Query
    public class GetYuzeyselPerformanceQuery : IRequest<GetYuzeyselPerformanceQueryResponse>
    {
        public string Symbols { get; set; }

    }
    #endregion
    public class GetYuzeyselPerformanceQueryHandler : IRequestHandler<GetYuzeyselPerformanceQuery, GetYuzeyselPerformanceQueryResponse>
    {
        private readonly IYuzeyselRepository _yuzeyselRepository;
        public GetYuzeyselPerformanceQueryHandler(IYuzeyselRepository yuzeyselRepository)
        {
            _yuzeyselRepository = yuzeyselRepository;
        }
        public async Task<GetYuzeyselPerformanceQueryResponse> Handle(GetYuzeyselPerformanceQuery request, CancellationToken cancellationToken)
        {
            var yuzeysels = await _yuzeyselRepository.GetPerformanceBySembols(request.Symbols);
            var data = yuzeysels.Select(s => new YuzeyselPerformanceDataModel()
            {
                Symbol = s.SEMBOL,
                Bid = Math.Round(Convert.ToDecimal(s.ALIS), 2),
                Ask = Math.Round(Convert.ToDecimal(s.SATIS), 2),

                DayClose = Math.Round(Convert.ToDecimal(s.KAPANIS), 2),
                DayChangePercent = Convert.ToDecimal(s.ONCEKIKAPANIS) == 0
                    ? 0
                    : Math.Round((Convert.ToDecimal(s.KAPANIS) - Convert.ToDecimal(s.ONCEKIKAPANIS)) / Convert.ToDecimal(s.ONCEKIKAPANIS) * 100, 2),

                WeekClose = Math.Round(Convert.ToDecimal(s.ONCEKIHAFTAKAPANIS), 2),
                WeekChangePercent = Convert.ToDecimal(s.ONCEKIHAFTAKAPANIS) == 0
                    ? 0
                    : Math.Round((Convert.ToDecimal(s.KAPANIS) - Convert.ToDecimal(s.ONCEKIHAFTAKAPANIS)) / Convert.ToDecimal(s.ONCEKIHAFTAKAPANIS) * 100, 2),

                MonthClose = Math.Round(Convert.ToDecimal(s.ONCEKIAYKAPANIS), 2),
                MonthChangePercent = Convert.ToDecimal(s.ONCEKIAYKAPANIS) == 0
                    ? 0
                    : Math.Round((Convert.ToDecimal(s.KAPANIS) - Convert.ToDecimal(s.ONCEKIAYKAPANIS)) / Convert.ToDecimal(s.ONCEKIAYKAPANIS) * 100, 2),

                YearClose = Math.Round(Convert.ToDecimal(s.ONCEKIYILKAPANIS), 2),
                YearChangePercent = Convert.ToDecimal(s.ONCEKIYILKAPANIS) == 0
                    ? 0
                    : Math.Round((Convert.ToDecimal(s.KAPANIS) - Convert.ToDecimal(s.ONCEKIYILKAPANIS)) / Convert.ToDecimal(s.ONCEKIYILKAPANIS) * 100, 2)
            }).ToList();

            var response = new GetYuzeyselPerformanceQueryResponse
            {
                Data = data
            };

            return response;
        }
    }
}
