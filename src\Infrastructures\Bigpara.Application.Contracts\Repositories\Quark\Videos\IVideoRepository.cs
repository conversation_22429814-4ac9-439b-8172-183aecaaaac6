﻿using Bigpara.Domain;
using Bigpara.Domain.Bigpara;

namespace Bigpara.Application.Contracts.Repositories.Quark.Videos;

public interface IVideoRepository
{
    Task<Video> GetVideoByOldId(string oldVideoId);
    Task<List<Video>> GetVideoListByCategoryIds(string categoryIds);

    Task<int> CreateOrUpdate(Video video);
    Task<VideoList> GetVideos(int pageNumber, int pageSize);
    Task<VideoList> GetVideosAll(int pageNumber, int pageSize);
    Task<List<Video>> GetTopNVideo(int topN);
    Task<List<Video>> GetTopNVideByCategoryIds(int topN, string categoryIds);
    Task<Video> GetVideoDetail(int id);
}
