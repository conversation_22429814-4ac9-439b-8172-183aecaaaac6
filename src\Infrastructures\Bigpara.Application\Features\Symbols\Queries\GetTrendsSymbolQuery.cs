﻿using Bigpara.Application.Contracts.Repositories.Matriks.Piyasalar;
using Bigpara.Application.Contracts.Repositories.Matriks.Sembol;
using Bigpara.Application.Dtos;
using Bigpara.Application.Features.Symbols.Queries.ViewModels;
using MediatR;

namespace Bigpara.Application.Features.Symbols.Queries;


#region Query
public class GetTrendsSymbolQuery : IRequest<GetSymbolSearchQueryResponse>
{
    public string Type { get; set; }
    public int  Size { get; set; }

}

#endregion

#region Handler
public class GetTrendsSymbolQueryHandler : IRequestHandler<GetTrendsSymbolQuery, GetSymbolSearchQueryResponse>
{
    private readonly ISembolRepository _sembollerService;
    private readonly IPiyasaRepository _piyasaRepository;
    public GetTrendsSymbolQueryHandler(ISembolRepository sembollerService, IPiyasaRepository piyasaRepository)
    {
        _sembollerService = sembollerService;
        _piyasaRepository = piyasaRepository;
    }

    public async Task<GetSymbolSearchQueryResponse> Handle(GetTrendsSymbolQuery request, CancellationToken cancellationToken)
    {
        var cachedIndexSymbols = await _sembollerService.GetSembollerList();
        var quearyableSymbols = cachedIndexSymbols.AsQueryable().Where(p=> string.IsNullOrEmpty(request.Type) ||  p.SecurityType==request.Type);

        if (request.Size > 0)
            quearyableSymbols = quearyableSymbols.Take(request.Size);

        // Paging
        var pagedSymbols = quearyableSymbols
            .OrderByDescending(p => p.SembolId) //TODO : Click Or Search Count Or Percent Desc
            .ToList();

        var sembols = new List<UserSymbolDto>();
        foreach (var x in pagedSymbols)
        {
            var piyasa = await _piyasaRepository.GetById(x.PiyasaId.Value);
            if (piyasa == null)
                continue;

            sembols.Add(new UserSymbolDto
            {
                Symbol = x.Sembol,
                Description = x.Aciklama,
                Icon = $"{x.SembolId}.png",
                Id = x.SembolId,
                Bazaar = piyasa?.PIYASAKODU,
                Exchange = x.PiyasaId != 3 ? "BIST" : "SPOT",
                Type = x.SecurityType,
            });
        }

        var response = new GetSymbolSearchQueryResponse
        {
            Data = sembols
        };

        return response;
    }
}

#endregion


