namespace Bigpara.Domain.Bigpara;

using System;

public partial class AuthorArticle
{
    public int Id { get; set; }
    public int AuthorId { get; set; }
    public string Title { get; set; }
    public string SpotTitle { get; set; }
    public string SeoTitle { get; set; }
    public string Body { get; set; }
    public int SourceId { get; set; }
    public bool IsSocialPosted { get; set; }
    public bool ShareWithHurriyetSocial { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public DateTime? CreatedDate { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public int ReadCount { get; set; }
    public bool IsActive { get; set; }
    public string RedirectUrl { get; set; }
    public string MetaKeywords { get; set; }
    public string MetaDescription { get; set; }

    public int DailyReadCount { get; set; }
    public int WeeklyReadCount { get; set; }
    public int TotalReadCount { get; set; }
}
