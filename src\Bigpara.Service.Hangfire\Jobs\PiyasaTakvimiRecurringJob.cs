﻿using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Infrastructure.Dtos.Matriks;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs;

public class PiyasaTakvimiRecurringJob : IRecurringJob
{
    private readonly IPiyasaTakvimiService _piyasaTakvimiService;
    private readonly IConfiguration _configuration;
    private readonly BigparaHttpClient _bigparaHttpClient;
    private readonly ILogger<PiyasaTakvimiRecurringJob> _logger;
    private readonly INotificationService _notificationService;

    public PiyasaTakvimiRecurringJob
    (
        IPiyasaTakvimiService piyasaTakvimiService,
        ILogger<PiyasaTakvimiRecurringJob> logger,
        IConfiguration configuration,
        BigparaHttpClient bigparaHttpClient,
        INotificationService notificationService
    )
    {
        _piyasaTakvimiService = piyasaTakvimiService;
        _configuration = configuration;
        _bigparaHttpClient = bigparaHttpClient;
        _logger = logger;
        _notificationService = notificationService;
    }

    public string Name => "PiyasaTakvimiRecurringJob";
    public IEnumerable<string> Crons => ["0 0 8 * * ?"];
    public async Task ExecuteAsync()
    {
        try
        {
           _logger.LogInformation("PiyasaTakvimiRecurringJob - Başladı ");
            await ProcessPiyasaTakvimi();
        }
        catch (Exception ex)
        {
            _logger.LogError($"Bigpara.PiyasaTakvimi - Ex: {ex.Message}");
            //TODO : Check
            //EmailHelper.SendEmail(String.Format("Bigpara.PiyasaTakvimi Url:{0}", url), ex.Message);
            await _notificationService.NotifyErrorAsync($"Bigpara.PiyasaTakvimi", ex.Message.ToString());
        }
    }

    public async Task ProcessPiyasaTakvimi()
    {
        string startDate = $"{DateTime.Today.ToString("yyyyMMdd")}001000";
        string endDate = $"{DateTime.Today.AddMonths(1).ToString("yyyyMMdd")}235900";

        var url = $"{_configuration["Foreks:CloudApi"]}/{_configuration["Foreks:Cloud:PiyasaTakvimi:Url"]}{startDate}/to/{endDate}/?lang=TR";

        try
        {
            var calendarData = await _bigparaHttpClient.FetchDataAsync<FinancialCalendar>(url);
            foreach (FinancialCalendar result in calendarData)
            {
                try
                {
                    string newsDate = $"{DateHelper.UnixTimeToLocalDateTime(result.UpdateDate).ToShortDateString()} {result.Hour.Trim().Substring(0, 2)}:{result.Hour.Trim().Substring(2)}:00";

                    await _piyasaTakvimiService.InsertOrUpdatePiyasaTakvimi(new Domain.Matriks.PiyasaTakvimi()
                    {
                        NewsId = result.NewsId,
                        Country = result.Country,
                        Indicator = result.Indicator,
                        Per = result.Per,
                        Priority = result.Priority.ToString(),
                        Actual = result.Actual,
                        Consensus = result.Consensus,
                        Previous = result.Previous,
                        NewsDate = Convert.ToDateTime(newsDate)
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Bigpara.PiyasaTakvimi newsId:{result.NewsId} - Exp:{ex.Message}");
                    //TODO : Check
                    //EmailHelper.SendEmail(String.Format("Bigpara.PiyasaTakvimi newsId:{0}", result.NewsId), ex.Message.ToString());
                    await _notificationService.NotifyErrorAsync($"Bigpara.PiyasaTakvimi newsId:{result.NewsId}", ex.Message.ToString());
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"Bigpara.PiyasaTakvimi Url:{url} - Exp:{ex.Message}");
            //TODO : Check
            //EmailHelper.SendEmail(String.Format("Bigpara.PiyasaTakvimi Url:{0}", url), ex.Message);
            await _notificationService.NotifyErrorAsync($"Bigpara.PiyasaTakvimi Url:{url}", ex.Message.ToString());
        }
    }
}
