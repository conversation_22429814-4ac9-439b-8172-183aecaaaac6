﻿namespace Bigpara.Domain.ExtendedTypes;

public class AuthorsArticleDetails
{

    public int AuthorId { get; set; }
    public int Id { get; set; }
    public string AuthorFirstName { get; set; }
    public string AuthorLastName { get; set; }
    public string AuthorTitle { get; set; }
    public string AuthorProfession { get; set; }
    public string AuthorPicture { get; set; }
    public string Picture { get; set; }
    public int AuthorArticleId { get; set; }
    public string ArticleTitle { get; set; }
    public int ArticleReadCount { get; set; }
    public int ArticleCommentCount { get; set; }
    public DateTime ArticleStartDate { get; set; }
    public string ArticleBody { get; set; }
    public string ArticleSpotTitle { get; set; }
    public string RedirectUrl { get; set; }

    public string AuthorsFullName
    {
        get { return string.Format("{0} {1}", this.AuthorFirstName, this.AuthorLastName); }
    }
}
