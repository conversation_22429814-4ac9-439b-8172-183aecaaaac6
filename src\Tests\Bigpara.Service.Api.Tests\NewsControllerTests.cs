using Bigpara.Application.Common;
using Bigpara.Application.Features.News.Commands;
using Bigpara.Application.Features.News.Queries;
using Bigpara.Application.Features.News.Queries.ViewModels;
using Bigpara.Service.Api.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace Bigpara.Service.Api.Tests.Controllers
{
    public class NewsControllerTests
    {
        private readonly Mock<ILogger<NewsController>> _loggerMock;
        private readonly Mock<IMediator> _mediatorMock;
        private readonly NewsController _controller;

        public NewsControllerTests()
        {
            _loggerMock = new Mock<ILogger<NewsController>>();
            _mediatorMock = new Mock<IMediator>();
            _controller = new NewsController(_loggerMock.Object, _mediatorMock.Object);
        }

        [Fact]
        public async Task Filter_ReturnsOkResult_WithExpectedData()
        {
            // Arrange
            var query = new GetNewsKeywordSearchQuery { Keyword = "test", Size = 10 };
            var expectedResult = new NewsSearchQueryViewModel();
            _mediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.Filter(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expectedResult, okResult.Value);
        }

        [Fact]
        public async Task Trends_ReturnsOkResult_WithExpectedData()
        {
            // Arrange
            var query = new GetNewsMostReadingQuery { Size = 5 };
            var expectedResult = new NewsMostReadingViewModel();
            _mediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.Trends(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expectedResult, okResult.Value);
        }

        [Fact]
        public async Task UpdateRead_ReturnsOkResult_WithExpectedData()
        {
            // Arrange
            var command = new AddNewsReadCommand { id = "123" };
            var expectedResult = new BaseResponse {  };
            _mediatorMock.Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.UpdateRead(command);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expectedResult, okResult.Value);
        }
    }
}
