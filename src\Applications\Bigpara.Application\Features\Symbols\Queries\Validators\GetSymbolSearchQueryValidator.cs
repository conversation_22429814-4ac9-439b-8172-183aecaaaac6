﻿using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Features.Symbols.Queries.Validators
{
    public class GetSymbolSearchQueryValidator:AbstractValidator<GetSymbolSearchQuery>
    {
        public GetSymbolSearchQueryValidator()
        {
            RuleFor(x => x.Keyword)
                .NotEmpty().WithMessage("Search text cannot be empty.")
                .MinimumLength(2).WithMessage("Search text must be at least 2 characters long.")
                .MaximumLength(10).WithMessage("Search text must not exceed 50 characters.");
        }
    }
}
