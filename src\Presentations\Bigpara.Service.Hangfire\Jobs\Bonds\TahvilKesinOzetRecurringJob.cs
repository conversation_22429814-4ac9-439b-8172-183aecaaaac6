﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Jobs.Bonds;

public class TahvilKesinOzetRecurringJob : IRecurringJob
{
    private readonly ForeksHttpClient _foreksHttpClient;
    private readonly ILogger<TahvilKesinOzetRecurringJob> _logger;
    private readonly IConfiguration _configuration;
    private readonly ITahvilService _tahvilService;
    private readonly INotificationService _notificationService;
    private readonly int _maxConcurrentTasks;

    public TahvilKesinOzetRecurringJob(
        ForeksHttpClient foreksHttpClient,
        ILogger<TahvilKesinOzetRecurringJob> logger,
        IConfiguration configuration,
        ITahvilService tahvilService,
        INotificationService notificationService)
    {
        _foreksHttpClient = foreksHttpClient;
        _logger = logger;
        _configuration = configuration;
        _tahvilService = tahvilService;
        _notificationService = notificationService;
        _maxConcurrentTasks = _configuration.GetValue("TahvilKesinOzetProcessing:MaxConcurrentTasks", 1);
    }

    public string Name => "TahvilKesinOzetRecurringJob";
     public IEnumerable<string> Crons => ["0 0 8 * * ?"];

    public async Task ExecuteAsync()
    {
        try
        {
            await ProcessDataTypeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{Name} sırasında kritik hata oluştu.");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    public async Task ProcessDataTypeAsync()
    {
        try
        {
            var url = $"{_configuration["Foreks:SnapShotApi"]}/?{_configuration["Foreks:SnapShot:TahvilKesinOzet:Url"]}";
            if (string.IsNullOrEmpty(url))
            {
                _logger.LogError("Foreks:SnapShot:TahvilKesinOzet:Url değeri bulunamadı.");
                await _notificationService.NotifyErrorAsync(Name, "Foreks:SnapShot:TahvilKesinOzet:Url değeri bulunamadı.");
                return;
            }

            var result = await _foreksHttpClient.FetchDataAsync<TahvilDto>(url);
            if (result == null || result.Count == 0)
            {
                _logger.LogWarning("Çekilen veri boş veya null.");
                return;
            }

            var liveResults = result?.Where(x => x.LiveStatus != null && x.LiveStatus == 1).ToList();

            if (liveResults == null || liveResults.Count == 0)
            {
                _logger.LogWarning("Çekilen veri boş veya null.");
                return;
            }

            using var semaphore = new SemaphoreSlim(_maxConcurrentTasks);

            foreach (var item in liveResults)
            {
                await semaphore.WaitAsync();

                try
                {
                    await ProcessSingleItemAsync(item);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"İşlem sırasında hata oluştu: {item.StrMat}");
                    await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.StrMat}, Hata: {ex} , Hata Mesajı: {ex.Message}");
                }
                finally
                {
                    semaphore.Release();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    private async Task ProcessSingleItemAsync(TahvilDto tahvilDto)
    {
        try
        {
            await _tahvilService.ChangeTahvilKesinOzet(tahvilDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {tahvilDto.StrMat}");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {tahvilDto.StrMat}, Hata: {ex} , Hata Mesajı: {ex.Message}");
        }
    }
}
