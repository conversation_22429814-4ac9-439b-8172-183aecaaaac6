﻿using Bigpara.Notifications;
using Hangfire.Client;
using Hangfire.Common;
using Hangfire.Server;
using Hangfire.States;
using Hangfire.Storage;

namespace Bigpara.Service.Hangfire.Filters;

public class LogEverythingAttribute : JobFilterAttribute, IClientFilter, IServerFilter, IElectStateFilter, IApplyStateFilter
{
    private readonly ILogger<LogEverythingAttribute> _logger;
    private readonly INotificationService _notificationService;

    public LogEverythingAttribute
    (
        ILogger<LogEverythingAttribute> logger,
        INotificationService notificationService
    )
    {
        _logger = logger;
        _notificationService = notificationService;
    }

    public void OnCreating(CreatingContext filterContext)
    {
        _logger.LogInformation($"Creating a job based on method `{filterContext.Job.Method.Name}`...");
    }

    public void OnCreated(CreatedContext filterContext)
    {
        _logger.LogInformation($"Job that is based on method `{filterContext.Job.Method.Name}` has been created with id `{filterContext.BackgroundJob?.Id}`");
    }

    public void OnPerforming(PerformingContext filterContext)
    {
        _logger.LogInformation($"Starting to perform job `{filterContext.BackgroundJob?.Id}`");
    }

    public void OnPerformed(PerformedContext filterContext)
    {
        _logger.LogInformation($"Job `{filterContext.BackgroundJob?.Id}` has been performed");
    }

    public void OnStateElection(ElectStateContext context)
    {
        if (context.CandidateState is FailedState failedState)
        {
            string message = $"Job `{context.BackgroundJob?.Id}` has failed due to exception `{failedState.Exception}` but will be retried automatically.";
            _logger.LogError(message);
            // TODO : check
            //EmailHelper.SendEmail(context.JobId, message);
            _notificationService.NotifyErrorAsync(context.BackgroundJob?.Id, message).GetAwaiter().GetResult(); ;
        }
    }

    public void OnStateApplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
    {
        _logger.LogInformation($"Job `{context.BackgroundJob?.Id}` state was changed from `{context.OldStateName}` to `{context.NewState.Name}`");

        if (context.NewState is FailedState failedState)
        {
            string message = $"Background job #{context.BackgroundJob?.Id} failed with exception: {failedState.Exception}";
            _logger.LogError(message);
            // TODO : check
            //EmailHelper.SendEmail(context.JobId, message);
            _notificationService.NotifyErrorAsync(context.BackgroundJob?.Id, message).GetAwaiter().GetResult(); ;
        }
    }

    public void OnStateUnapplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
    {
        _logger.LogInformation($"Job `{context.BackgroundJob?.Id}` state `{context.OldStateName}` was unapplied.");
    }
}