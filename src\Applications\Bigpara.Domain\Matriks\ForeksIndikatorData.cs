﻿using Newtonsoft.Json;

namespace Bigpara.Domain.Matriks;


public partial class ForeksIndikatorData
{
    [JsonProperty("r")]
    public Result Result { get; set; }

    [JsonProperty("d")]
    public long Date { get; set; }

    [JsonProperty("a")]
    public long Amount { get; set; }
}


public partial class ForeksData
{
    public static ForeksIndikatorData[] FromJson(string json) => JsonConvert.DeserializeObject<ForeksIndikatorData[]>(json);
}

public static class Serialize
{
    public static string ToJson(this ForeksIndikatorData[] self) => JsonConvert.SerializeObject(self);
}


public partial class Result
{
    [JsonProperty("SMA=per:5")]
    public IndikatorValue SMA5 { get; set; }

    [JsonProperty("SMA=per:9")]
    public IndikatorValue SMA9 { get; set; }

    [JsonProperty("SMA=per:10")]
    public IndikatorValue SMA10 { get; set; }

    [JsonProperty("SMA=per:20")]
    public IndikatorValue SMA20 { get; set; }

    [JsonProperty("SMA=per:50")]
    public IndikatorValue SMA50 { get; set; }

    [JsonProperty("SMA=per:100")]
    public IndikatorValue SMA100 { get; set; }

    [JsonProperty("SMA=per:250")]
    public IndikatorValue SMA250 { get; set; }

    [JsonProperty("BOL=per:20-devu:2-devd:2")]
    public IndikatorValue BOL { get; set; }

    [JsonProperty("CCI=per:10")]
    public IndikatorValue CCI10 { get; set; }

    [JsonProperty("CCI=per:14")]
    public IndikatorValue CCI14 { get; set; }

    [JsonProperty("CCI=per:20")]
    public IndikatorValue CCI20 { get; set; }

    [JsonProperty("MACD=fast:12-slow:26-sig:9")]
    public IndikatorValue MACD { get; set; }

    [JsonProperty("ADX=per:3")]
    public IndikatorValue ADX3 { get; set; }

    [JsonProperty("SAR=acc:0.002-max:0.2")]
    public IndikatorValue SAR { get; set; }

    [JsonProperty("MOM=per:12")]
    public IndikatorValue MOM { get; set; }

    [JsonProperty("RSI=per:9")]
    public IndikatorValue RSI9 { get; set; }

    [JsonProperty("RSI=per:10")]
    public IndikatorValue RSI10 { get; set; }

    [JsonProperty("RSI=per:14")]
    public IndikatorValue RSI14 { get; set; }

    [JsonProperty("STOF=fastk:5-fastd:3")]
    public IndikatorValue STOF { get; set; }

    [JsonProperty("STOS=fastk:10-slowk:3-slowd:6")]
    public IndikatorValue STOS_10_3_6 { get; set; }

    [JsonProperty("STOS=fastk:5-slowk:1-slowd:6")]
    public IndikatorValue STOS_5_1_6 { get; set; }

    [JsonProperty("STOS=fastk:5-slowk:3-slowd:6")]
    public IndikatorValue STOS_5_3_6 { get; set; }

    [JsonProperty("STOS=fastk:13-slowk:1-slowd:6")]
    public IndikatorValue STOS_13_1_6 { get; set; }

    [JsonProperty("STOS=fastk:21-slowk:1-slowd:6")]
    public IndikatorValue STOS_21_1_6 { get; set; }
}

public partial class IndikatorValue
{
    [JsonProperty("a")]
    public double A { get; set; }

    [JsonProperty("c")]
    public double C { get; set; }

    [JsonProperty("d")]
    public double D { get; set; }

    [JsonProperty("h")]
    public double H { get; set; }

    [JsonProperty("k")]
    public double K { get; set; }

    [JsonProperty("l")]
    public double L { get; set; }

    [JsonProperty("m")]
    public double M { get; set; }

    [JsonProperty("r")]
    public double R { get; set; }

    [JsonProperty("s")]
    public double S { get; set; }

    [JsonProperty("u")]
    public double U { get; set; }

}
