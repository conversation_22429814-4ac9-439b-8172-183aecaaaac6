﻿namespace Bigpara.Domain.Bitcoin;

public partial class MarketsOnline
{
    public string Symbol { get; set; }
    public System.DateTime DateTime { get; set; }
    public Nullable<double> HighValue { get; set; }
    public Nullable<double> LowValue { get; set; }
    public Nullable<double> CloseValue { get; set; }
    public Nullable<double> AvgValue { get; set; }
    public Nullable<double> BidValue { get; set; }
    public Nullable<double> AskValue { get; set; }
    public Nullable<double> VolumeValue { get; set; }
    public string Currency { get; set; }
    public Nullable<double> CurrencyVolume { get; set; }
    public Nullable<System.DateTime> UpdatedDateTime { get; set; }
}
