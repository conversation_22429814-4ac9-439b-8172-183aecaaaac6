﻿using Bigpara.Application.Common;
using Bigpara.Application.Dtos.Matriks;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Features.Symbols.Queries.Models
{
    public class UserSembolItem
    {
        public string Code { get; set; }
        public int Id { get; set; }
    }
    public class GetListFollowSymbolsQueryResponse : BaseResponse
    {
        public GetListFollowSymbolsQueryResponse()
        {
            Data = new List<UserSembolItem>();
        }
        public List<UserSembolItem> Data { get; set; }
    }
}
