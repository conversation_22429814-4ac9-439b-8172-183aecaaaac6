﻿using Bigpara.Service.Realtime.Hubs;
using Microsoft.AspNetCore.SignalR;
using StackExchange.Redis;

namespace Bigpara.Service.Realtime.Services
{
    public interface IRedisSubscriptionService
    {
        Task StartListening();
        Task StopListening();
    }

    public class RedisSubscriptionService : IRedisSubscriptionService
    {
        private readonly IConnectionMultiplexer _redis;
        private readonly IHubContext<TradeHub> _hubContext;
        private readonly ILogger<RedisSubscriptionService> _logger;
        private ISubscriber _subscriber;

        public RedisSubscriptionService(
            IConnectionMultiplexer redis,
            IHubContext<TradeHub> hubContext,
            ILogger<RedisSubscriptionService> logger)
        {
            _redis = redis;
            _hubContext = hubContext;
            _logger = logger;

            _subscriber = redis.GetSubscriber();
        }

        public async Task StartListening()
        {
            // Pattern-based subscription - daha performanslı
            await _subscriber.SubscribeAsync(
                new RedisChannel("Bigpara.Service.Realtime.Hubs.TradeHub:group:*",RedisChannel.PatternMode.Pattern),
                async (channel, message) =>
                {
                    try
                    {
                        await ProcessMessage(channel, message);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error processing message for channel {channel}");
                    }
                });

            _logger.LogInformation("Optimized Redis subscription service started");
        }

        private async Task ProcessMessage(string channel, string message)
        {
            var parts = channel.Split(':');
            if (parts.Length < 3) return;

            var dataType = parts[2]; // symbol_chart, symbol_detail, etc.
            var symbol = parts[3].ToUpper();

            switch (dataType)
            {
                case "symbol_chart":
                   
                    var groupName = $"symbol_chart_{symbol}";
                    await _hubContext.Clients.Group(groupName).SendAsync("ReceiveChart", symbol, message);
                    break;

                case "symbol_detail":

                    groupName = $"symbol_detail_{symbol}";
                    await _hubContext.Clients.Group(groupName).SendAsync("ReceiveDetail", symbol, message);
                    break;
            }

            await _hubContext.Clients.Group("symbol_table_all").SendAsync("ReceiveChart", symbol, message);
            //await _hubContext.Clients.Group($"symbol_table_{}_all").SendAsync("ReceiveChart", type, message);

        }

        public async Task StopListening()
        {
            await _subscriber.UnsubscribeAllAsync();
            _logger.LogInformation("Optimized Redis subscription service stopped");
        }

    }
}