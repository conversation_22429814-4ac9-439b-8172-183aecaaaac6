﻿using Bigpara.Notifications.Email;
using Bigpara.Notifications.Teams;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Bigpara.Notifications.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddNotifications(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<EmailConfiguration>(configuration.GetSection("EmailSettings"));
        services.AddSingleton<IEmailService, EmailService>();


        services.Configure<TeamsConfiguration>(configuration.GetSection("TeamsSettings"));
        services.AddHttpClient<ITeamsNotificationService, TeamsNotificationService>();

        services.AddSingleton<INotificationService, NotificationManager>();

        return services;
    }
}
