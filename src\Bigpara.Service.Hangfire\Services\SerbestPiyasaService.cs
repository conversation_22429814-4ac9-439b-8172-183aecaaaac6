﻿using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;
using Bigpara.Service.Hangfire.Infrastructure.Data;
using Bigpara.Service.Hangfire.Services.Interfaces;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients.Interfaces;
using Bigpara.Service.Hangfire.Infrastructure.Publishers;

namespace Bigpara.Service.Hangfire.Services;

public class SerbestPiyasaService : ISerbestPiyasaService
{
    private readonly IPublisher _publisher;
    private readonly ILogger<SerbestPiyasaService> _logger;
    private readonly IStoredProcedureParameterService _storedProcedureService;
    private readonly TimeZoneInfo _turkeyTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");

    public SerbestPiyasaService
    (
        IStoredProcedureParameterService storedProcedureService,
        ILogger<SerbestPiyasaService> logger,
        IPublisher publisher)
    {
        _storedProcedureService = storedProcedureService;
        _logger = logger;
        _publisher = publisher;
    }

    public async Task Change(YuzeyselDto yuzeyselDto)
    {
        yuzeyselDto.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                   DateTimeOffset.FromUnixTimeMilliseconds(yuzeyselDto.UnixTime).UtcDateTime,
                   _turkeyTimeZone);

        var grandBazaarParameters = _storedProcedureService.CreateGrandBazaarStoredProcedureParameters(yuzeyselDto);
        if (grandBazaarParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_Serbest", grandBazaarParameters, yuzeyselDto?.SEMBOL);

        var grandBazaarHistoricalParameters = _storedProcedureService.CreateGrandBazaarHistoricalStoredProcedureParameters(yuzeyselDto);
        if (grandBazaarHistoricalParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_SerbestTarihsel", grandBazaarHistoricalParameters, yuzeyselDto?.SEMBOL);
    }

    public async Task SendRealTimeApi(Dictionary<string, string> values)
    {
       await _publisher.PublishSymbolDataAsync(values["SEMBOL"], values);
    }
}
