﻿<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Realtime Trade Takips</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .buy { color: green; }
        .sell { color: red; }
    </style>
</head>
<body>
    @RenderBody()

    <script>
        const connection = new signalR.HubConnectionBuilder()
            .withUrl("@Html.Raw(Configuration["Bigpara:Socket"])/tradehub")
            .withAutomaticReconnect()
            .configureLogging(signalR.LogLevel.Debug)
            .build();

        connection.on("ReceiveChart", (symbol,tradeData) => {
            console.log("Trade verisi alındı:", symbol, tradeData);
            updateTradeRow(tradeData);
        });

        connection.on("ReceiveDetail", (tradeData) => {
            console.log("Trade detay verisi alındı:", tradeData);
            updateTradeRow(tradeData);
        });

        connection.on("ReceiveTable", (tradeData) => {
            console.log("Trade Table verisi alındı:", tradeData);
            updateTradeRow(tradeData);
        });

        connection.on("ReceiveTypeTable", (tradeData) => {
            console.log("Trade Type Table verisi alındı:", tradeData);
            updateTradeRow(tradeData);
        });

        async function start() {
            try {
                await connection.start().then(()=>{
                  connection.invoke("SubscribeAll");
                  connection.invoke("SubscribeToSymbol","USDTRY");
                  connection.invoke("SubscribeToSymbols",["USDTRY","EURUSD","JPYTRY"]);
                  connection.invoke("SubscribeToSymbolDetail","USDTRY");
                  
                });
                console.log("SignalR bağlantısı kuruldu.");
            } catch (err) {
                console.error("SignalR bağlantısı kurulamadı:", err);
                setTimeout(start, 5000);
            }
        }

        connection.onclose(async () => {
            await start();
        });

        function addTradeRow(trade) {
            trade = JSON.parse(trade); // JSON stringini nesneye çevir
            const tbody = document.getElementById("tradeBody");
            const row = document.createElement("tr");

            // ISO formatına çevir
            const date = new Date(parseInt(trade.dt));
            const formattedTime = date.toLocaleTimeString();
            const direction=trade.d==="-1.0"?"buy":"sell";
            row.innerHTML = `
                <td>${trade._i}</td>
                <td>${trade.a}</td>
                <td>${trade.b}</td>
                <td>${trade.c}</td>
                <td>${trade.C}</td>
                <td class="${direction}">${trade.d}</td>
                <td>${formattedTime}</td>
               
            `;

            // Tablonun en üstüne ekle
            if (tbody.firstChild) {
                tbody.insertBefore(row, tbody.firstChild);
            } else {
                tbody.appendChild(row);
            }

            // Sadece son 20 satırı göster
            while (tbody.children.length > 20) {
                tbody.removeChild(tbody.lastChild);
            }
        }
        function updateTradeRow(trade) {

            trade = JSON.parse(trade);
            const row = document.querySelector(`tr[data-id='${trade._i}']`);
            if (!row) return;

           

            const date = new Date(parseInt(trade.dt));
            const formattedTime = date.toLocaleTimeString();
            const direction = trade.d === "-1.0" ? "buy" : "sell";

            const idCell = row.querySelector("td[data-val='_i']");
            const aCell = row.querySelector("td[data-val='a']");
            const bCell = row.querySelector("td[data-val='b']");
            const cCell = row.querySelector("td[data-val='c']");
            const cUpperCell = row.querySelector("td[data-val='C']");
            const dCell = row.querySelector("td[data-val='d']");
            const dtCell = row.querySelector("td[data-val='dt']");

            if (idCell && trade._i) idCell.textContent = trade._i;
            if (aCell && trade.a) aCell.textContent = trade.a;
            if (bCell && trade.b) bCell.textContent = trade.b;
            if (cCell && trade.c) cCell.textContent = trade.c;
            if (cUpperCell && trade.C) cUpperCell.textContent = trade.C;
            if (dCell && trade.d) {
                dCell.textContent = trade.d;
                dCell.className = direction;
            }
            if (dtCell) dtCell.textContent = formattedTime;

            // Apply the effect
            row.style.transition = "background-color 0.1s ease";
            row.style.backgroundColor = direction === "buy" ? "green" : "red";
            setTimeout(() => {
                row.style.backgroundColor = "";
            }, 10);
        }


        // Bağlantıyı başlat
        start();
    </script>

</body>
</html>